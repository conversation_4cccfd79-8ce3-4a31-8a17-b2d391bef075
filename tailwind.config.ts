import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				// New gradient colors
				'gradient-start': '#1DEBF6',
				'gradient-end': '#83FFBC',
				// Heey brand colors - updated
				lime: {
					400: '#83FFBC',
					500: '#1DEBF6',
					600: '#00D4E8',
					700: '#00B8D4'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			backgroundImage: {
				'primary-gradient': 'linear-gradient(135deg, #1DEBF6 0%, #83FFBC 100%)',
				'lime-gradient': 'linear-gradient(135deg, #1DEBF6 0%, #83FFBC 100%)',
				'glass-gradient': 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
			},
			backdropBlur: {
				xs: '2px'
			},
			keyframes: {
				// Optimized marquee animations
				'marquee-left': {
					'0%': { transform: 'translate3d(100%, 0, 0)' },
					'100%': { transform: 'translate3d(-100%, 0, 0)' }
				},
				'marquee-right': {
					'0%': { transform: 'translate3d(-100%, 0, 0)' },
					'100%': { transform: 'translate3d(100%, 0, 0)' }
				},
				'fade-in': {
					'0%': { opacity: '0', transform: 'translateY(20px)' },
					'100%': { opacity: '1', transform: 'translateY(0)' }
				},
				'slide-up': {
					'0%': { opacity: '0', transform: 'translateY(100%)' },
					'100%': { opacity: '1', transform: 'translateY(0)' }
				},
                                'float': {
                                        '0%, 100%': { transform: 'translateY(0px)' },
                                        '50%': { transform: 'translateY(-10px)' }
                                },
                                'float-up-down': {
                                        '0%, 100%': { transform: 'translate3d(0, 0, 0)' },
                                        '50%': { transform: 'translate3d(0, -20px, 0)' }
                                },
                                'float-around': {
                                        '0%': { transform: 'translate3d(0, 0, 0)' },
                                        '25%': { transform: 'translate3d(10px, -10px, 0)' },
                                        '50%': { transform: 'translate3d(-10px, 10px, 0)' },
                                        '75%': { transform: 'translate3d(10px, 10px, 0)' },
                                        '100%': { transform: 'translate3d(0, 0, 0)' }
                                },
                                'pulse-slow': {
                                        '0%, 100%': { transform: 'scale(1)' },
                                        '50%': { transform: 'scale(1.05)' }
                                },
                                'spin-slow': {
                                        from: { transform: 'rotate(0deg)' },
                                        to: { transform: 'rotate(360deg)' }
                                }
                        },
                        animation: {
				// Optimized marquee animations
				'marquee-left': 'marquee-left linear infinite',
				'marquee-right': 'marquee-right linear infinite',
				'fade-in': 'fade-in 0.6s ease-out',
				'slide-up': 'slide-up 0.4s ease-out',
                                'float': 'float 3s ease-in-out infinite',
                                'float-up-down': 'float-up-down 8s ease-in-out infinite',
                                'float-around': 'float-around 10s ease-in-out infinite',
                                'pulse-slow': 'pulse-slow 6s ease-in-out infinite',
                                'spin-slow': 'spin-slow 20s linear infinite'
                        }
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
