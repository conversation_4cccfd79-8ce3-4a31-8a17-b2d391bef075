#!/bin/bash

# Heey Platform Production Deployment Script
set -e

echo "🚀 Starting Heey Platform deployment..."

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    print_warning ".env.production not found. Creating from example..."
    cp .env.example .env.production
    print_warning "Please update .env.production with your production values before deploying."
fi

# Validate environment variables
print_status "Validating environment configuration..."
if ! grep -q "VITE_SUPABASE_URL=" .env.production || ! grep -q "VITE_SUPABASE_ANON_KEY=" .env.production; then
    print_error "Missing required Supabase configuration in .env.production"
    exit 1
fi

# Install dependencies
print_status "Installing dependencies..."
npm ci

# Run tests
print_status "Running tests..."
if command -v npm run test &> /dev/null; then
    npm run test
else
    print_warning "No test script found, skipping tests"
fi

# Run linting
print_status "Running code quality checks..."
npm run lint

# Build the application
print_status "Building application for production..."
NODE_ENV=production npm run build

# Check build output
if [ ! -d "dist" ]; then
    print_error "Build failed - dist directory not found"
    exit 1
fi

print_success "Build completed successfully!"

# Bundle size analysis
print_status "Analyzing bundle size..."
if [ -f "dist/assets/index-*.js" ]; then
    JS_SIZE=$(du -h dist/assets/index-*.js | awk '{print $1}')
    print_status "Main JS bundle size: $JS_SIZE"
    
    # Warn if bundle is too large
    JS_SIZE_BYTES=$(du -b dist/assets/index-*.js | awk '{print $1}')
    if [ "$JS_SIZE_BYTES" -gt 2000000 ]; then # 2MB
        print_warning "Bundle size is large ($JS_SIZE). Consider code splitting."
    fi
fi

if [ -f "dist/assets/index-*.css" ]; then
    CSS_SIZE=$(du -h dist/assets/index-*.css | awk '{print $1}')
    print_status "Main CSS bundle size: $CSS_SIZE"
fi

# Security check for sensitive files
print_status "Checking for sensitive files in build..."
if find dist -name "*.env*" -o -name "*key*" -o -name "*secret*" | grep -q .; then
    print_error "Sensitive files found in build directory!"
    find dist -name "*.env*" -o -name "*key*" -o -name "*secret*"
    exit 1
fi

# Generate deployment manifest
print_status "Generating deployment manifest..."
cat > dist/deployment-info.json << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)",
  "version": "$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')",
  "branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')",
  "environment": "production",
  "buildCommand": "npm run build",
  "nodeVersion": "$(node --version)",
  "npmVersion": "$(npm --version)"
}
EOF

print_success "Deployment preparation completed!"
print_status "Build artifacts are ready in the 'dist' directory"

# Deployment instructions
echo ""
echo "📋 Next steps for deployment:"
echo "1. Upload the 'dist' directory contents to your web server"
echo "2. Configure your web server to serve index.html for all routes (SPA routing)"
echo "3. Set up HTTPS with SSL certificate"
echo "4. Configure Content Security Policy headers"
echo "5. Set up monitoring and logging"
echo "6. Test the deployment thoroughly"

# Platform-specific deployment hints
echo ""
echo "🌐 Platform-specific deployment commands:"
echo "• Netlify: netlify deploy --prod --dir=dist"
echo "• Vercel: vercel --prod"
echo "• AWS S3: aws s3 sync dist/ s3://your-bucket-name"
echo "• Firebase: firebase deploy"

print_success "Deployment script completed successfully! 🎉"