#!/bin/bash

# Health Check Script for Heey Platform
set -e

# Configuration
HEALTH_CHECK_URL="${1:-http://localhost:3000}"
MAX_RETRIES=30
RETRY_INTERVAL=2

# Color codes
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[HEALTH CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[HEALTHY]${NC} $1"
}

print_error() {
    echo -e "${RED}[UNHEALTHY]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to check if URL is responding
check_url() {
    local url=$1
    local description=$2
    
    if curl -f -s -o /dev/null "$url"; then
        print_success "$description is responding"
        return 0
    else
        print_error "$description is not responding"
        return 1
    fi
}

# Function to check if service is ready
wait_for_service() {
    local url=$1
    local retries=0
    
    print_status "Waiting for service to be ready at $url..."
    
    while [ $retries -lt $MAX_RETRIES ]; do
        if curl -f -s -o /dev/null "$url"; then
            print_success "Service is ready!"
            return 0
        fi
        
        retries=$((retries + 1))
        print_status "Attempt $retries/$MAX_RETRIES failed, retrying in $RETRY_INTERVAL seconds..."
        sleep $RETRY_INTERVAL
    done
    
    print_error "Service failed to become ready after $MAX_RETRIES attempts"
    return 1
}

# Main health checks
print_status "Starting health checks for Heey Platform..."

# Basic connectivity check
print_status "Checking basic connectivity..."
if ! wait_for_service "$HEALTH_CHECK_URL"; then
    exit 1
fi

# Check main application routes
print_status "Checking application routes..."
check_url "$HEALTH_CHECK_URL/" "Main application"
check_url "$HEALTH_CHECK_URL/dashboard" "Dashboard route"
check_url "$HEALTH_CHECK_URL/avatar" "Avatar route"

# Check static assets
print_status "Checking static assets..."
if curl -f -s -o /dev/null "$HEALTH_CHECK_URL/assets/" 2>/dev/null; then
    print_success "Static assets are accessible"
else
    print_warning "Static assets check failed (may be normal if no assets directory is exposed)"
fi

# Check for required JavaScript bundles
print_status "Checking JavaScript bundles..."
if curl -s "$HEALTH_CHECK_URL/" | grep -q "index.*\.js"; then
    print_success "JavaScript bundles are loaded"
else
    print_error "JavaScript bundles not found in main page"
fi

# Check for CSS bundles
print_status "Checking CSS bundles..."
if curl -s "$HEALTH_CHECK_URL/" | grep -q "index.*\.css"; then
    print_success "CSS bundles are loaded"
else
    print_warning "CSS bundles not found (may be inlined)"
fi

# Performance check - measure response time
print_status "Measuring response time..."
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' "$HEALTH_CHECK_URL/")
RESPONSE_TIME_MS=$(echo "$RESPONSE_TIME * 1000" | bc -l | cut -d. -f1)

if [ "$RESPONSE_TIME_MS" -lt 1000 ]; then
    print_success "Response time: ${RESPONSE_TIME_MS}ms (Good)"
elif [ "$RESPONSE_TIME_MS" -lt 3000 ]; then
    print_warning "Response time: ${RESPONSE_TIME_MS}ms (Acceptable)"
else
    print_error "Response time: ${RESPONSE_TIME_MS}ms (Slow)"
fi

# Check HTTP headers for security
print_status "Checking security headers..."
HEADERS=$(curl -I -s "$HEALTH_CHECK_URL/")

if echo "$HEADERS" | grep -qi "x-frame-options"; then
    print_success "X-Frame-Options header found"
else
    print_warning "X-Frame-Options header missing"
fi

if echo "$HEADERS" | grep -qi "x-content-type-options"; then
    print_success "X-Content-Type-Options header found"
else
    print_warning "X-Content-Type-Options header missing"
fi

if echo "$HEADERS" | grep -qi "strict-transport-security"; then
    print_success "HTTPS Strict Transport Security header found"
else
    print_warning "HSTS header missing (may be normal for HTTP)"
fi

# Final status
print_status "Health check completed!"
print_success "Heey Platform appears to be healthy and operational 🎉"

echo ""
echo "📊 Health Check Summary:"
echo "• URL: $HEALTH_CHECK_URL"
echo "• Response Time: ${RESPONSE_TIME_MS}ms"
echo "• Status: Healthy"
echo "• Timestamp: $(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"