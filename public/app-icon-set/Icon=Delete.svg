<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18 10H6C4.89543 10 4 9.10457 4 8V7.5151C4 6.61907 4.59595 5.83239 5.45851 5.58979L6.9915 5.15864C7.3598 5.05506 7.69066 4.84801 7.94483 4.56207L8.73665 3.67127C9.11618 3.2443 9.66019 3 10.2315 3H13.7685C14.3398 3 14.8838 3.2443 15.2634 3.67127L16.0552 4.56207C16.3093 4.84801 16.6402 5.05506 17.0085 5.15864L18.5415 5.58979C19.4041 5.83239 20 6.61907 20 7.5151V8C20 9.10457 19.1046 10 18 10Z" fill="url(#paint0_linear_38_4249)"/>
<foreignObject x="2.4126" y="4" width="19.1748" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4249_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4249)" data-figma-bg-blur-radius="3">
<path d="M6.47819 16.6082L5.70896 11.6082C5.37945 9.46639 5.21469 8.39548 5.81329 7.69774C6.41189 7 7.49541 7 9.66245 7H14.3376C16.5046 7 17.5881 7 18.1867 7.69774C18.7853 8.39548 18.6206 9.46639 18.291 11.6082L17.5218 16.6082C17.2728 18.2267 17.1483 19.0359 16.5864 19.5179C16.0245 20 15.2058 20 13.5683 20H10.4317C8.79421 20 7.97548 20 7.41358 19.5179C6.85167 19.0359 6.72718 18.2267 6.47819 16.6082Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M6.47819 16.6082L5.70896 11.6082C5.37945 9.46639 5.21469 8.39548 5.81329 7.69774C6.41189 7 7.49541 7 9.66245 7H14.3376C16.5046 7 17.5881 7 18.1867 7.69774C18.7853 8.39548 18.6206 9.46639 18.291 11.6082L17.5218 16.6082C17.2728 18.2267 17.1483 19.0359 16.5864 19.5179C16.0245 20 15.2058 20 13.5683 20H10.4317C8.79421 20 7.97548 20 7.41358 19.5179C6.85167 19.0359 6.72718 18.2267 6.47819 16.6082Z" fill="url(#paint1_linear_38_4249)" fill-opacity="0.2"/>
<path d="M9.66211 7.09961H14.3379C15.4241 7.09961 16.2281 7.10058 16.8359 7.18652C17.4409 7.2721 17.8336 7.44029 18.1104 7.7627C18.3871 8.08531 18.4947 8.49922 18.4873 9.11035C18.4798 9.72423 18.3576 10.5191 18.1924 11.5928L17.4229 16.5928C17.298 17.4045 17.2056 18.0045 17.0752 18.4619C16.9456 18.9166 16.7819 19.219 16.5215 19.4424C16.2611 19.6658 15.9369 19.7819 15.4678 19.8408C14.9959 19.9001 14.3894 19.9004 13.5684 19.9004H10.4316C9.61063 19.9004 9.00408 19.9001 8.53223 19.8408C8.06312 19.7819 7.73893 19.6658 7.47852 19.4424C7.21812 19.219 7.0544 18.9166 6.9248 18.4619C6.79444 18.0045 6.70204 17.4045 6.57715 16.5928L5.80762 11.5928C5.64243 10.5191 5.52016 9.72423 5.5127 9.11035C5.50528 8.49922 5.61287 8.08531 5.88965 7.7627C6.16641 7.44029 6.55911 7.2721 7.16406 7.18652C7.77191 7.10058 8.5759 7.09961 9.66211 7.09961Z" stroke="url(#paint2_linear_38_4249)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4249)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 9.5C12.2761 9.5 12.5 9.72386 12.5 10V17C12.5 17.2761 12.2761 17.5 12 17.5C11.7239 17.5 11.5 17.2761 11.5 17V10C11.5 9.72386 11.7239 9.5 12 9.5Z" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_4249)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.0708 9.50497C16.3441 9.54402 16.5341 9.79729 16.495 10.0707L15.495 17.0707C15.456 17.344 15.2027 17.534 14.9294 17.4949C14.656 17.4559 14.466 17.2026 14.5051 16.9292L15.5051 9.92923C15.5441 9.65586 15.7974 9.46591 16.0708 9.50497Z" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter3_i_38_4249)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.92923 9.50497C7.65586 9.54402 7.46591 9.79729 7.50496 10.0707L8.50496 17.0707C8.54401 17.344 8.79728 17.534 9.07065 17.4949C9.34401 17.4559 9.53396 17.2026 9.49491 16.9292L8.49491 9.92923C8.45586 9.65586 8.20259 9.46591 7.92923 9.50497Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4249" x="2.4126" y="4" width="19.1748" height="19" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4249"/>
</filter>
<clipPath id="bgblur_0_38_4249_clip_path" transform="translate(-2.4126 -4)"><path d="M6.47819 16.6082L5.70896 11.6082C5.37945 9.46639 5.21469 8.39548 5.81329 7.69774C6.41189 7 7.49541 7 9.66245 7H14.3376C16.5046 7 17.5881 7 18.1867 7.69774C18.7853 8.39548 18.6206 9.46639 18.291 11.6082L17.5218 16.6082C17.2728 18.2267 17.1483 19.0359 16.5864 19.5179C16.0245 20 15.2058 20 13.5683 20H10.4317C8.79421 20 7.97548 20 7.41358 19.5179C6.85167 19.0359 6.72718 18.2267 6.47819 16.6082Z"/>
</clipPath><filter id="filter1_i_38_4249" x="11.5" y="9.5" width="1" height="8.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4249"/>
</filter>
<filter id="filter2_i_38_4249" x="14.5" y="9.49988" width="2" height="8.50012" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4249"/>
</filter>
<filter id="filter3_i_38_4249" x="7.5" y="9.49988" width="2" height="8.50012" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4249"/>
</filter>
<linearGradient id="paint0_linear_38_4249" x1="4" y1="3" x2="9.01246" y2="14.457" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4249" x1="5" y1="7" x2="17.6403" y2="20.6126" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4249" x1="5.4375" y1="7.59091" x2="19" y2="7.59091" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
