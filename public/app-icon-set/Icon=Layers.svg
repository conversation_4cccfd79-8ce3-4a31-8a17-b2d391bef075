<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.4661 10.3886C11.4103 9.87048 12.5897 9.87048 13.5339 10.3886L20.5175 14.2207C21.1608 14.5737 21.1608 15.4263 20.5175 15.7793L13.5339 19.6114C12.5897 20.1295 11.4103 20.1295 10.4661 19.6114L3.48248 15.7793C2.83917 15.4263 2.83917 14.5737 3.48248 14.2207L10.4661 10.3886Z" fill="url(#paint0_linear_38_4045)"/>
<foreignObject x="0" y="4" width="24" height="16"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4045_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4045)" data-figma-bg-blur-radius="3">
<path d="M10.4661 7.38856C11.4103 6.87048 12.5897 6.87048 13.5339 7.38855L20.5175 11.2207C21.1608 11.5737 21.1608 12.4263 20.5175 12.7793L13.5339 16.6114C12.5897 17.1295 11.4103 17.1295 10.4661 16.6114L3.48248 12.7793C2.83917 12.4263 2.83917 11.5737 3.48248 11.2207L10.4661 7.38856Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M10.4661 7.38856C11.4103 6.87048 12.5897 6.87048 13.5339 7.38855L20.5175 11.2207C21.1608 11.5737 21.1608 12.4263 20.5175 12.7793L13.5339 16.6114C12.5897 17.1295 11.4103 17.1295 10.4661 16.6114L3.48248 12.7793C2.83917 12.4263 2.83917 11.5737 3.48248 11.2207L10.4661 7.38856Z" fill="url(#paint1_linear_38_4045)" fill-opacity="0.2"/>
<path d="M10.5146 7.47656C11.4287 6.97507 12.5713 6.97507 13.4854 7.47656L20.4697 11.3086C21.0434 11.6237 21.0434 12.3763 20.4697 12.6914L13.4854 16.5234C12.5713 17.0249 11.4287 17.0249 10.5146 16.5234L3.53027 12.6914C2.95663 12.3763 2.95662 11.6237 3.53027 11.3086L10.5146 7.47656Z" stroke="url(#paint2_linear_38_4045)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<foreignObject x="0" y="1" width="24" height="16"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_1_38_4045_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_i_38_4045)" data-figma-bg-blur-radius="3">
<path d="M10.4661 4.38856C11.4103 3.87048 12.5897 3.87048 13.5339 4.38855L20.5175 8.22066C21.1608 8.57367 21.1608 9.42633 20.5175 9.77934L13.5339 13.6114C12.5897 14.1295 11.4103 14.1295 10.4661 13.6114L3.48248 9.77934C2.83917 9.42633 2.83917 8.57367 3.48248 8.22066L10.4661 4.38856Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M10.4661 4.38856C11.4103 3.87048 12.5897 3.87048 13.5339 4.38855L20.5175 8.22066C21.1608 8.57367 21.1608 9.42633 20.5175 9.77934L13.5339 13.6114C12.5897 14.1295 11.4103 14.1295 10.4661 13.6114L3.48248 9.77934C2.83917 9.42633 2.83917 8.57367 3.48248 8.22066L10.4661 4.38856Z" fill="url(#paint3_linear_38_4045)" fill-opacity="0.2"/>
<path d="M10.5146 4.47656C11.4287 3.97507 12.5713 3.97507 13.4854 4.47656L20.4697 8.30859C21.0434 8.62368 21.0434 9.37632 20.4697 9.69141L13.4854 13.5234C12.5713 14.0249 11.4287 14.0249 10.5146 13.5234L3.53027 9.69141C2.95663 9.37632 2.95662 8.62368 3.53027 8.30859L10.5146 4.47656Z" stroke="url(#paint4_linear_38_4045)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<defs>
<filter id="filter0_i_38_4045" x="0" y="4" width="24" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4045"/>
</filter>
<clipPath id="bgblur_0_38_4045_clip_path" transform="translate(0 -4)"><path d="M10.4661 7.38856C11.4103 6.87048 12.5897 6.87048 13.5339 7.38855L20.5175 11.2207C21.1608 11.5737 21.1608 12.4263 20.5175 12.7793L13.5339 16.6114C12.5897 17.1295 11.4103 17.1295 10.4661 16.6114L3.48248 12.7793C2.83917 12.4263 2.83917 11.5737 3.48248 11.2207L10.4661 7.38856Z"/>
</clipPath><filter id="filter1_i_38_4045" x="0" y="1" width="24" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4045"/>
</filter>
<clipPath id="bgblur_1_38_4045_clip_path" transform="translate(0 -1)"><path d="M10.4661 4.38856C11.4103 3.87048 12.5897 3.87048 13.5339 4.38855L20.5175 8.22066C21.1608 8.57367 21.1608 9.42633 20.5175 9.77934L13.5339 13.6114C12.5897 14.1295 11.4103 14.1295 10.4661 13.6114L3.48248 9.77934C2.83917 9.42633 2.83917 8.57367 3.48248 8.22066L10.4661 4.38856Z"/>
</clipPath><linearGradient id="paint0_linear_38_4045" x1="3" y1="10" x2="11.2783" y2="24.9009" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4045" x1="3" y1="7" x2="11.2783" y2="21.9009" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4045" x1="3.5625" y1="7.45455" x2="21" y2="7.45455" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
<linearGradient id="paint3_linear_38_4045" x1="3" y1="4" x2="11.2783" y2="18.9009" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint4_linear_38_4045" x1="3.5625" y1="4.45455" x2="21" y2="4.45455" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
