<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="17" cy="8" r="4" fill="url(#paint0_linear_38_4006)"/>
<circle cx="8" cy="11" r="1" fill="url(#paint1_linear_38_4006)"/>
<circle cx="12" cy="11" r="1" fill="url(#paint2_linear_38_4006)"/>
<circle cx="16" cy="11" r="1" fill="url(#paint3_linear_38_4006)"/>
<foreignObject x="1" y="2" width="22" height="20.6177"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4006_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4006)" data-figma-bg-blur-radius="3">
<path d="M8 5C5.79086 5 4 6.79086 4 9V13C4 15.2091 5.79086 17 8 17H11.723C11.9043 17 12.0821 17.0493 12.2375 17.1425L16.2428 19.5457C16.576 19.7456 17 19.5056 17 19.1169V17.2616C17 17.0334 17.1558 16.8373 17.3702 16.7592C18.9045 16.1998 20 14.7278 20 13V9C20 6.79086 18.2091 5 16 5H8Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M8 5C5.79086 5 4 6.79086 4 9V13C4 15.2091 5.79086 17 8 17H11.723C11.9043 17 12.0821 17.0493 12.2375 17.1425L16.2428 19.5457C16.576 19.7456 17 19.5056 17 19.1169V17.2616C17 17.0334 17.1558 16.8373 17.3702 16.7592C18.9045 16.1998 20 14.7278 20 13V9C20 6.79086 18.2091 5 16 5H8Z" fill="url(#paint4_linear_38_4006)" fill-opacity="0.2"/>
<path d="M8 5.09961H16C18.1539 5.09961 19.9004 6.84609 19.9004 9V13C19.9004 14.6844 18.832 16.1196 17.3359 16.665C17.0876 16.7556 16.9004 16.986 16.9004 17.2617V19.1172C16.9002 19.4279 16.5605 19.6199 16.2939 19.46L12.2891 17.0566C12.1181 16.9541 11.922 16.9004 11.7227 16.9004H8C5.84609 16.9004 4.09961 15.1539 4.09961 13V9C4.09961 6.84609 5.84609 5.09961 8 5.09961Z" stroke="url(#paint5_linear_38_4006)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4006)">
<circle cx="8" cy="11" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_4006)">
<circle cx="12" cy="11" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter3_i_38_4006)">
<circle cx="16" cy="11" r="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4006" x="1" y="2" width="22" height="20.6177" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4006"/>
</filter>
<clipPath id="bgblur_0_38_4006_clip_path" transform="translate(-1 -2)"><path d="M8 5C5.79086 5 4 6.79086 4 9V13C4 15.2091 5.79086 17 8 17H11.723C11.9043 17 12.0821 17.0493 12.2375 17.1425L16.2428 19.5457C16.576 19.7456 17 19.5056 17 19.1169V17.2616C17 17.0334 17.1558 16.8373 17.3702 16.7592C18.9045 16.1998 20 14.7278 20 13V9C20 6.79086 18.2091 5 16 5H8Z"/>
</clipPath><filter id="filter1_i_38_4006" x="7" y="10" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4006"/>
</filter>
<filter id="filter2_i_38_4006" x="11" y="10" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4006"/>
</filter>
<filter id="filter3_i_38_4006" x="15" y="10" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4006"/>
</filter>
<linearGradient id="paint0_linear_38_4006" x1="13" y1="4" x2="20.8" y2="11.8" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4006" x1="7" y1="10" x2="8.95" y2="11.95" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4006" x1="11" y1="10" x2="12.95" y2="11.95" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint3_linear_38_4006" x1="15" y1="10" x2="16.95" y2="11.95" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint4_linear_38_4006" x1="4" y1="5" x2="18.5946" y2="20.5676" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint5_linear_38_4006" x1="4.5" y1="5.68182" x2="20" y2="5.68182" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
