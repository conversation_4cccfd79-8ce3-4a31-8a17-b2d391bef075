<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="7" y="19" width="10" height="3" rx="1" fill="url(#paint0_linear_38_4368)"/>
<foreignObject x="3" y="-1" width="18" height="25"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4368_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4368)" data-figma-bg-blur-radius="3">
<rect x="6" y="2" width="12" height="19" rx="2" fill="#1D1D1D" fill-opacity="0.05"/>
<rect x="6" y="2" width="12" height="19" rx="2" fill="url(#paint1_linear_38_4368)" fill-opacity="0.2"/>
<rect x="6.1" y="2.1" width="11.8" height="18.8" rx="1.9" stroke="url(#paint2_linear_38_4368)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4368)">
<circle cx="12" cy="19" r="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4368" x="3" y="-1" width="18" height="25" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4368"/>
</filter>
<clipPath id="bgblur_0_38_4368_clip_path" transform="translate(-3 1)"><rect x="6" y="2" width="12" height="19" rx="2"/>
</clipPath><filter id="filter1_i_38_4368" x="11" y="18" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4368"/>
</filter>
<linearGradient id="paint0_linear_38_4368" x1="7" y1="19" x2="8.61009" y2="24.367" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4368" x1="6" y1="2" x2="22.7275" y2="12.5648" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4368" x1="6.375" y1="2.86364" x2="18" y2="2.86364" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
