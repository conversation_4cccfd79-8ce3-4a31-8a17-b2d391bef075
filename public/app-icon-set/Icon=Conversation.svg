<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16 7C16 5.89543 15.1046 5 14 5H5C3.89543 5 3 5.89543 3 7V15.382C3 16.1253 3.78231 16.6088 4.44721 16.2764L7 15H14C15.1046 15 16 14.1046 16 13V7Z" fill="url(#paint0_linear_38_4185)"/>
<foreignObject x="5" y="5" width="19" height="17.3833"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4185_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4185)" data-figma-bg-blur-radius="3">
<path d="M8 10C8 8.89543 8.89543 8 10 8H19C20.1046 8 21 8.89543 21 10V18.382C21 19.1253 20.2177 19.6088 19.5528 19.2764L17 18H10C8.89543 18 8 17.1046 8 16V10Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M8 10C8 8.89543 8.89543 8 10 8H19C20.1046 8 21 8.89543 21 10V18.382C21 19.1253 20.2177 19.6088 19.5528 19.2764L17 18H10C8.89543 18 8 17.1046 8 16V10Z" fill="url(#paint1_linear_38_4185)" fill-opacity="0.2"/>
<path d="M10 8.09961H19C20.0493 8.09961 20.9004 8.95066 20.9004 10V18.3818C20.9004 19.0509 20.1961 19.4857 19.5977 19.1865L17.0449 17.9102L17.0234 17.9004H10C8.95066 17.9004 8.09961 17.0493 8.09961 16V10C8.09961 8.95066 8.95066 8.09961 10 8.09961Z" stroke="url(#paint2_linear_38_4185)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4185)">
<rect x="10" y="10" width="9" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_4185)">
<rect x="10" y="14" width="9" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4185" x="5" y="5" width="19" height="17.3833" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4185"/>
</filter>
<clipPath id="bgblur_0_38_4185_clip_path" transform="translate(-5 -5)"><path d="M8 10C8 8.89543 8.89543 8 10 8H19C20.1046 8 21 8.89543 21 10V18.382C21 19.1253 20.2177 19.6088 19.5528 19.2764L17 18H10C8.89543 18 8 17.1046 8 16V10Z"/>
</clipPath><filter id="filter1_i_38_4185" x="10" y="10" width="9" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4185"/>
</filter>
<filter id="filter2_i_38_4185" x="10" y="14" width="9" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4185"/>
</filter>
<linearGradient id="paint0_linear_38_4185" x1="16" y1="5" x2="4.33738" y2="17.6345" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4185" x1="8" y1="8" x2="19.6626" y2="20.6345" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4185" x1="8.40625" y1="8.54545" x2="21" y2="8.54545" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
