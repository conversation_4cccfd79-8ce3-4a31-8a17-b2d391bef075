<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.0002 10C12.3633 10 12.698 10.1969 12.8743 10.5144L17.8743 19.5144C18.1426 19.9971 17.9686 20.6059 17.4858 20.8742C17.003 21.1424 16.3942 20.9684 16.126 20.4856L12.0002 13.0591L7.87434 20.4856C7.60613 20.9684 6.99732 21.1424 6.51454 20.8742C6.03176 20.6059 5.85781 19.9971 6.12602 19.5144L11.126 10.5144C11.3024 10.1969 11.637 10 12.0002 10Z" fill="url(#paint0_linear_38_4436)"/>
<foreignObject x="0" y="0" width="24" height="20"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4436_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4436)" data-figma-bg-blur-radius="3">
<rect x="3" y="3" width="18" height="14" rx="2" fill="#1D1D1D" fill-opacity="0.05"/>
<rect x="3" y="3" width="18" height="14" rx="2" fill="url(#paint1_linear_38_4436)" fill-opacity="0.2"/>
<rect x="3.1" y="3.1" width="17.8" height="13.8" rx="1.9" stroke="url(#paint2_linear_38_4436)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4436)">
<rect x="4" y="14" width="16" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4436" x="0" y="0" width="24" height="20" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4436"/>
</filter>
<clipPath id="bgblur_0_38_4436_clip_path" transform="translate(0 0)"><rect x="3" y="3" width="18" height="14" rx="2"/>
</clipPath><filter id="filter1_i_38_4436" x="4" y="14" width="16" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4436"/>
</filter>
<linearGradient id="paint0_linear_38_4436" x1="6" y1="10" x2="16.6847" y2="21.6562" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4436" x1="3" y1="3" x2="16.23" y2="20.01" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4436" x1="3.5625" y1="3.63636" x2="21" y2="3.63636" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
