<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="14.5" cy="9.5" r="7.5" fill="url(#paint0_linear_38_4321)"/>
<foreignObject x="1" y="1" width="22" height="22"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4321_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4321)" data-figma-bg-blur-radius="3">
<rect x="4" y="4" width="16" height="16" rx="3" fill="#1D1D1D" fill-opacity="0.05"/>
<rect x="4" y="4" width="16" height="16" rx="3" fill="url(#paint1_linear_38_4321)" fill-opacity="0.2"/>
<rect x="4.1" y="4.1" width="15.8" height="15.8" rx="2.9" stroke="url(#paint2_linear_38_4321)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4321)">
<path d="M9.87849 8.46452C9.48797 8.074 8.8548 8.074 8.46428 8.46452C8.07376 8.85505 8.07376 9.48821 8.46428 9.87874L10.5856 12.0001L8.46428 14.1214C8.07376 14.5119 8.07376 15.1451 8.46428 15.5356C8.8548 15.9261 9.48797 15.9261 9.87849 15.5356L11.9998 13.4143L14.1211 15.5356C14.5117 15.9261 15.1448 15.9261 15.5353 15.5356C15.9259 15.1451 15.9259 14.5119 15.5353 14.1214L13.414 12.0001L15.5353 9.87874C15.9259 9.48821 15.9259 8.85505 15.5353 8.46452C15.1448 8.074 14.5117 8.074 14.1211 8.46452L11.9998 10.5858L9.87849 8.46452Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4321" x="1" y="1" width="22" height="22" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4321"/>
</filter>
<clipPath id="bgblur_0_38_4321_clip_path" transform="translate(-1 -1)"><rect x="4" y="4" width="16" height="16" rx="3"/>
</clipPath><filter id="filter1_i_38_4321" x="8.17139" y="8.17163" width="7.65674" height="8.15686" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4321"/>
</filter>
<linearGradient id="paint0_linear_38_4321" x1="7" y1="2" x2="21.625" y2="16.625" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4321" x1="4" y1="4" x2="19.6" y2="19.6" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4321" x1="4.5" y1="4.72727" x2="20" y2="4.72727" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
