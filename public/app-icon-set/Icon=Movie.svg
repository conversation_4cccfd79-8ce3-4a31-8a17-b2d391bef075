<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="14.5" cy="8.5" r="5.5" fill="url(#paint0_linear_38_4213)"/>
<foreignObject x="-1" y="2" width="26" height="20"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4213_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4213)" data-figma-bg-blur-radius="3">
<rect x="2" y="5" width="20" height="14" rx="2" fill="#1D1D1D" fill-opacity="0.05"/>
<rect x="2" y="5" width="20" height="14" rx="2" fill="url(#paint1_linear_38_4213)" fill-opacity="0.2"/>
<rect x="2.1" y="5.1" width="19.8" height="13.8" rx="1.9" stroke="url(#paint2_linear_38_4213)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4213)">
<rect x="4" y="15" width="3" height="2" rx="0.5" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_4213)">
<rect x="17" y="15" width="3" height="2" rx="0.5" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter3_i_38_4213)">
<rect x="4" y="11" width="3" height="2" rx="0.5" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter4_i_38_4213)">
<rect x="17" y="11" width="3" height="2" rx="0.5" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter5_i_38_4213)">
<rect x="4" y="7" width="3" height="2" rx="0.5" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter6_i_38_4213)">
<rect x="17" y="7" width="3" height="2" rx="0.5" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter7_i_38_4213)">
<path d="M14.2854 11.5713L10.7572 9.45435C10.424 9.25439 10 9.49445 10 9.8831V14.1169C10 14.5056 10.424 14.7456 10.7572 14.5457L14.2854 12.4287C14.6091 12.2345 14.6091 11.7655 14.2854 11.5713Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4213" x="-1" y="2" width="26" height="20" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4213"/>
</filter>
<clipPath id="bgblur_0_38_4213_clip_path" transform="translate(1 -2)"><rect x="2" y="5" width="20" height="14" rx="2"/>
</clipPath><filter id="filter1_i_38_4213" x="4" y="15" width="3" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4213"/>
</filter>
<filter id="filter2_i_38_4213" x="17" y="15" width="3" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4213"/>
</filter>
<filter id="filter3_i_38_4213" x="4" y="11" width="3" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4213"/>
</filter>
<filter id="filter4_i_38_4213" x="17" y="11" width="3" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4213"/>
</filter>
<filter id="filter5_i_38_4213" x="4" y="7" width="3" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4213"/>
</filter>
<filter id="filter6_i_38_4213" x="17" y="7" width="3" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4213"/>
</filter>
<filter id="filter7_i_38_4213" x="10" y="9.38232" width="4.52832" height="5.73535" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4213"/>
</filter>
<linearGradient id="paint0_linear_38_4213" x1="9" y1="3" x2="19.725" y2="13.725" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4213" x1="2" y1="5" x2="14.8255" y2="23.3221" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4213" x1="2.625" y1="5.63636" x2="22" y2="5.63636" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
