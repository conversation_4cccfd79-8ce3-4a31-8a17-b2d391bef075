<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="12" cy="14" r="4" fill="url(#paint0_linear_38_4376)"/>
<foreignObject x="-1" y="2" width="26" height="20"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4376_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4376)" data-figma-bg-blur-radius="3">
<path d="M8.70406 5.4565C8.13998 5.21719 7.62803 5 6.6189 5C2.56099 5 -0.151939 19 4.27766 19C5.9314 19 7.02161 17.9326 8.11666 16.8605C9.22145 15.7788 10.3312 14.6923 12.0294 14.6923C13.6799 14.6923 14.7865 15.7665 15.8946 16.842C17.0056 17.9203 18.118 19 19.7812 19C24.2108 19 21.2837 5 17.3176 5C16.0692 5 15.5576 5.24006 15.0175 5.4935C14.4136 5.77685 13.7741 6.07692 12.0294 6.07692C10.1665 6.07692 9.39605 5.75007 8.70406 5.4565Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M8.70406 5.4565C8.13998 5.21719 7.62803 5 6.6189 5C2.56099 5 -0.151939 19 4.27766 19C5.9314 19 7.02161 17.9326 8.11666 16.8605C9.22145 15.7788 10.3312 14.6923 12.0294 14.6923C13.6799 14.6923 14.7865 15.7665 15.8946 16.842C17.0056 17.9203 18.118 19 19.7812 19C24.2108 19 21.2837 5 17.3176 5C16.0692 5 15.5576 5.24006 15.0175 5.4935C14.4136 5.77685 13.7741 6.07692 12.0294 6.07692C10.1665 6.07692 9.39605 5.75007 8.70406 5.4565Z" fill="url(#paint1_linear_38_4376)" fill-opacity="0.2"/>
<path d="M17.3174 5.09961C17.7822 5.09961 18.2396 5.30502 18.6787 5.67773C19.1181 6.05069 19.5342 6.58719 19.9121 7.2373C20.668 8.53765 21.2642 10.2767 21.5996 12.0186C21.9352 13.7615 22.0071 15.4969 21.7246 16.791C21.5834 17.4378 21.3558 17.9666 21.0352 18.332C20.7171 18.6943 20.3048 18.9004 19.7812 18.9004C18.1648 18.9004 17.0802 17.854 15.9639 16.7705C14.8612 15.7003 13.7265 14.5928 12.0293 14.5928C10.2843 14.5928 9.1467 15.7123 8.04688 16.7891C6.94664 17.8663 5.88413 18.9004 4.27734 18.9004C3.75391 18.9003 3.33849 18.6947 3.01465 18.332C2.68809 17.9663 2.45222 17.437 2.30176 16.79C2.00083 15.4958 2.04967 13.7604 2.36426 12.0176C2.67869 10.2758 3.2563 8.53733 4.00488 7.2373C4.37921 6.58725 4.79423 6.05061 5.23633 5.67773C5.67799 5.30532 6.14233 5.09961 6.61914 5.09961C7.6084 5.09964 8.10582 5.31158 8.66504 5.54883C9.01148 5.6958 9.38421 5.8541 9.90527 5.97461C10.426 6.09503 11.0921 6.17675 12.0293 6.17676C12.9071 6.17676 13.5115 6.10106 13.9746 5.98828C14.4383 5.87534 14.7571 5.72593 15.0596 5.58398C15.3296 5.45728 15.5853 5.33717 15.9268 5.24805C16.2682 5.15896 16.6998 5.09962 17.3174 5.09961Z" stroke="url(#paint2_linear_38_4376)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4376)">
<circle cx="17" cy="9" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_4376)">
<circle cx="19" cy="11" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter3_i_38_4376)">
<circle cx="17" cy="13" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter4_i_38_4376)">
<circle cx="15" cy="11" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter5_i_38_4376)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 8C6.44772 8 6 8.44772 6 9V10H5C4.44772 10 4 10.4477 4 11C4 11.5523 4.44772 12 5 12H6V13C6 13.5523 6.44772 14 7 14C7.55228 14 8 13.5523 8 13V12H9C9.55228 12 10 11.5523 10 11C10 10.4477 9.55228 10 9 10H8V9C8 8.44772 7.55228 8 7 8Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4376" x="-1" y="2" width="26" height="20" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4376"/>
</filter>
<clipPath id="bgblur_0_38_4376_clip_path" transform="translate(1 -2)"><path d="M8.70406 5.4565C8.13998 5.21719 7.62803 5 6.6189 5C2.56099 5 -0.151939 19 4.27766 19C5.9314 19 7.02161 17.9326 8.11666 16.8605C9.22145 15.7788 10.3312 14.6923 12.0294 14.6923C13.6799 14.6923 14.7865 15.7665 15.8946 16.842C17.0056 17.9203 18.118 19 19.7812 19C24.2108 19 21.2837 5 17.3176 5C16.0692 5 15.5576 5.24006 15.0175 5.4935C14.4136 5.77685 13.7741 6.07692 12.0294 6.07692C10.1665 6.07692 9.39605 5.75007 8.70406 5.4565Z"/>
</clipPath><filter id="filter1_i_38_4376" x="16" y="8" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4376"/>
</filter>
<filter id="filter2_i_38_4376" x="18" y="10" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4376"/>
</filter>
<filter id="filter3_i_38_4376" x="16" y="12" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4376"/>
</filter>
<filter id="filter4_i_38_4376" x="14" y="10" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4376"/>
</filter>
<filter id="filter5_i_38_4376" x="4" y="8" width="6" height="6.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4376"/>
</filter>
<linearGradient id="paint0_linear_38_4376" x1="8" y1="10" x2="15.8" y2="17.8" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4376" x1="2" y1="5" x2="14.8255" y2="23.3221" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4376" x1="2.625" y1="5.63636" x2="22" y2="5.63636" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
