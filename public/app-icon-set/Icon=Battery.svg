<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="5" y="15" width="6" height="17" rx="1" transform="rotate(-90 5 15)" fill="url(#paint0_linear_38_4053)"/>
<foreignObject x="0" y="3" width="24" height="18"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4053_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4053)" data-figma-bg-blur-radius="3">
<mask id="path-3-inside-1_38_4053" fill="white">
<path d="M3 8C3 6.89543 3.89543 6 5 6H19C20.1046 6 21 6.89543 21 8V16C21 17.1046 20.1046 18 19 18H5C3.89543 18 3 17.1046 3 16V8Z"/>
</mask>
<path d="M3 8C3 6.89543 3.89543 6 5 6H19C20.1046 6 21 6.89543 21 8V16C21 17.1046 20.1046 18 19 18H5C3.89543 18 3 17.1046 3 16V8Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M3 8C3 6.89543 3.89543 6 5 6H19C20.1046 6 21 6.89543 21 8V16C21 17.1046 20.1046 18 19 18H5C3.89543 18 3 17.1046 3 16V8Z" fill="url(#paint1_linear_38_4053)" fill-opacity="0.2"/>
<path d="M3.2 8C3.2 7.00589 4.00589 6.2 5 6.2V5.8C3.78497 5.8 2.8 6.78497 2.8 8H3.2ZM5 6.2H19V5.8H5V6.2ZM19 6.2C19.9941 6.2 20.8 7.00589 20.8 8H21.2C21.2 6.78497 20.215 5.8 19 5.8V6.2ZM20.8 8V16H21.2V8H20.8ZM20.8 16C20.8 16.9941 19.9941 17.8 19 17.8V18.2C20.215 18.2 21.2 17.215 21.2 16H20.8ZM19 17.8H5V18.2H19V17.8ZM5 17.8C4.00589 17.8 3.2 16.9941 3.2 16H2.8C2.8 17.215 3.78497 18.2 5 18.2V17.8ZM3.2 16V8H2.8V16H3.2Z" fill="url(#paint2_linear_38_4053)" fill-opacity="0.1" mask="url(#path-3-inside-1_38_4053)"/>
</g>
<g filter="url(#filter1_i_38_4053)">
<rect x="5" y="8" width="4" height="8" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_4053)">
<rect x="10" y="8" width="4" height="8" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter3_i_38_4053)">
<rect x="15" y="8" width="4" height="8" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4053" x="0" y="3" width="24" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4053"/>
</filter>
<clipPath id="bgblur_0_38_4053_clip_path" transform="translate(0 -3)"><path d="M3 8C3 6.89543 3.89543 6 5 6H19C20.1046 6 21 6.89543 21 8V16C21 17.1046 20.1046 18 19 18H5C3.89543 18 3 17.1046 3 16V8Z"/>
</clipPath><filter id="filter1_i_38_4053" x="5" y="8" width="4" height="8.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4053"/>
</filter>
<filter id="filter2_i_38_4053" x="10" y="8" width="4" height="8.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4053"/>
</filter>
<filter id="filter3_i_38_4053" x="15" y="8" width="4" height="8.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4053"/>
</filter>
<linearGradient id="paint0_linear_38_4053" x1="5" y1="15" x2="15.404" y2="18.672" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4053" x1="3" y1="6" x2="13.8" y2="22.2" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4053" x1="3.5625" y1="6.54545" x2="21" y2="6.54545" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
