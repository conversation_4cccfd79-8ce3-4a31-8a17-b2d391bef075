<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14 15L19 20" stroke="url(#paint0_linear_38_4015)" stroke-width="2" stroke-linecap="round"/>
<foreignObject x="1" y="1" width="21" height="21"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4015_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4015)" data-figma-bg-blur-radius="3">
<circle cx="11.5" cy="11.5" r="7.5" fill="#1D1D1D" fill-opacity="0.05"/>
<circle cx="11.5" cy="11.5" r="7.5" fill="url(#paint1_linear_38_4015)" fill-opacity="0.2"/>
<circle cx="11.5" cy="11.5" r="7.4" stroke="url(#paint2_linear_38_4015)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4015)">
<circle cx="11.5" cy="11.5" r="4.5" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4015" x="1" y="1" width="21" height="21" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4015"/>
</filter>
<clipPath id="bgblur_0_38_4015_clip_path" transform="translate(-1 -1)"><circle cx="11.5" cy="11.5" r="7.5"/>
</clipPath><filter id="filter1_i_38_4015" x="7" y="7" width="9" height="9.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4015"/>
</filter>
<linearGradient id="paint0_linear_38_4015" x1="14" y1="15" x2="18.875" y2="19.875" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4015" x1="4" y1="4" x2="18.625" y2="18.625" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4015" x1="4.46875" y1="4.68182" x2="19" y2="4.68182" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
