<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.59025 16.083C9.54167 15.5 10.0018 15 10.5868 15H13.4132C13.9982 15 14.4583 15.5 14.4097 16.083L14.0764 20.083C14.0332 20.6013 13.6 21 13.0799 21H10.9201C10.4 21 9.96678 20.6013 9.92359 20.083L9.59025 16.083Z" fill="url(#paint0_linear_38_4282)"/>
<foreignObject x="2" y="-1" width="20" height="23"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4282_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4282)" data-figma-bg-blur-radius="3">
<path d="M15.2972 16.9198C15.4684 15.7209 16.2257 14.7103 17.0623 13.8346C18.2627 12.578 19 10.8751 19 9C19 5.13401 15.866 2 12 2C8.13401 2 5 5.13401 5 9C5 10.8751 5.73726 12.578 6.93771 13.8346C7.7743 14.7103 8.53156 15.7209 8.70283 16.9198L8.75469 17.2828C8.89545 18.2681 9.73929 19 10.7346 19H13.2654C14.2607 19 15.1046 18.2681 15.2453 17.2828L15.2972 16.9198Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M15.2972 16.9198C15.4684 15.7209 16.2257 14.7103 17.0623 13.8346C18.2627 12.578 19 10.8751 19 9C19 5.13401 15.866 2 12 2C8.13401 2 5 5.13401 5 9C5 10.8751 5.73726 12.578 6.93771 13.8346C7.7743 14.7103 8.53156 15.7209 8.70283 16.9198L8.75469 17.2828C8.89545 18.2681 9.73929 19 10.7346 19H13.2654C14.2607 19 15.1046 18.2681 15.2453 17.2828L15.2972 16.9198Z" fill="url(#paint1_linear_38_4282)" fill-opacity="0.2"/>
<path d="M12 2.09961C15.8108 2.09961 18.9004 5.18924 18.9004 9C18.9004 10.8483 18.1736 12.5269 16.9902 13.7656C16.1505 14.6447 15.3742 15.6742 15.1982 16.9053L15.1465 17.2686C15.0128 18.2045 14.2111 18.9003 13.2656 18.9004H10.7344C9.78893 18.9003 8.98722 18.2045 8.85352 17.2686L8.80176 16.9053C8.62575 15.6742 7.84953 14.6447 7.00977 13.7656C5.82642 12.5269 5.09961 10.8483 5.09961 9C5.09961 5.18924 8.18923 2.09961 12 2.09961Z" stroke="url(#paint2_linear_38_4282)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4282)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.70574 11.7624L10.4848 14.8788C10.5518 15.1467 10.3889 15.4181 10.121 15.4851C9.85313 15.5521 9.58167 15.3892 9.51469 15.1213L8.69142 11.8282C8.51918 11.1392 9.17542 10.5315 9.84915 10.7561L11.2092 11.2095C11.7224 11.3805 12.2772 11.3805 12.7903 11.2095L14.1504 10.7561C14.8241 10.5315 15.4804 11.1392 15.3081 11.8282L14.4848 15.1213C14.4179 15.3892 14.1464 15.5521 13.8785 15.4851C13.6106 15.4181 13.4477 15.1467 13.5147 14.8788L14.2938 11.7624L13.1066 12.1581C12.3881 12.3976 11.6114 12.3976 10.893 12.1581L9.70574 11.7624Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4282" x="2" y="-1" width="20" height="23" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4282"/>
</filter>
<clipPath id="bgblur_0_38_4282_clip_path" transform="translate(-2 1)"><path d="M15.2972 16.9198C15.4684 15.7209 16.2257 14.7103 17.0623 13.8346C18.2627 12.578 19 10.8751 19 9C19 5.13401 15.866 2 12 2C8.13401 2 5 5.13401 5 9C5 10.8751 5.73726 12.578 6.93771 13.8346C7.7743 14.7103 8.53156 15.7209 8.70283 16.9198L8.75469 17.2828C8.89545 18.2681 9.73929 19 10.7346 19H13.2654C14.2607 19 15.1046 18.2681 15.2453 17.2828L15.2972 16.9198Z"/>
</clipPath><filter id="filter1_i_38_4282" x="8.66357" y="10.7086" width="6.67236" height="5.2915" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4282"/>
</filter>
<linearGradient id="paint0_linear_38_4282" x1="9.5" y1="15" x2="15.2541" y2="19.7951" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4282" x1="5" y1="2" x2="21.2674" y2="15.3967" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4282" x1="5.4375" y1="2.77273" x2="19" y2="2.77273" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
