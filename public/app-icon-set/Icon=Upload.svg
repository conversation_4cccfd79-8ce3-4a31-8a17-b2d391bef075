<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="6" y="13" width="12" height="6" rx="2" fill="url(#paint0_linear_38_4333)"/>
<foreignObject x="0" y="1" width="24" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4333_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4333)" data-figma-bg-blur-radius="3">
<rect x="3" y="4" width="18" height="13" rx="3" fill="#1D1D1D" fill-opacity="0.05"/>
<rect x="3" y="4" width="18" height="13" rx="3" fill="url(#paint1_linear_38_4333)" fill-opacity="0.2"/>
<rect x="3.1" y="4.1" width="17.8" height="12.8" rx="2.9" stroke="url(#paint2_linear_38_4333)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4333)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 15C11.4477 15 11 14.5523 11 14L11 9.41421L9.70711 10.7071C9.31658 11.0976 8.68342 11.0976 8.29289 10.7071C7.90237 10.3166 7.90237 9.68342 8.29289 9.29289L11.2929 6.29289C11.6834 5.90237 12.3166 5.90237 12.7071 6.29289L15.7071 9.29289C16.0976 9.68342 16.0976 10.3166 15.7071 10.7071C15.3166 11.0976 14.6834 11.0976 14.2929 10.7071L13 9.41421L13 14C13 14.5523 12.5523 15 12 15Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4333" x="0" y="1" width="24" height="19" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4333"/>
</filter>
<clipPath id="bgblur_0_38_4333_clip_path" transform="translate(0 -1)"><rect x="3" y="4" width="18" height="13" rx="3"/>
</clipPath><filter id="filter1_i_38_4333" x="8" y="6" width="8" height="9.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4333"/>
</filter>
<linearGradient id="paint0_linear_38_4333" x1="6" y1="13" x2="10.68" y2="22.36" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4333" x1="3" y1="4" x2="15.0323" y2="20.66" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4333" x1="3.5625" y1="4.59091" x2="21" y2="4.59091" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
