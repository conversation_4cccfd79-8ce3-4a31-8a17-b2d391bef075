<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="2" y="19" width="21" height="3" rx="1" fill="url(#paint0_linear_38_4403)"/>
<foreignObject x="0" y="0" width="25" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4403_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4403)" data-figma-bg-blur-radius="3">
<mask id="path-3-inside-1_38_4403" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 4.5C10 3.67157 10.6716 3 11.5 3H13.5C14.3284 3 15 3.67157 15 4.5V19.5C15 20.3284 14.3284 21 13.5 21H11.5C10.6716 21 10 20.3284 10 19.5V4.5ZM3 10.5C3 9.67157 3.67157 9 4.5 9H6.5C7.32843 9 8 9.67157 8 10.5V19.5C8 20.3284 7.32843 21 6.5 21H4.5C3.67157 21 3 20.3284 3 19.5V10.5ZM18.5 12C17.6716 12 17 12.6716 17 13.5V19.5C17 20.3284 17.6716 21 18.5 21H20.5C21.3284 21 22 20.3284 22 19.5V13.5C22 12.6716 21.3284 12 20.5 12H18.5Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 4.5C10 3.67157 10.6716 3 11.5 3H13.5C14.3284 3 15 3.67157 15 4.5V19.5C15 20.3284 14.3284 21 13.5 21H11.5C10.6716 21 10 20.3284 10 19.5V4.5ZM3 10.5C3 9.67157 3.67157 9 4.5 9H6.5C7.32843 9 8 9.67157 8 10.5V19.5C8 20.3284 7.32843 21 6.5 21H4.5C3.67157 21 3 20.3284 3 19.5V10.5ZM18.5 12C17.6716 12 17 12.6716 17 13.5V19.5C17 20.3284 17.6716 21 18.5 21H20.5C21.3284 21 22 20.3284 22 19.5V13.5C22 12.6716 21.3284 12 20.5 12H18.5Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 4.5C10 3.67157 10.6716 3 11.5 3H13.5C14.3284 3 15 3.67157 15 4.5V19.5C15 20.3284 14.3284 21 13.5 21H11.5C10.6716 21 10 20.3284 10 19.5V4.5ZM3 10.5C3 9.67157 3.67157 9 4.5 9H6.5C7.32843 9 8 9.67157 8 10.5V19.5C8 20.3284 7.32843 21 6.5 21H4.5C3.67157 21 3 20.3284 3 19.5V10.5ZM18.5 12C17.6716 12 17 12.6716 17 13.5V19.5C17 20.3284 17.6716 21 18.5 21H20.5C21.3284 21 22 20.3284 22 19.5V13.5C22 12.6716 21.3284 12 20.5 12H18.5Z" fill="url(#paint1_linear_38_4403)" fill-opacity="0.2"/>
<path d="M11.5 2.8C10.5611 2.8 9.8 3.56112 9.8 4.5H10.2C10.2 3.78203 10.782 3.2 11.5 3.2V2.8ZM13.5 2.8H11.5V3.2H13.5V2.8ZM15.2 4.5C15.2 3.56112 14.4389 2.8 13.5 2.8V3.2C14.218 3.2 14.8 3.78203 14.8 4.5H15.2ZM15.2 19.5V4.5H14.8V19.5H15.2ZM13.5 21.2C14.4389 21.2 15.2 20.4389 15.2 19.5H14.8C14.8 20.218 14.218 20.8 13.5 20.8V21.2ZM11.5 21.2H13.5V20.8H11.5V21.2ZM9.8 19.5C9.8 20.4389 10.5611 21.2 11.5 21.2V20.8C10.782 20.8 10.2 20.218 10.2 19.5H9.8ZM9.8 4.5V19.5H10.2V4.5H9.8ZM4.5 8.8C3.56112 8.8 2.8 9.56112 2.8 10.5H3.2C3.2 9.78203 3.78203 9.2 4.5 9.2V8.8ZM6.5 8.8H4.5V9.2H6.5V8.8ZM8.2 10.5C8.2 9.56112 7.43888 8.8 6.5 8.8V9.2C7.21797 9.2 7.8 9.78203 7.8 10.5H8.2ZM8.2 19.5V10.5H7.8V19.5H8.2ZM6.5 21.2C7.43888 21.2 8.2 20.4389 8.2 19.5H7.8C7.8 20.218 7.21797 20.8 6.5 20.8V21.2ZM4.5 21.2H6.5V20.8H4.5V21.2ZM2.8 19.5C2.8 20.4389 3.56112 21.2 4.5 21.2V20.8C3.78203 20.8 3.2 20.218 3.2 19.5H2.8ZM2.8 10.5V19.5H3.2V10.5H2.8ZM17.2 13.5C17.2 12.782 17.782 12.2 18.5 12.2V11.8C17.5611 11.8 16.8 12.5611 16.8 13.5H17.2ZM17.2 19.5V13.5H16.8V19.5H17.2ZM18.5 20.8C17.782 20.8 17.2 20.218 17.2 19.5H16.8C16.8 20.4389 17.5611 21.2 18.5 21.2V20.8ZM20.5 20.8H18.5V21.2H20.5V20.8ZM21.8 19.5C21.8 20.218 21.218 20.8 20.5 20.8V21.2C21.4389 21.2 22.2 20.4389 22.2 19.5H21.8ZM21.8 13.5V19.5H22.2V13.5H21.8ZM20.5 12.2C21.218 12.2 21.8 12.782 21.8 13.5H22.2C22.2 12.5611 21.4389 11.8 20.5 11.8V12.2ZM18.5 12.2H20.5V11.8H18.5V12.2Z" fill="url(#paint2_linear_38_4403)" fill-opacity="0.1" mask="url(#path-3-inside-1_38_4403)"/>
</g>
<defs>
<filter id="filter0_i_38_4403" x="0" y="0" width="25" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4403"/>
</filter>
<clipPath id="bgblur_0_38_4403_clip_path" transform="translate(0 0)"><path fill-rule="evenodd" clip-rule="evenodd" d="M10 4.5C10 3.67157 10.6716 3 11.5 3H13.5C14.3284 3 15 3.67157 15 4.5V19.5C15 20.3284 14.3284 21 13.5 21H11.5C10.6716 21 10 20.3284 10 19.5V4.5ZM3 10.5C3 9.67157 3.67157 9 4.5 9H6.5C7.32843 9 8 9.67157 8 10.5V19.5C8 20.3284 7.32843 21 6.5 21H4.5C3.67157 21 3 20.3284 3 19.5V10.5ZM18.5 12C17.6716 12 17 12.6716 17 13.5V19.5C17 20.3284 17.6716 21 18.5 21H20.5C21.3284 21 22 20.3284 22 19.5V13.5C22 12.6716 21.3284 12 20.5 12H18.5Z"/>
</clipPath><linearGradient id="paint0_linear_38_4403" x1="2" y1="19" x2="2.819" y2="24.733" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4403" x1="3" y1="3" x2="20.5244" y2="21.498" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4403" x1="3.59375" y1="3.81818" x2="22" y2="3.81818" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
