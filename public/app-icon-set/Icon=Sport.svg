<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1" y="10" width="22" height="3" rx="1" fill="url(#paint0_linear_38_4071)"/>
<foreignObject x="0" y="1" width="24" height="21"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4071_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4071)" data-figma-bg-blur-radius="3">
<mask id="path-3-inside-1_38_4071" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6 4C5.44772 4 5 4.44772 5 5V5.8C5 5.91046 4.91046 6 4.8 6H4C3.44772 6 3 6.44772 3 7V16C3 16.5523 3.44772 17 4 17H4.8C4.91046 17 5 17.0895 5 17.2V18C5 18.5523 5.44772 19 6 19H8C8.55228 19 9 18.5523 9 18V5C9 4.44772 8.55228 4 8 4H6ZM16 4C15.4477 4 15 4.44772 15 5V18C15 18.5523 15.4477 19 16 19H18C18.5523 19 19 18.5523 19 18V17.2C19 17.0895 19.0895 17 19.2 17H20C20.5523 17 21 16.5523 21 16V7C21 6.44772 20.5523 6 20 6H19.2C19.0895 6 19 5.91046 19 5.8V5C19 4.44772 18.5523 4 18 4H16Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6 4C5.44772 4 5 4.44772 5 5V5.8C5 5.91046 4.91046 6 4.8 6H4C3.44772 6 3 6.44772 3 7V16C3 16.5523 3.44772 17 4 17H4.8C4.91046 17 5 17.0895 5 17.2V18C5 18.5523 5.44772 19 6 19H8C8.55228 19 9 18.5523 9 18V5C9 4.44772 8.55228 4 8 4H6ZM16 4C15.4477 4 15 4.44772 15 5V18C15 18.5523 15.4477 19 16 19H18C18.5523 19 19 18.5523 19 18V17.2C19 17.0895 19.0895 17 19.2 17H20C20.5523 17 21 16.5523 21 16V7C21 6.44772 20.5523 6 20 6H19.2C19.0895 6 19 5.91046 19 5.8V5C19 4.44772 18.5523 4 18 4H16Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6 4C5.44772 4 5 4.44772 5 5V5.8C5 5.91046 4.91046 6 4.8 6H4C3.44772 6 3 6.44772 3 7V16C3 16.5523 3.44772 17 4 17H4.8C4.91046 17 5 17.0895 5 17.2V18C5 18.5523 5.44772 19 6 19H8C8.55228 19 9 18.5523 9 18V5C9 4.44772 8.55228 4 8 4H6ZM16 4C15.4477 4 15 4.44772 15 5V18C15 18.5523 15.4477 19 16 19H18C18.5523 19 19 18.5523 19 18V17.2C19 17.0895 19.0895 17 19.2 17H20C20.5523 17 21 16.5523 21 16V7C21 6.44772 20.5523 6 20 6H19.2C19.0895 6 19 5.91046 19 5.8V5C19 4.44772 18.5523 4 18 4H16Z" fill="url(#paint1_linear_38_4071)" fill-opacity="0.2"/>
<path d="M5.2 5C5.2 4.55817 5.55817 4.2 6 4.2V3.8C5.33726 3.8 4.8 4.33726 4.8 5H5.2ZM5.2 5.8V5H4.8V5.8H5.2ZM4 6.2H4.8V5.8H4V6.2ZM3.2 7C3.2 6.55817 3.55817 6.2 4 6.2V5.8C3.33726 5.8 2.8 6.33726 2.8 7H3.2ZM3.2 16V7H2.8V16H3.2ZM4 16.8C3.55817 16.8 3.2 16.4418 3.2 16H2.8C2.8 16.6627 3.33726 17.2 4 17.2V16.8ZM4.8 16.8H4V17.2H4.8V16.8ZM5.2 18V17.2H4.8V18H5.2ZM6 18.8C5.55817 18.8 5.2 18.4418 5.2 18H4.8C4.8 18.6627 5.33726 19.2 6 19.2V18.8ZM8 18.8H6V19.2H8V18.8ZM8.8 18C8.8 18.4418 8.44183 18.8 8 18.8V19.2C8.66274 19.2 9.2 18.6627 9.2 18H8.8ZM8.8 5V18H9.2V5H8.8ZM8 4.2C8.44183 4.2 8.8 4.55817 8.8 5H9.2C9.2 4.33726 8.66274 3.8 8 3.8V4.2ZM6 4.2H8V3.8H6V4.2ZM15.2 5C15.2 4.55817 15.5582 4.2 16 4.2V3.8C15.3373 3.8 14.8 4.33726 14.8 5H15.2ZM15.2 18V5H14.8V18H15.2ZM16 18.8C15.5582 18.8 15.2 18.4418 15.2 18H14.8C14.8 18.6627 15.3373 19.2 16 19.2V18.8ZM18 18.8H16V19.2H18V18.8ZM18.8 18C18.8 18.4418 18.4418 18.8 18 18.8V19.2C18.6627 19.2 19.2 18.6627 19.2 18H18.8ZM18.8 17.2V18H19.2V17.2H18.8ZM20 16.8H19.2V17.2H20V16.8ZM20.8 16C20.8 16.4418 20.4418 16.8 20 16.8V17.2C20.6627 17.2 21.2 16.6627 21.2 16H20.8ZM20.8 7V16H21.2V7H20.8ZM20 6.2C20.4418 6.2 20.8 6.55817 20.8 7H21.2C21.2 6.33726 20.6627 5.8 20 5.8V6.2ZM19.2 6.2H20V5.8H19.2V6.2ZM18.8 5V5.8H19.2V5H18.8ZM18 4.2C18.4418 4.2 18.8 4.55817 18.8 5H19.2C19.2 4.33726 18.6627 3.8 18 3.8V4.2ZM16 4.2H18V3.8H16V4.2ZM19.2 5.8H18.8C18.8 6.02091 18.9791 6.2 19.2 6.2V5.8ZM19.2 17.2V16.8C18.9791 16.8 18.8 16.9791 18.8 17.2H19.2ZM4.8 17.2H5.2C5.2 16.9791 5.02091 16.8 4.8 16.8V17.2ZM4.8 5.8V6.2C5.02091 6.2 5.2 6.02091 5.2 5.8H4.8Z" fill="url(#paint2_linear_38_4071)" fill-opacity="0.1" mask="url(#path-3-inside-1_38_4071)"/>
</g>
<defs>
<filter id="filter0_i_38_4071" x="0" y="1" width="24" height="21" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4071"/>
</filter>
<clipPath id="bgblur_0_38_4071_clip_path" transform="translate(0 -1)"><path fill-rule="evenodd" clip-rule="evenodd" d="M6 4C5.44772 4 5 4.44772 5 5V5.8C5 5.91046 4.91046 6 4.8 6H4C3.44772 6 3 6.44772 3 7V16C3 16.5523 3.44772 17 4 17H4.8C4.91046 17 5 17.0895 5 17.2V18C5 18.5523 5.44772 19 6 19H8C8.55228 19 9 18.5523 9 18V5C9 4.44772 8.55228 4 8 4H6ZM16 4C15.4477 4 15 4.44772 15 5V18C15 18.5523 15.4477 19 16 19H18C18.5523 19 19 18.5523 19 18V17.2C19 17.0895 19.0895 17 19.2 17H20C20.5523 17 21 16.5523 21 16V7C21 6.44772 20.5523 6 20 6H19.2C19.0895 6 19 5.91046 19 5.8V5C19 4.44772 18.5523 4 18 4H16Z"/>
</clipPath><linearGradient id="paint0_linear_38_4071" x1="1" y1="10" x2="1.78316" y2="15.7432" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4071" x1="3" y1="4" x2="17.3852" y2="21.2623" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4071" x1="3.5625" y1="4.68182" x2="21" y2="4.68182" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
