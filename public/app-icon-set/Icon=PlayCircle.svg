<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="12" cy="16" r="6" fill="url(#paint0_linear_38_4278)"/>
<foreignObject x="0" y="0" width="24" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4278_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4278)" data-figma-bg-blur-radius="3">
<circle cx="12" cy="12" r="9" fill="#1D1D1D" fill-opacity="0.05"/>
<circle cx="12" cy="12" r="9" fill="url(#paint1_linear_38_4278)" fill-opacity="0.2"/>
<circle cx="12" cy="12" r="8.9" stroke="url(#paint2_linear_38_4278)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4278)">
<path d="M15.1263 11.4176L11.0883 8.72553C10.6231 8.4154 10 8.74888 10 9.30796V14.692C10 15.2511 10.6231 15.5846 11.0883 15.2745L15.1263 12.5824C15.542 12.3054 15.542 11.6946 15.1263 11.4176Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4278" x="0" y="0" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4278"/>
</filter>
<clipPath id="bgblur_0_38_4278_clip_path" transform="translate(0 0)"><circle cx="12" cy="12" r="9"/>
</clipPath><filter id="filter1_i_38_4278" x="10" y="8.60681" width="5.43799" height="7.28638" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4278"/>
</filter>
<linearGradient id="paint0_linear_38_4278" x1="6" y1="10" x2="17.7" y2="21.7" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4278" x1="3" y1="3" x2="20.55" y2="20.55" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4278" x1="3.5625" y1="3.81818" x2="21" y2="3.81818" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
