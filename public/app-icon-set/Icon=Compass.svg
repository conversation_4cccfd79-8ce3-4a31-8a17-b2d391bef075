<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="15" cy="9" r="5" fill="url(#paint0_linear_38_4019)"/>
<foreignObject x="1" y="1" width="22" height="22"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4019_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4019)" data-figma-bg-blur-radius="3">
<circle cx="12" cy="12" r="8" fill="#1D1D1D" fill-opacity="0.05"/>
<circle cx="12" cy="12" r="8" fill="url(#paint1_linear_38_4019)" fill-opacity="0.2"/>
<circle cx="12" cy="12" r="7.9" stroke="url(#paint2_linear_38_4019)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4019)">
<path d="M15.4201 7.90008C15.8597 7.65586 16.3442 8.14039 16.1 8.57998L13.9693 12.4151C13.6066 13.0681 13.0682 13.6064 12.4153 13.9692L8.58014 16.0998C8.14055 16.344 7.65602 15.8595 7.90024 15.4199L10.0309 11.5848C10.3936 10.9318 10.932 10.3935 11.5849 10.0307L15.4201 7.90008Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4019" x="1" y="1" width="22" height="22" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4019"/>
</filter>
<clipPath id="bgblur_0_38_4019_clip_path" transform="translate(-1 -1)"><circle cx="12" cy="12" r="8"/>
</clipPath><filter id="filter1_i_38_4019" x="7.83447" y="7.83447" width="8.33105" height="8.83093" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4019"/>
</filter>
<linearGradient id="paint0_linear_38_4019" x1="10" y1="4" x2="19.75" y2="13.75" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4019" x1="4" y1="4" x2="19.6" y2="19.6" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4019" x1="4.5" y1="4.72727" x2="20" y2="4.72727" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
