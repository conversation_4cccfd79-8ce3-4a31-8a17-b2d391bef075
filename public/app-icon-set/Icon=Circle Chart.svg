<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 3.00001C12 2.44772 11.5512 1.99483 11.0017 2.04997C10.0309 2.14737 9.07783 2.38649 8.17317 2.76121C6.95992 3.26376 5.85752 4.00035 4.92894 4.92894C4.00035 5.85753 3.26376 6.95992 2.76121 8.17317C2.38649 9.07784 2.14737 10.0309 2.04997 11.0017C1.99483 11.5512 2.44772 12 3.00001 12H11C11.5523 12 12 11.5523 12 11L12 3.00001Z" fill="url(#paint0_linear_38_4400)"/>
<foreignObject x="1" y="1" width="22" height="22"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4400_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4400)" data-figma-bg-blur-radius="3">
<circle cx="12" cy="12" r="8" fill="#1D1D1D" fill-opacity="0.05"/>
<circle cx="12" cy="12" r="8" fill="url(#paint1_linear_38_4400)" fill-opacity="0.2"/>
<circle cx="12" cy="12" r="7.9" stroke="url(#paint2_linear_38_4400)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<defs>
<filter id="filter0_i_38_4400" x="1" y="1" width="22" height="22" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4400"/>
</filter>
<clipPath id="bgblur_0_38_4400_clip_path" transform="translate(-1 -1)"><circle cx="12" cy="12" r="8"/>
</clipPath><linearGradient id="paint0_linear_38_4400" x1="2.04541" y1="2.04541" x2="11.7511" y2="11.7511" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4400" x1="4" y1="4" x2="19.6" y2="19.6" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4400" x1="4.5" y1="4.72727" x2="20" y2="4.72727" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
