<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="12" y="7.75732" width="6" height="6" rx="2" transform="rotate(45 12 7.75732)" fill="url(#paint0_linear_38_3984)"/>
<foreignObject x="4.52148" y="0.521362" width="16.957" height="22.9573"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_3984_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_3984)" data-figma-bg-blur-radius="3">
<path d="M8.93934 4.06066L8.06066 4.93934C7.47487 5.52513 7.47487 6.47487 8.06066 7.06066L12.8586 11.8586C12.9367 11.9367 12.9367 12.0633 12.8586 12.1414L8.06066 16.9393C7.47487 17.5251 7.47487 18.4749 8.06066 19.0607L8.93934 19.9393C9.52513 20.5251 10.4749 20.5251 11.0607 19.9393L17.9393 13.0607C18.5251 12.4749 18.5251 11.5251 17.9393 10.9393L11.0607 4.06066C10.4749 3.47487 9.52513 3.47487 8.93934 4.06066Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M8.93934 4.06066L8.06066 4.93934C7.47487 5.52513 7.47487 6.47487 8.06066 7.06066L12.8586 11.8586C12.9367 11.9367 12.9367 12.0633 12.8586 12.1414L8.06066 16.9393C7.47487 17.5251 7.47487 18.4749 8.06066 19.0607L8.93934 19.9393C9.52513 20.5251 10.4749 20.5251 11.0607 19.9393L17.9393 13.0607C18.5251 12.4749 18.5251 11.5251 17.9393 10.9393L11.0607 4.06066C10.4749 3.47487 9.52513 3.47487 8.93934 4.06066Z" fill="url(#paint1_linear_38_3984)" fill-opacity="0.2"/>
<path d="M8.93934 4.06066L8.06066 4.93934C7.47487 5.52513 7.47487 6.47487 8.06066 7.06066L12.8586 11.8586C12.9367 11.9367 12.9367 12.0633 12.8586 12.1414L8.06066 16.9393C7.47487 17.5251 7.47487 18.4749 8.06066 19.0607L8.93934 19.9393C9.52513 20.5251 10.4749 20.5251 11.0607 19.9393L17.9393 13.0607C18.5251 12.4749 18.5251 11.5251 17.9393 10.9393L11.0607 4.06066C10.4749 3.47487 9.52513 3.47487 8.93934 4.06066Z" stroke="url(#paint2_linear_38_3984)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<defs>
<filter id="filter0_i_38_3984" x="4.52148" y="0.521362" width="16.957" height="22.9573" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3984"/>
</filter>
<clipPath id="bgblur_0_38_3984_clip_path" transform="translate(-4.52148 -0.521362)"><path d="M8.93934 4.06066L8.06066 4.93934C7.47487 5.52513 7.47487 6.47487 8.06066 7.06066L12.8586 11.8586C12.9367 11.9367 12.9367 12.0633 12.8586 12.1414L8.06066 16.9393C7.47487 17.5251 7.47487 18.4749 8.06066 19.0607L8.93934 19.9393C9.52513 20.5251 10.4749 20.5251 11.0607 19.9393L17.9393 13.0607C18.5251 12.4749 18.5251 11.5251 17.9393 10.9393L11.0607 4.06066C10.4749 3.47487 9.52513 3.47487 8.93934 4.06066Z"/>
</clipPath><linearGradient id="paint0_linear_38_3984" x1="12" y1="7.75732" x2="17.85" y2="13.6073" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_3984" x1="7" y1="3" x2="23.2" y2="13.8" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_3984" x1="7.375" y1="3.81818" x2="19" y2="3.81818" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
