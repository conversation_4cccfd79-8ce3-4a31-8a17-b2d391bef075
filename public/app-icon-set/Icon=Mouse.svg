<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="12" cy="6" r="3" fill="url(#paint0_linear_38_4372)"/>
<foreignObject x="3" y="1" width="18" height="23"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4372_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4372)" data-figma-bg-blur-radius="3">
<path d="M6 10C6 8.13623 6 7.20435 6.30448 6.46927C6.71046 5.48915 7.48915 4.71046 8.46927 4.30448C9.20435 4 10.1362 4 12 4C13.8638 4 14.7956 4 15.5307 4.30448C16.5108 4.71046 17.2895 5.48915 17.6955 6.46927C18 7.20435 18 8.13623 18 10V15C18 16.8638 18 17.7956 17.6955 18.5307C17.2895 19.5108 16.5108 20.2895 15.5307 20.6955C14.7956 21 13.8638 21 12 21C10.1362 21 9.20435 21 8.46927 20.6955C7.48915 20.2895 6.71046 19.5108 6.30448 18.5307C6 17.7956 6 16.8638 6 15L6 10Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M6 10C6 8.13623 6 7.20435 6.30448 6.46927C6.71046 5.48915 7.48915 4.71046 8.46927 4.30448C9.20435 4 10.1362 4 12 4C13.8638 4 14.7956 4 15.5307 4.30448C16.5108 4.71046 17.2895 5.48915 17.6955 6.46927C18 7.20435 18 8.13623 18 10V15C18 16.8638 18 17.7956 17.6955 18.5307C17.2895 19.5108 16.5108 20.2895 15.5307 20.6955C14.7956 21 13.8638 21 12 21C10.1362 21 9.20435 21 8.46927 20.6955C7.48915 20.2895 6.71046 19.5108 6.30448 18.5307C6 17.7956 6 16.8638 6 15L6 10Z" fill="url(#paint1_linear_38_4372)" fill-opacity="0.2"/>
<path d="M12 4.09961C12.9331 4.09961 13.628 4.09993 14.1816 4.1377C14.7342 4.17539 15.1389 4.25023 15.4922 4.39648C16.4478 4.79231 17.2077 5.5522 17.6035 6.50781C17.7498 6.86105 17.8246 7.26582 17.8623 7.81836C17.9001 8.37196 17.9004 9.06685 17.9004 10V15C17.9004 15.9331 17.9001 16.628 17.8623 17.1816C17.8246 17.7342 17.7498 18.1389 17.6035 18.4922C17.2077 19.4478 16.4478 20.2077 15.4922 20.6035C15.1389 20.7498 14.7342 20.8246 14.1816 20.8623C13.628 20.9001 12.9331 20.9004 12 20.9004C11.0669 20.9004 10.372 20.9001 9.81836 20.8623C9.26582 20.8246 8.86105 20.7498 8.50781 20.6035C7.5522 20.2077 6.79231 19.4478 6.39648 18.4922C6.25023 18.1389 6.17539 17.7342 6.1377 17.1816C6.09993 16.628 6.09961 15.9331 6.09961 15L6.09961 10C6.09961 9.06685 6.09993 8.37196 6.1377 7.81836C6.17539 7.26582 6.25023 6.86105 6.39648 6.50781C6.79231 5.5522 7.5522 4.79231 8.50781 4.39648C8.86105 4.25023 9.26582 4.17539 9.81836 4.1377C10.372 4.09993 11.0669 4.09961 12 4.09961Z" stroke="url(#paint2_linear_38_4372)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4372)">
<rect x="11" y="6" width="2" height="5" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4372" x="3" y="1" width="18" height="23" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4372"/>
</filter>
<clipPath id="bgblur_0_38_4372_clip_path" transform="translate(-3 -1)"><path d="M6 10C6 8.13623 6 7.20435 6.30448 6.46927C6.71046 5.48915 7.48915 4.71046 8.46927 4.30448C9.20435 4 10.1362 4 12 4C13.8638 4 14.7956 4 15.5307 4.30448C16.5108 4.71046 17.2895 5.48915 17.6955 6.46927C18 7.20435 18 8.13623 18 10V15C18 16.8638 18 17.7956 17.6955 18.5307C17.2895 19.5108 16.5108 20.2895 15.5307 20.6955C14.7956 21 13.8638 21 12 21C10.1362 21 9.20435 21 8.46927 20.6955C7.48915 20.2895 6.71046 19.5108 6.30448 18.5307C6 17.7956 6 16.8638 6 15L6 10Z"/>
</clipPath><filter id="filter1_i_38_4372" x="11" y="6" width="2" height="5.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4372"/>
</filter>
<linearGradient id="paint0_linear_38_4372" x1="9" y1="3" x2="14.85" y2="8.85" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4372" x1="6" y1="4" x2="21.618" y2="15.0245" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4372" x1="6.375" y1="4.77273" x2="18" y2="4.77273" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
