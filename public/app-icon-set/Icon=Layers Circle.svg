<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<ellipse cx="12" cy="14.5" rx="8" ry="3.5" fill="url(#paint0_linear_38_4193)"/>
<foreignObject x="1" y="5" width="22" height="13"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4193_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4193)" data-figma-bg-blur-radius="3">
<ellipse cx="12" cy="11.5" rx="8" ry="3.5" fill="#1D1D1D" fill-opacity="0.05"/>
<ellipse cx="12" cy="11.5" rx="8" ry="3.5" fill="url(#paint1_linear_38_4193)" fill-opacity="0.2"/>
<path d="M12 8.09961C14.1987 8.09961 16.1848 8.49054 17.6172 9.11719C18.3333 9.43053 18.907 9.80069 19.2998 10.207C19.6922 10.6129 19.9004 11.0499 19.9004 11.5C19.9004 11.9501 19.6922 12.3871 19.2998 12.793C18.907 13.1993 18.3333 13.5695 17.6172 13.8828C16.1848 14.5095 14.1987 14.9004 12 14.9004C9.80132 14.9004 7.81515 14.5095 6.38281 13.8828C5.66673 13.5695 5.09303 13.1993 4.7002 12.793C4.30781 12.3871 4.09961 11.9501 4.09961 11.5C4.09961 11.0499 4.30781 10.6129 4.7002 10.207C5.09303 9.80069 5.66673 9.43053 6.38281 9.11719C7.81515 8.49054 9.80132 8.09961 12 8.09961Z" stroke="url(#paint2_linear_38_4193)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<foreignObject x="1" y="2" width="22" height="13"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_1_38_4193_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_i_38_4193)" data-figma-bg-blur-radius="3">
<ellipse cx="12" cy="8.5" rx="8" ry="3.5" fill="#1D1D1D" fill-opacity="0.05"/>
<ellipse cx="12" cy="8.5" rx="8" ry="3.5" fill="url(#paint3_linear_38_4193)" fill-opacity="0.2"/>
<path d="M12 5.09961C14.1987 5.09961 16.1848 5.49054 17.6172 6.11719C18.3333 6.43053 18.907 6.80069 19.2998 7.20703C19.6922 7.61295 19.9004 8.04989 19.9004 8.5C19.9004 8.95011 19.6922 9.38705 19.2998 9.79297C18.907 10.1993 18.3333 10.5695 17.6172 10.8828C16.1848 11.5095 14.1987 11.9004 12 11.9004C9.80132 11.9004 7.81515 11.5095 6.38281 10.8828C5.66673 10.5695 5.09303 10.1993 4.7002 9.79297C4.30781 9.38705 4.09961 8.95011 4.09961 8.5C4.09961 8.04989 4.30781 7.61295 4.7002 7.20703C5.09303 6.80069 5.66673 6.43053 6.38281 6.11719C7.81515 5.49054 9.80132 5.09961 12 5.09961Z" stroke="url(#paint4_linear_38_4193)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<defs>
<filter id="filter0_i_38_4193" x="1" y="5" width="22" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4193"/>
</filter>
<clipPath id="bgblur_0_38_4193_clip_path" transform="translate(-1 -5)"><ellipse cx="12" cy="11.5" rx="8" ry="3.5"/>
</clipPath><filter id="filter1_i_38_4193" x="1" y="2" width="22" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4193"/>
</filter>
<clipPath id="bgblur_1_38_4193_clip_path" transform="translate(-1 -2)"><ellipse cx="12" cy="8.5" rx="8" ry="3.5"/>
</clipPath><linearGradient id="paint0_linear_38_4193" x1="4" y1="11" x2="9.01246" y2="22.457" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4193" x1="4" y1="8" x2="9.01246" y2="19.457" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4193" x1="4.5" y1="8.31818" x2="20" y2="8.31818" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
<linearGradient id="paint3_linear_38_4193" x1="4" y1="5" x2="9.01246" y2="16.457" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint4_linear_38_4193" x1="4.5" y1="5.31818" x2="20" y2="5.31818" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
