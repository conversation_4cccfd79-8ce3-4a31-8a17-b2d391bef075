<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.3406 3H5.47703C4.76957 3 4.28581 3.71453 4.54856 4.37139L7.74856 12.3714C7.90042 12.751 8.26813 13 8.67703 13H15.323C15.7319 13 16.0996 12.751 16.2514 12.3714L19.4514 4.37139C19.7142 3.71453 19.2304 3 18.523 3H15.6594C15.2594 3 14.8978 3.2384 14.7403 3.60608L12.1838 9.57107C12.1146 9.73269 11.8854 9.73269 11.8162 9.57107L9.25975 3.60608C9.10217 3.2384 8.74063 3 8.3406 3Z" fill="url(#paint0_linear_38_4294)"/>
<foreignObject x="2" y="5" width="20" height="20"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4294_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4294)" data-figma-bg-blur-radius="3">
<circle cx="12" cy="15" r="7" fill="#1D1D1D" fill-opacity="0.05"/>
<circle cx="12" cy="15" r="7" fill="url(#paint1_linear_38_4294)" fill-opacity="0.2"/>
<circle cx="12" cy="15" r="6.9" stroke="url(#paint2_linear_38_4294)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4294)">
<path d="M11.7146 10.8781C11.8044 10.6017 12.1954 10.6017 12.2852 10.8781L13.1225 13.4549H15.8319C16.1225 13.4549 16.2433 13.8268 16.0082 13.9976L13.8163 15.5902L14.6535 18.167C14.7433 18.4434 14.427 18.6732 14.1919 18.5024L11.9999 16.9098L9.80795 18.5024C9.57284 18.6732 9.2565 18.4434 9.3463 18.167L10.1836 15.5902L7.9916 13.9976C7.75648 13.8268 7.87732 13.4549 8.16793 13.4549H10.8773L11.7146 10.8781Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4294" x="2" y="5" width="20" height="20" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4294"/>
</filter>
<clipPath id="bgblur_0_38_4294_clip_path" transform="translate(-2 -5)"><circle cx="12" cy="15" r="7"/>
</clipPath><filter id="filter1_i_38_4294" x="7.86719" y="10.6708" width="8.26514" height="8.39026" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4294"/>
</filter>
<linearGradient id="paint0_linear_38_4294" x1="4" y1="3" x2="12.764" y2="17.0225" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4294" x1="5" y1="8" x2="18.65" y2="21.65" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4294" x1="5.4375" y1="8.63636" x2="19" y2="8.63636" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
