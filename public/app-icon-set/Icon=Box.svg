<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="5" y="4" width="14" height="4" rx="1" fill="url(#paint0_linear_38_4237)"/>
<foreignObject x="0" y="2" width="24" height="21"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4237_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4237)" data-figma-bg-blur-radius="3">
<mask id="path-3-inside-1_38_4237" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 5C3.89543 5 3 5.89543 3 7V9C3 9.699 3.35859 10.3143 3.90186 10.6718C3.96149 10.7111 4 10.7764 4 10.8478V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V10.8478C20 10.7764 20.0385 10.7111 20.0981 10.6718C20.6414 10.3143 21 9.699 21 9V7C21 5.89543 20.1046 5 19 5H5Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 5C3.89543 5 3 5.89543 3 7V9C3 9.699 3.35859 10.3143 3.90186 10.6718C3.96149 10.7111 4 10.7764 4 10.8478V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V10.8478C20 10.7764 20.0385 10.7111 20.0981 10.6718C20.6414 10.3143 21 9.699 21 9V7C21 5.89543 20.1046 5 19 5H5Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 5C3.89543 5 3 5.89543 3 7V9C3 9.699 3.35859 10.3143 3.90186 10.6718C3.96149 10.7111 4 10.7764 4 10.8478V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V10.8478C20 10.7764 20.0385 10.7111 20.0981 10.6718C20.6414 10.3143 21 9.699 21 9V7C21 5.89543 20.1046 5 19 5H5Z" fill="url(#paint1_linear_38_4237)" fill-opacity="0.2"/>
<path d="M20.0981 10.6718L19.9882 10.5048L20.0981 10.6718ZM3.90186 10.6718L3.7919 10.8389L3.90186 10.6718ZM3.2 7C3.2 6.00589 4.00589 5.2 5 5.2V4.8C3.78497 4.8 2.8 5.78497 2.8 7H3.2ZM3.2 9V7H2.8V9H3.2ZM4.01181 10.5048C3.52237 10.1826 3.2 9.6289 3.2 9H2.8C2.8 9.7691 3.19482 10.4459 3.7919 10.8389L4.01181 10.5048ZM4.2 18V10.8478H3.8V18H4.2ZM6 19.8C5.00589 19.8 4.2 18.9941 4.2 18H3.8C3.8 19.215 4.78497 20.2 6 20.2V19.8ZM18 19.8H6V20.2H18V19.8ZM19.8 18C19.8 18.9941 18.9941 19.8 18 19.8V20.2C19.215 20.2 20.2 19.215 20.2 18H19.8ZM19.8 10.8478V18H20.2V10.8478H19.8ZM20.8 9C20.8 9.6289 20.4776 10.1826 19.9882 10.5048L20.2081 10.8389C20.8052 10.4459 21.2 9.7691 21.2 9H20.8ZM20.8 7V9H21.2V7H20.8ZM19 5.2C19.9941 5.2 20.8 6.00589 20.8 7H21.2C21.2 5.78497 20.215 4.8 19 4.8V5.2ZM5 5.2H19V4.8H5V5.2ZM20.2 10.8478C20.2 10.8485 20.1998 10.8481 20.2007 10.8463C20.2018 10.8443 20.2042 10.8415 20.2081 10.8389L19.9882 10.5048C19.8777 10.5774 19.8 10.7029 19.8 10.8478H20.2ZM3.7919 10.8389C3.79582 10.8415 3.79815 10.8443 3.79926 10.8463C3.80025 10.8481 3.8 10.8485 3.8 10.8478H4.2C4.2 10.7029 4.12226 10.5774 4.01181 10.5048L3.7919 10.8389Z" fill="url(#paint2_linear_38_4237)" fill-opacity="0.1" mask="url(#path-3-inside-1_38_4237)"/>
</g>
<g filter="url(#filter1_i_38_4237)">
<rect x="6" y="11" width="12" height="1" rx="0.5" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4237" x="0" y="2" width="24" height="21" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4237"/>
</filter>
<clipPath id="bgblur_0_38_4237_clip_path" transform="translate(0 -2)"><path fill-rule="evenodd" clip-rule="evenodd" d="M5 5C3.89543 5 3 5.89543 3 7V9C3 9.699 3.35859 10.3143 3.90186 10.6718C3.96149 10.7111 4 10.7764 4 10.8478V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V10.8478C20 10.7764 20.0385 10.7111 20.0981 10.6718C20.6414 10.3143 21 9.699 21 9V7C21 5.89543 20.1046 5 19 5H5Z"/>
</clipPath><filter id="filter1_i_38_4237" x="6" y="11" width="12" height="1.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4237"/>
</filter>
<linearGradient id="paint0_linear_38_4237" x1="5" y1="4" x2="7.06038" y2="11.2113" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4237" x1="3" y1="5" x2="17.3852" y2="22.2623" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4237" x1="3.5625" y1="5.68182" x2="21" y2="5.68182" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
