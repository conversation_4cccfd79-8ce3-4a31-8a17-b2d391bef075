<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13 6H11C10.4477 6 10 6.44772 10 7V8C10 8.55228 10.4477 9 11 9H13C13.5523 9 14 8.55228 14 8V7C14 6.44772 13.5523 6 13 6ZM11 4C9.34315 4 8 5.34315 8 7V8C8 9.65685 9.34315 11 11 11H13C14.6569 11 16 9.65685 16 8V7C16 5.34315 14.6569 4 13 4H11Z" fill="url(#paint0_linear_38_4431)"/>
<foreignObject x="0" y="4" width="24" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4431_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4431)" data-figma-bg-blur-radius="3">
<path d="M14.5 20H9.5C7.16537 20 5.99805 20 5.11441 19.5277C4.4167 19.1548 3.84525 18.5833 3.47231 17.8856C3 17.0019 3 15.8346 3 13.5C3 11.1654 3 9.99805 3.47231 9.11441C3.84525 8.4167 4.4167 7.84525 5.11441 7.47231C5.99805 7 7.16537 7 9.5 7H14.5C16.8346 7 18.0019 7 18.8856 7.47231C19.5833 7.84525 20.1548 8.4167 20.5277 9.11441C21 9.99805 21 11.1654 21 13.5C21 15.8346 21 17.0019 20.5277 17.8856C20.1548 18.5833 19.5833 19.1548 18.8856 19.5277C18.0019 20 16.8346 20 14.5 20Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M14.5 20H9.5C7.16537 20 5.99805 20 5.11441 19.5277C4.4167 19.1548 3.84525 18.5833 3.47231 17.8856C3 17.0019 3 15.8346 3 13.5C3 11.1654 3 9.99805 3.47231 9.11441C3.84525 8.4167 4.4167 7.84525 5.11441 7.47231C5.99805 7 7.16537 7 9.5 7H14.5C16.8346 7 18.0019 7 18.8856 7.47231C19.5833 7.84525 20.1548 8.4167 20.5277 9.11441C21 9.99805 21 11.1654 21 13.5C21 15.8346 21 17.0019 20.5277 17.8856C20.1548 18.5833 19.5833 19.1548 18.8856 19.5277C18.0019 20 16.8346 20 14.5 20Z" fill="url(#paint1_linear_38_4431)" fill-opacity="0.2"/>
<path d="M9.5 7.09961H14.5C15.669 7.09961 16.5396 7.09961 17.2285 7.1582C17.9161 7.21669 18.414 7.33346 18.8389 7.56055C19.5189 7.9241 20.0759 8.48114 20.4395 9.16113C20.6665 9.58599 20.7833 10.0839 20.8418 10.7715C20.9004 11.4604 20.9004 12.331 20.9004 13.5C20.9004 14.669 20.9004 15.5396 20.8418 16.2285C20.7833 16.9161 20.6665 17.414 20.4395 17.8389C20.0759 18.5189 19.5189 19.0759 18.8389 19.4395C18.414 19.6665 17.9161 19.7833 17.2285 19.8418C16.5396 19.9004 15.669 19.9004 14.5 19.9004H9.5C8.33096 19.9004 7.46038 19.9004 6.77148 19.8418C6.08385 19.7833 5.58599 19.6665 5.16113 19.4395C4.48114 19.0759 3.9241 18.5189 3.56055 17.8389C3.33346 17.414 3.21669 16.9161 3.1582 16.2285C3.09961 15.5396 3.09961 14.669 3.09961 13.5C3.09961 12.331 3.09961 11.4604 3.1582 10.7715C3.21669 10.0839 3.33346 9.58599 3.56055 9.16113C3.9241 8.48114 4.48114 7.9241 5.16113 7.56055C5.58599 7.33346 6.08385 7.21669 6.77148 7.1582C7.46038 7.09961 8.33096 7.09961 9.5 7.09961Z" stroke="url(#paint2_linear_38_4431)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4431)">
<rect x="7" y="10" width="10" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_4431)">
<rect x="10" y="13" width="4" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4431" x="0" y="4" width="24" height="19" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4431"/>
</filter>
<clipPath id="bgblur_0_38_4431_clip_path" transform="translate(0 -4)"><path d="M14.5 20H9.5C7.16537 20 5.99805 20 5.11441 19.5277C4.4167 19.1548 3.84525 18.5833 3.47231 17.8856C3 17.0019 3 15.8346 3 13.5C3 11.1654 3 9.99805 3.47231 9.11441C3.84525 8.4167 4.4167 7.84525 5.11441 7.47231C5.99805 7 7.16537 7 9.5 7H14.5C16.8346 7 18.0019 7 18.8856 7.47231C19.5833 7.84525 20.1548 8.4167 20.5277 9.11441C21 9.99805 21 11.1654 21 13.5C21 15.8346 21 17.0019 20.5277 17.8856C20.1548 18.5833 19.5833 19.1548 18.8856 19.5277C18.0019 20 16.8346 20 14.5 20Z"/>
</clipPath><filter id="filter1_i_38_4431" x="7" y="10" width="10" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4431"/>
</filter>
<filter id="filter2_i_38_4431" x="10" y="13" width="4" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4431"/>
</filter>
<linearGradient id="paint0_linear_38_4431" x1="8" y1="4" x2="14.7646" y2="11.731" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4431" x1="3" y1="7" x2="15.0323" y2="23.66" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4431" x1="3.5625" y1="7.59091" x2="21" y2="7.59091" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
