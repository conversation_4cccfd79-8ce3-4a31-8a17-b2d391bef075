<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="12" cy="12" r="6" fill="url(#paint0_linear_38_4093)"/>
<foreignObject x="0" y="1" width="24" height="22"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4093_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4093)" data-figma-bg-blur-radius="3">
<mask id="path-3-inside-1_38_4093" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 4C3.89543 4 3 4.89543 3 6V9C3 10.1046 3.89543 11 5 11H19C20.1046 11 21 10.1046 21 9V6C21 4.89543 20.1046 4 19 4H5ZM5 13C3.89543 13 3 13.8954 3 15V18C3 19.1046 3.89543 20 5 20H19C20.1046 20 21 19.1046 21 18V15C21 13.8954 20.1046 13 19 13H5Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 4C3.89543 4 3 4.89543 3 6V9C3 10.1046 3.89543 11 5 11H19C20.1046 11 21 10.1046 21 9V6C21 4.89543 20.1046 4 19 4H5ZM5 13C3.89543 13 3 13.8954 3 15V18C3 19.1046 3.89543 20 5 20H19C20.1046 20 21 19.1046 21 18V15C21 13.8954 20.1046 13 19 13H5Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 4C3.89543 4 3 4.89543 3 6V9C3 10.1046 3.89543 11 5 11H19C20.1046 11 21 10.1046 21 9V6C21 4.89543 20.1046 4 19 4H5ZM5 13C3.89543 13 3 13.8954 3 15V18C3 19.1046 3.89543 20 5 20H19C20.1046 20 21 19.1046 21 18V15C21 13.8954 20.1046 13 19 13H5Z" fill="url(#paint1_linear_38_4093)" fill-opacity="0.2"/>
<path d="M3.2 6C3.2 5.00589 4.00589 4.2 5 4.2V3.8C3.78497 3.8 2.8 4.78497 2.8 6H3.2ZM3.2 9V6H2.8V9H3.2ZM5 10.8C4.00589 10.8 3.2 9.99411 3.2 9H2.8C2.8 10.215 3.78497 11.2 5 11.2V10.8ZM19 10.8H5V11.2H19V10.8ZM20.8 9C20.8 9.99411 19.9941 10.8 19 10.8V11.2C20.215 11.2 21.2 10.215 21.2 9H20.8ZM20.8 6V9H21.2V6H20.8ZM19 4.2C19.9941 4.2 20.8 5.00589 20.8 6H21.2C21.2 4.78497 20.215 3.8 19 3.8V4.2ZM5 4.2H19V3.8H5V4.2ZM3.2 15C3.2 14.0059 4.00589 13.2 5 13.2V12.8C3.78497 12.8 2.8 13.785 2.8 15H3.2ZM3.2 18V15H2.8V18H3.2ZM5 19.8C4.00589 19.8 3.2 18.9941 3.2 18H2.8C2.8 19.215 3.78497 20.2 5 20.2V19.8ZM19 19.8H5V20.2H19V19.8ZM20.8 18C20.8 18.9941 19.9941 19.8 19 19.8V20.2C20.215 20.2 21.2 19.215 21.2 18H20.8ZM20.8 15V18H21.2V15H20.8ZM19 13.2C19.9941 13.2 20.8 14.0059 20.8 15H21.2C21.2 13.785 20.215 12.8 19 12.8V13.2ZM5 13.2H19V12.8H5V13.2Z" fill="url(#paint2_linear_38_4093)" fill-opacity="0.1" mask="url(#path-3-inside-1_38_4093)"/>
</g>
<defs>
<filter id="filter0_i_38_4093" x="0" y="1" width="24" height="22" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4093"/>
</filter>
<clipPath id="bgblur_0_38_4093_clip_path" transform="translate(0 -1)"><path fill-rule="evenodd" clip-rule="evenodd" d="M5 4C3.89543 4 3 4.89543 3 6V9C3 10.1046 3.89543 11 5 11H19C20.1046 11 21 10.1046 21 9V6C21 4.89543 20.1046 4 19 4H5ZM5 13C3.89543 13 3 13.8954 3 15V18C3 19.1046 3.89543 20 5 20H19C20.1046 20 21 19.1046 21 18V15C21 13.8954 20.1046 13 19 13H5Z"/>
</clipPath><linearGradient id="paint0_linear_38_4093" x1="6" y1="6" x2="17.7" y2="17.7" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4093" x1="3" y1="4" x2="18.4924" y2="21.429" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4093" x1="3.5625" y1="4.72727" x2="21" y2="4.72727" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
