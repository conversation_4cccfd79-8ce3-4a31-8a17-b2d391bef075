<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.5 12.866C12.8333 12.4811 12.8333 11.5189 13.5 11.134L19.5 7.66987C20.1667 7.28497 21 7.7661 21 8.5359L21 15.4641C21 16.2339 20.1667 16.715 19.5 16.3301L13.5 12.866Z" fill="url(#paint0_linear_38_4359)"/>
<foreignObject x="0" y="3" width="20" height="18"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4359_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4359)" data-figma-bg-blur-radius="3">
<rect x="3" y="6" width="14" height="12" rx="2" fill="#1D1D1D" fill-opacity="0.05"/>
<rect x="3" y="6" width="14" height="12" rx="2" fill="url(#paint1_linear_38_4359)" fill-opacity="0.2"/>
<rect x="3.1" y="6.1" width="13.8" height="11.8" rx="1.9" stroke="url(#paint2_linear_38_4359)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4359)">
<rect x="5" y="8" width="10" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4359" x="0" y="3" width="20" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4359"/>
</filter>
<clipPath id="bgblur_0_38_4359_clip_path" transform="translate(0 -3)"><rect x="3" y="6" width="14" height="12" rx="2"/>
</clipPath><filter id="filter1_i_38_4359" x="5" y="8" width="10" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4359"/>
</filter>
<linearGradient id="paint0_linear_38_4359" x1="12" y1="18" x2="23.7" y2="6.3" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4359" x1="3" y1="6" x2="14.5624" y2="19.4894" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4359" x1="3.4375" y1="6.54545" x2="17" y2="6.54545" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
