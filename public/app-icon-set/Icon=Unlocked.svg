<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="17" cy="11" r="4" fill="url(#paint0_linear_38_4313)"/>
<foreignObject x="1" y="0" width="22" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4313_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4313)" data-figma-bg-blur-radius="3">
<mask id="path-3-inside-1_38_4313" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 3C9.23858 3 7 5.23858 7 8V10C5.34315 10 4 11.3431 4 13V18C4 19.6569 5.34315 21 7 21H17C18.6569 21 20 19.6569 20 18V13C20 11.3431 18.6569 10 17 10H9V8C9 6.34315 10.3431 5 12 5C13.6569 5 15 6.34315 15 8C15 8.55228 15.4477 9 16 9C16.5523 9 17 8.55228 17 8C17 5.23858 14.7614 3 12 3Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 3C9.23858 3 7 5.23858 7 8V10C5.34315 10 4 11.3431 4 13V18C4 19.6569 5.34315 21 7 21H17C18.6569 21 20 19.6569 20 18V13C20 11.3431 18.6569 10 17 10H9V8C9 6.34315 10.3431 5 12 5C13.6569 5 15 6.34315 15 8C15 8.55228 15.4477 9 16 9C16.5523 9 17 8.55228 17 8C17 5.23858 14.7614 3 12 3Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 3C9.23858 3 7 5.23858 7 8V10C5.34315 10 4 11.3431 4 13V18C4 19.6569 5.34315 21 7 21H17C18.6569 21 20 19.6569 20 18V13C20 11.3431 18.6569 10 17 10H9V8C9 6.34315 10.3431 5 12 5C13.6569 5 15 6.34315 15 8C15 8.55228 15.4477 9 16 9C16.5523 9 17 8.55228 17 8C17 5.23858 14.7614 3 12 3Z" fill="url(#paint1_linear_38_4313)" fill-opacity="0.2"/>
<path d="M7 10V10.2H7.2V10H7ZM9 10H8.8V10.2H9V10ZM7.2 8C7.2 5.34903 9.34903 3.2 12 3.2V2.8C9.12812 2.8 6.8 5.12812 6.8 8H7.2ZM7.2 10V8H6.8V10H7.2ZM4.2 13C4.2 11.4536 5.4536 10.2 7 10.2V9.8C5.23269 9.8 3.8 11.2327 3.8 13H4.2ZM4.2 18V13H3.8V18H4.2ZM7 20.8C5.4536 20.8 4.2 19.5464 4.2 18H3.8C3.8 19.7673 5.23269 21.2 7 21.2V20.8ZM17 20.8H7V21.2H17V20.8ZM19.8 18C19.8 19.5464 18.5464 20.8 17 20.8V21.2C18.7673 21.2 20.2 19.7673 20.2 18H19.8ZM19.8 13V18H20.2V13H19.8ZM17 10.2C18.5464 10.2 19.8 11.4536 19.8 13H20.2C20.2 11.2327 18.7673 9.8 17 9.8V10.2ZM9 10.2H17V9.8H9V10.2ZM8.8 8V10H9.2V8H8.8ZM12 4.8C10.2327 4.8 8.8 6.23269 8.8 8H9.2C9.2 6.4536 10.4536 5.2 12 5.2V4.8ZM15.2 8C15.2 6.23269 13.7673 4.8 12 4.8V5.2C13.5464 5.2 14.8 6.4536 14.8 8H15.2ZM16 8.8C15.5582 8.8 15.2 8.44183 15.2 8H14.8C14.8 8.66274 15.3373 9.2 16 9.2V8.8ZM16.8 8C16.8 8.44183 16.4418 8.8 16 8.8V9.2C16.6627 9.2 17.2 8.66274 17.2 8H16.8ZM12 3.2C14.651 3.2 16.8 5.34903 16.8 8H17.2C17.2 5.12812 14.8719 2.8 12 2.8V3.2Z" fill="url(#paint2_linear_38_4313)" fill-opacity="0.1" mask="url(#path-3-inside-1_38_4313)"/>
</g>
<g filter="url(#filter1_i_38_4313)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13 15.7324C13.5978 15.3866 14 14.7403 14 14C14 12.8954 13.1046 12 12 12C10.8954 12 10 12.8954 10 14C10 14.7403 10.4022 15.3866 11 15.7324L11 18C11 18.5523 11.4477 19 12 19C12.5523 19 13 18.5523 13 18L13 15.7324Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4313" x="1" y="0" width="22" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4313"/>
</filter>
<clipPath id="bgblur_0_38_4313_clip_path" transform="translate(-1 0)"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 3C9.23858 3 7 5.23858 7 8V10C5.34315 10 4 11.3431 4 13V18C4 19.6569 5.34315 21 7 21H17C18.6569 21 20 19.6569 20 18V13C20 11.3431 18.6569 10 17 10H9V8C9 6.34315 10.3431 5 12 5C13.6569 5 15 6.34315 15 8C15 8.55228 15.4477 9 16 9C16.5523 9 17 8.55228 17 8C17 5.23858 14.7614 3 12 3Z"/>
</clipPath><filter id="filter1_i_38_4313" x="10" y="12" width="4" height="7.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4313"/>
</filter>
<linearGradient id="paint0_linear_38_4313" x1="13" y1="7" x2="20.8" y2="14.8" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4313" x1="4" y1="3" x2="21.429" y2="18.4924" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4313" x1="4.5" y1="3.81818" x2="20" y2="3.81818" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
