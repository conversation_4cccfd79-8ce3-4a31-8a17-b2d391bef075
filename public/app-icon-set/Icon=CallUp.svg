<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="9" cy="15" r="5" fill="url(#paint0_linear_38_4127)"/>
<foreignObject x="0" y="2" width="22.8481" height="22.8481"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4127_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4127)" data-figma-bg-blur-radius="3">
<path d="M7.57934 17.2688C5.30202 14.9915 3.95412 12.6737 3.2517 10.8835C2.56813 9.14132 3.35431 7.35169 4.67765 6.02834L5.26661 5.43939C5.92203 4.78396 7.00912 4.87393 7.54787 5.62819L9.43146 8.26521C9.80399 8.78676 9.80399 9.48738 9.43146 10.0089L8.87841 10.7832C8.50649 11.3039 8.44839 11.9553 8.81458 12.48C9.25541 13.1117 9.88888 13.9215 10.4078 14.4404C10.9267 14.9593 11.7364 15.5927 12.3681 16.0336C12.8928 16.3997 13.5443 16.3416 14.0649 15.9697L14.8392 15.4167C15.3608 15.0441 16.0614 15.0441 16.5829 15.4167L19.2199 17.3003C19.9742 17.839 20.0642 18.9261 19.4087 19.5815L18.8198 20.1705C17.4964 21.4938 15.7068 22.28 13.9646 21.5964C12.1744 20.894 9.85666 19.5461 7.57934 17.2688Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M7.57934 17.2688C5.30202 14.9915 3.95412 12.6737 3.2517 10.8835C2.56813 9.14132 3.35431 7.35169 4.67765 6.02834L5.26661 5.43939C5.92203 4.78396 7.00912 4.87393 7.54787 5.62819L9.43146 8.26521C9.80399 8.78676 9.80399 9.48738 9.43146 10.0089L8.87841 10.7832C8.50649 11.3039 8.44839 11.9553 8.81458 12.48C9.25541 13.1117 9.88888 13.9215 10.4078 14.4404C10.9267 14.9593 11.7364 15.5927 12.3681 16.0336C12.8928 16.3997 13.5443 16.3416 14.0649 15.9697L14.8392 15.4167C15.3608 15.0441 16.0614 15.0441 16.5829 15.4167L19.2199 17.3003C19.9742 17.839 20.0642 18.9261 19.4087 19.5815L18.8198 20.1705C17.4964 21.4938 15.7068 22.28 13.9646 21.5964C12.1744 20.894 9.85666 19.5461 7.57934 17.2688Z" fill="url(#paint1_linear_38_4127)" fill-opacity="0.2"/>
<path d="M5.33691 5.50977C5.94864 4.89804 6.96396 4.98255 7.4668 5.68652L9.34961 8.32324C9.69731 8.81002 9.69731 9.4644 9.34961 9.95117L8.79688 10.7246C8.40494 11.2733 8.33796 11.9717 8.73242 12.5371C9.17481 13.171 9.81222 13.986 10.3369 14.5107C10.8616 15.0354 11.6766 15.6728 12.3105 16.1152C12.876 16.5098 13.5743 16.4428 14.123 16.0508L14.8975 15.498C15.3841 15.1505 16.0377 15.1506 16.5244 15.498L19.1621 17.3818C19.8658 17.8847 19.9495 18.8991 19.3379 19.5107L18.749 20.0996C17.4396 21.409 15.6905 22.1659 14.001 21.5029C12.2234 20.8054 9.9176 19.4654 7.65039 17.1982C5.38297 14.9308 4.04221 12.6243 3.34473 10.8467C2.682 9.1572 3.43874 7.40794 4.74805 6.09863L5.33691 5.50977Z" stroke="url(#paint2_linear_38_4127)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<foreignObject x="8" y="4.07117" width="11.9292" height="11.9293"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_1_38_4127_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_i_38_4127)" data-figma-bg-blur-radius="3">
<path fill-rule="evenodd" clip-rule="evenodd" d="M16 13.0005C15.4477 13.0005 15.0128 12.5458 14.8766 12.0106C14.5167 10.5968 13.4037 9.48384 11.9899 9.12392C11.4547 8.98766 11 8.55277 11 8.00049C11 7.4482 11.4511 6.99175 11.9958 7.08275C14.5117 7.503 16.4975 9.48879 16.9177 12.0047C17.0087 12.5494 16.5523 13.0005 16 13.0005Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16 13.0005C15.4477 13.0005 15.0128 12.5458 14.8766 12.0106C14.5167 10.5968 13.4037 9.48384 11.9899 9.12392C11.4547 8.98766 11 8.55277 11 8.00049C11 7.4482 11.4511 6.99175 11.9958 7.08275C14.5117 7.503 16.4975 9.48879 16.9177 12.0047C17.0087 12.5494 16.5523 13.0005 16 13.0005Z" fill="url(#paint3_linear_38_4127)" fill-opacity="0.2"/>
<path d="M11.9795 7.18115C14.4532 7.5944 16.4061 9.54732 16.8193 12.021C16.8983 12.4935 16.5025 12.9009 16 12.9009C15.5073 12.9009 15.1024 12.4917 14.9736 11.9858C14.6047 10.5366 13.4639 9.39582 12.0146 9.02686C11.5088 8.89807 11.0996 8.49323 11.0996 8.00049C11.0996 7.49798 11.507 7.10223 11.9795 7.18115Z" stroke="url(#paint4_linear_38_4127)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<foreignObject x="8.00098" y="0.0447998" width="15.9551" height="15.9552"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_2_38_4127_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_i_38_4127)" data-figma-bg-blur-radius="3">
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.001 13C19.4487 13 19.0075 12.55 18.9393 12.002C18.4888 8.38167 15.6193 5.51217 11.999 5.06165C11.4509 4.99344 11.001 4.55228 11.001 4C11.001 3.44772 11.45 2.99475 11.9996 3.04924C16.7251 3.51775 20.4832 7.27586 20.9517 12.0014C21.0062 12.551 20.5533 13 20.001 13Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.001 13C19.4487 13 19.0075 12.55 18.9393 12.002C18.4888 8.38167 15.6193 5.51217 11.999 5.06165C11.4509 4.99344 11.001 4.55228 11.001 4C11.001 3.44772 11.45 2.99475 11.9996 3.04924C16.7251 3.51775 20.4832 7.27586 20.9517 12.0014C21.0062 12.551 20.5533 13 20.001 13Z" fill="url(#paint5_linear_38_4127)" fill-opacity="0.2"/>
<path d="M11.9893 3.14844C16.6674 3.61225 20.3887 7.3336 20.8525 12.0117C20.9002 12.4956 20.5008 12.9004 20.001 12.9004C19.5067 12.9004 19.101 12.4951 19.0381 11.9893C18.5817 8.32385 15.6771 5.41923 12.0117 4.96289C11.5059 4.89994 11.1006 4.49432 11.1006 4C11.1006 3.50015 11.5053 3.10075 11.9893 3.14844Z" stroke="url(#paint6_linear_38_4127)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<defs>
<filter id="filter0_i_38_4127" x="0" y="2" width="22.8481" height="22.8481" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4127"/>
</filter>
<clipPath id="bgblur_0_38_4127_clip_path" transform="translate(0 -2)"><path d="M7.57934 17.2688C5.30202 14.9915 3.95412 12.6737 3.2517 10.8835C2.56813 9.14132 3.35431 7.35169 4.67765 6.02834L5.26661 5.43939C5.92203 4.78396 7.00912 4.87393 7.54787 5.62819L9.43146 8.26521C9.80399 8.78676 9.80399 9.48738 9.43146 10.0089L8.87841 10.7832C8.50649 11.3039 8.44839 11.9553 8.81458 12.48C9.25541 13.1117 9.88888 13.9215 10.4078 14.4404C10.9267 14.9593 11.7364 15.5927 12.3681 16.0336C12.8928 16.3997 13.5443 16.3416 14.0649 15.9697L14.8392 15.4167C15.3608 15.0441 16.0614 15.0441 16.5829 15.4167L19.2199 17.3003C19.9742 17.839 20.0642 18.9261 19.4087 19.5815L18.8198 20.1705C17.4964 21.4938 15.7068 22.28 13.9646 21.5964C12.1744 20.894 9.85666 19.5461 7.57934 17.2688Z"/>
</clipPath><filter id="filter1_i_38_4127" x="8" y="4.07117" width="11.9292" height="11.9293" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4127"/>
</filter>
<clipPath id="bgblur_1_38_4127_clip_path" transform="translate(-8 -4.07117)"><path fill-rule="evenodd" clip-rule="evenodd" d="M16 13.0005C15.4477 13.0005 15.0128 12.5458 14.8766 12.0106C14.5167 10.5968 13.4037 9.48384 11.9899 9.12392C11.4547 8.98766 11 8.55277 11 8.00049C11 7.4482 11.4511 6.99175 11.9958 7.08275C14.5117 7.503 16.4975 9.48879 16.9177 12.0047C17.0087 12.5494 16.5523 13.0005 16 13.0005Z"/>
</clipPath><filter id="filter2_i_38_4127" x="8.00098" y="0.0447998" width="15.9551" height="15.9552" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4127"/>
</filter>
<clipPath id="bgblur_2_38_4127_clip_path" transform="translate(-8.00098 -0.0447998)"><path fill-rule="evenodd" clip-rule="evenodd" d="M20.001 13C19.4487 13 19.0075 12.55 18.9393 12.002C18.4888 8.38167 15.6193 5.51217 11.999 5.06165C11.4509 4.99344 11.001 4.55228 11.001 4C11.001 3.44772 11.45 2.99475 11.9996 3.04924C16.7251 3.51775 20.4832 7.27586 20.9517 12.0014C21.0062 12.551 20.5533 13 20.001 13Z"/>
</clipPath><linearGradient id="paint0_linear_38_4127" x1="4" y1="10" x2="13.75" y2="19.75" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4127" x1="3" y1="5" x2="19.4269" y2="21.4269" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4127" x1="3.5265" y1="5.76582" x2="19.8481" y2="5.76582" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
<linearGradient id="paint3_linear_38_4127" x1="11" y1="7.00049" x2="16.85" y2="12.8505" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint4_linear_38_4127" x1="11.1875" y1="7.27322" x2="17" y2="7.27322" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
<linearGradient id="paint5_linear_38_4127" x1="11.001" y1="3" x2="20.751" y2="12.75" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint6_linear_38_4127" x1="11.3135" y1="3.45455" x2="21.001" y2="3.45455" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
