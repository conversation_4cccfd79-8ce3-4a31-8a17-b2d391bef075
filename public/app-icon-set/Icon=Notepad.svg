<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8 4C8.55228 4 9 4.44772 9 5L9 19C9 19.5523 8.55228 20 8 20L5 20C3.89543 20 3 19.1046 3 18L3 6C3 4.89543 3.89543 4 5 4L8 4Z" fill="url(#paint0_linear_38_4170)"/>
<foreignObject x="5" y="0" width="19" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4170_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4170)" data-figma-bg-blur-radius="3">
<path d="M8 5C8 4.05719 8 3.58579 8.29289 3.29289C8.58579 3 9.05719 3 10 3H17C18.8856 3 19.8284 3 20.4142 3.58579C21 4.17157 21 5.11438 21 7V17C21 18.8856 21 19.8284 20.4142 20.4142C19.8284 21 18.8856 21 17 21H10C9.05719 21 8.58579 21 8.29289 20.7071C8 20.4142 8 19.9428 8 19V5Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M8 5C8 4.05719 8 3.58579 8.29289 3.29289C8.58579 3 9.05719 3 10 3H17C18.8856 3 19.8284 3 20.4142 3.58579C21 4.17157 21 5.11438 21 7V17C21 18.8856 21 19.8284 20.4142 20.4142C19.8284 21 18.8856 21 17 21H10C9.05719 21 8.58579 21 8.29289 20.7071C8 20.4142 8 19.9428 8 19V5Z" fill="url(#paint1_linear_38_4170)" fill-opacity="0.2"/>
<path d="M10 3.09961H17C17.9456 3.09961 18.6452 3.09976 19.1816 3.17188C19.715 3.24362 20.0726 3.38509 20.3438 3.65625C20.6149 3.92741 20.7564 4.28502 20.8281 4.81836C20.9002 5.35478 20.9004 6.05436 20.9004 7V17C20.9004 17.9456 20.9002 18.6452 20.8281 19.1816C20.7564 19.715 20.6149 20.0726 20.3438 20.3438C20.0726 20.6149 19.715 20.7564 19.1816 20.8281C18.6452 20.9002 17.9456 20.9004 17 20.9004H10C9.52577 20.9004 9.18012 20.8998 8.91602 20.8643C8.65481 20.8291 8.48804 20.7615 8.36328 20.6367C8.23852 20.512 8.17086 20.3452 8.13574 20.084C8.10023 19.8199 8.09961 19.4742 8.09961 19V5C8.09961 4.52577 8.10023 4.18012 8.13574 3.91602C8.17086 3.65481 8.23852 3.48804 8.36328 3.36328C8.48804 3.23852 8.65481 3.17086 8.91602 3.13574C9.18012 3.10023 9.52577 3.09961 10 3.09961Z" stroke="url(#paint2_linear_38_4170)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4170)">
<rect x="10" y="7" width="6" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_4170)">
<rect x="10" y="11" width="9" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter3_i_38_4170)">
<rect x="10" y="15" width="9" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4170" x="5" y="0" width="19" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4170"/>
</filter>
<clipPath id="bgblur_0_38_4170_clip_path" transform="translate(-5 0)"><path d="M8 5C8 4.05719 8 3.58579 8.29289 3.29289C8.58579 3 9.05719 3 10 3H17C18.8856 3 19.8284 3 20.4142 3.58579C21 4.17157 21 5.11438 21 7V17C21 18.8856 21 19.8284 20.4142 20.4142C19.8284 21 18.8856 21 17 21H10C9.05719 21 8.58579 21 8.29289 20.7071C8 20.4142 8 19.9428 8 19V5Z"/>
</clipPath><filter id="filter1_i_38_4170" x="10" y="7" width="6" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4170"/>
</filter>
<filter id="filter2_i_38_4170" x="10" y="11" width="9" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4170"/>
</filter>
<filter id="filter3_i_38_4170" x="10" y="15" width="9" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4170"/>
</filter>
<linearGradient id="paint0_linear_38_4170" x1="9" y1="4" x2="-1.25753" y2="7.84657" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4170" x1="8" y1="3" x2="24.66" y2="15.0323" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4170" x1="8.40625" y1="3.81818" x2="21" y2="3.81818" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
