<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="13" y="20" width="16" height="3" rx="1" transform="rotate(-90 13 20)" fill="url(#paint0_linear_38_4274)"/>
<foreignObject x="0" y="3" width="24" height="18"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4274_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4274)" data-figma-bg-blur-radius="3">
<path d="M6 6C4.34315 6 3 7.34315 3 9V9.5C3 9.77614 3.22733 9.99359 3.49494 10.0617C4.36012 10.282 5 11.0663 5 12C5 12.9337 4.36012 13.718 3.49494 13.9383C3.22733 14.0064 3 14.2239 3 14.5V15C3 16.6569 4.34315 18 6 18H18C19.6569 18 21 16.6569 21 15V14.5C21 14.2239 20.7727 14.0064 20.5051 13.9383C19.6399 13.718 19 12.9337 19 12C19 11.0663 19.6399 10.282 20.5051 10.0617C20.7727 9.99359 21 9.77614 21 9.5V9C21 7.34315 19.6569 6 18 6H6Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M6 6C4.34315 6 3 7.34315 3 9V9.5C3 9.77614 3.22733 9.99359 3.49494 10.0617C4.36012 10.282 5 11.0663 5 12C5 12.9337 4.36012 13.718 3.49494 13.9383C3.22733 14.0064 3 14.2239 3 14.5V15C3 16.6569 4.34315 18 6 18H18C19.6569 18 21 16.6569 21 15V14.5C21 14.2239 20.7727 14.0064 20.5051 13.9383C19.6399 13.718 19 12.9337 19 12C19 11.0663 19.6399 10.282 20.5051 10.0617C20.7727 9.99359 21 9.77614 21 9.5V9C21 7.34315 19.6569 6 18 6H6Z" fill="url(#paint1_linear_38_4274)" fill-opacity="0.2"/>
<path d="M6 6.09961H18C19.6016 6.09961 20.9004 7.39837 20.9004 9V9.5C20.9004 9.71658 20.7187 9.90417 20.4805 9.96484C19.572 10.1961 18.9004 11.0194 18.9004 12C18.9004 12.9806 19.572 13.8039 20.4805 14.0352C20.7187 14.0958 20.9004 14.2834 20.9004 14.5V15C20.9004 16.6016 19.6016 17.9004 18 17.9004H6C4.39837 17.9004 3.09961 16.6016 3.09961 15V14.5C3.09961 14.2834 3.28129 14.0958 3.51953 14.0352C4.42795 13.8039 5.09961 12.9806 5.09961 12C5.09961 11.0194 4.42795 10.1961 3.51953 9.96484C3.28129 9.90417 3.09961 9.71658 3.09961 9.5V9C3.09961 7.39837 4.39837 6.09961 6 6.09961Z" stroke="url(#paint2_linear_38_4274)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4274)">
<rect x="14" y="16" width="8" height="1" rx="0.5" transform="rotate(-90 14 16)" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4274" x="0" y="3" width="24" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4274"/>
</filter>
<clipPath id="bgblur_0_38_4274_clip_path" transform="translate(0 -3)"><path d="M6 6C4.34315 6 3 7.34315 3 9V9.5C3 9.77614 3.22733 9.99359 3.49494 10.0617C4.36012 10.282 5 11.0663 5 12C5 12.9337 4.36012 13.718 3.49494 13.9383C3.22733 14.0064 3 14.2239 3 14.5V15C3 16.6569 4.34315 18 6 18H18C19.6569 18 21 16.6569 21 15V14.5C21 14.2239 20.7727 14.0064 20.5051 13.9383C19.6399 13.718 19 12.9337 19 12C19 11.0663 19.6399 10.282 20.5051 10.0617C20.7727 9.99359 21 9.77614 21 9.5V9C21 7.34315 19.6569 6 18 6H6Z"/>
</clipPath><filter id="filter1_i_38_4274" x="14" y="8" width="1" height="8.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4274"/>
</filter>
<linearGradient id="paint0_linear_38_4274" x1="13" y1="20" x2="14.0596" y2="25.6513" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4274" x1="3" y1="6" x2="13.8" y2="22.2" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4274" x1="3.5625" y1="6.54545" x2="21" y2="6.54545" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
