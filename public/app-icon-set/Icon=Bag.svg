<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="12" cy="8" r="4" stroke="url(#paint0_linear_38_4343)" stroke-width="2"/>
<foreignObject x="0.35498" y="5" width="23.29" height="18"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4343_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4343)" data-figma-bg-blur-radius="3">
<path d="M3.54152 13.5017C3.75914 10.8904 3.86794 9.58471 4.72907 8.79235C5.5902 8 6.9004 8 9.5208 8H14.4792C17.0996 8 18.4098 8 19.2709 8.79235C20.1321 9.58471 20.2409 10.8904 20.4585 13.5017C20.7119 16.5423 20.8386 18.0627 19.9472 19.0313C19.0559 20 17.5304 20 14.4792 20H9.5208C6.46964 20 4.94406 20 4.05275 19.0313C3.16145 18.0627 3.28814 16.5423 3.54152 13.5017Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M3.54152 13.5017C3.75914 10.8904 3.86794 9.58471 4.72907 8.79235C5.5902 8 6.9004 8 9.5208 8H14.4792C17.0996 8 18.4098 8 19.2709 8.79235C20.1321 9.58471 20.2409 10.8904 20.4585 13.5017C20.7119 16.5423 20.8386 18.0627 19.9472 19.0313C19.0559 20 17.5304 20 14.4792 20H9.5208C6.46964 20 4.94406 20 4.05275 19.0313C3.16145 18.0627 3.28814 16.5423 3.54152 13.5017Z" fill="url(#paint1_linear_38_4343)" fill-opacity="0.2"/>
<path d="M9.52051 8.09961H14.4795C15.792 8.09961 16.7668 8.10029 17.5215 8.19824C18.2735 8.29589 18.7936 8.48944 19.2031 8.86621C19.6126 9.24298 19.8481 9.74512 20.0078 10.4863C20.1681 11.2303 20.2494 12.2017 20.3584 13.5098C20.4853 15.033 20.5798 16.1654 20.5322 17.0381C20.4848 17.9079 20.2975 18.5036 19.874 18.9639C19.4506 19.4241 18.8725 19.6607 18.0098 19.7803C17.1441 19.9002 16.0079 19.9004 14.4795 19.9004H9.52051C7.99213 19.9004 6.85592 19.9002 5.99023 19.7803C5.12749 19.6607 4.54941 19.4241 4.12598 18.9639C3.70253 18.5036 3.51522 17.9079 3.46777 17.0381C3.42017 16.1654 3.51466 15.033 3.6416 13.5098C3.75061 12.2017 3.83187 11.2303 3.99219 10.4863C4.15193 9.74512 4.38745 9.24298 4.79688 8.86621C5.20635 8.48944 5.72655 8.29589 6.47852 8.19824C7.23321 8.10029 8.208 8.09961 9.52051 8.09961Z" stroke="url(#paint2_linear_38_4343)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4343)">
<rect x="7" y="16" width="10" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4343" x="0.35498" y="5" width="23.29" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4343"/>
</filter>
<clipPath id="bgblur_0_38_4343_clip_path" transform="translate(-0.35498 -5)"><path d="M3.54152 13.5017C3.75914 10.8904 3.86794 9.58471 4.72907 8.79235C5.5902 8 6.9004 8 9.5208 8H14.4792C17.0996 8 18.4098 8 19.2709 8.79235C20.1321 9.58471 20.2409 10.8904 20.4585 13.5017C20.7119 16.5423 20.8386 18.0627 19.9472 19.0313C19.0559 20 17.5304 20 14.4792 20H9.5208C6.46964 20 4.94406 20 4.05275 19.0313C3.16145 18.0627 3.28814 16.5423 3.54152 13.5017Z"/>
</clipPath><filter id="filter1_i_38_4343" x="7" y="16" width="10" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4343"/>
</filter>
<linearGradient id="paint0_linear_38_4343" x1="7" y1="3" x2="16.75" y2="12.75" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4343" x1="3" y1="8" x2="13.8" y2="24.2" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4343" x1="3.5625" y1="8.54545" x2="21" y2="8.54545" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
