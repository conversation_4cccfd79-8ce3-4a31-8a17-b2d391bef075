<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.3929 8.79069L14.7988 6.11487C15.3302 5.79217 16 6.18771 16 6.82418V12.1758C16 12.8123 15.3302 13.2078 14.7988 12.8851L10.3929 10.2093C9.86902 9.89112 9.86902 9.10888 10.3929 8.79069Z" fill="url(#paint0_linear_38_4042)"/>
<foreignObject x="1" y="4" width="21" height="16"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4042_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4042)" data-figma-bg-blur-radius="3">
<path d="M13.1501 14.4944C13.0834 14.4559 13 14.504 13 14.581V15.8226C13 16.7318 11.9952 17.2969 11.1982 16.8359L4.5894 13.0133C3.80353 12.5587 3.80353 11.4413 4.5894 10.9867L11.1982 7.1641C11.9952 6.70309 13 7.26816 13 8.1774V9.41902C13 9.49604 13.0834 9.54415 13.1501 9.50559L17.1982 7.1641C17.9952 6.70309 19 7.26816 19 8.1774V15.8226C19 16.7318 17.9952 17.2969 17.1982 16.8359L13.1501 14.4944Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M13.1501 14.4944C13.0834 14.4559 13 14.504 13 14.581V15.8226C13 16.7318 11.9952 17.2969 11.1982 16.8359L4.5894 13.0133C3.80353 12.5587 3.80353 11.4413 4.5894 10.9867L11.1982 7.1641C11.9952 6.70309 13 7.26816 13 8.1774V9.41902C13 9.49604 13.0834 9.54415 13.1501 9.50559L17.1982 7.1641C17.9952 6.70309 19 7.26816 19 8.1774V15.8226C19 16.7318 17.9952 17.2969 17.1982 16.8359L13.1501 14.4944Z" fill="url(#paint1_linear_38_4042)" fill-opacity="0.2"/>
<path d="M17.248 7.25098C17.9809 6.82708 18.9004 7.34841 18.9004 8.17773V15.8223C18.9004 16.6516 17.9809 17.1729 17.248 16.749L13.2002 14.4082C13.0669 14.3311 12.9004 14.427 12.9004 14.5811V15.8223C12.9004 16.6516 11.9809 17.1729 11.248 16.749L4.63965 12.9268C3.92036 12.5107 3.92036 11.4893 4.63965 11.0732L11.248 7.25098C11.9809 6.82708 12.9004 7.34841 12.9004 8.17773V9.41895C12.9004 9.57298 13.0669 9.66892 13.2002 9.5918L17.248 7.25098Z" stroke="url(#paint2_linear_38_4042)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<defs>
<filter id="filter0_i_38_4042" x="1" y="4" width="21" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4042"/>
</filter>
<clipPath id="bgblur_0_38_4042_clip_path" transform="translate(-1 -4)"><path d="M13.1501 14.4944C13.0834 14.4559 13 14.504 13 14.581V15.8226C13 16.7318 11.9952 17.2969 11.1982 16.8359L4.5894 13.0133C3.80353 12.5587 3.80353 11.4413 4.5894 10.9867L11.1982 7.1641C11.9952 6.70309 13 7.26816 13 8.1774V9.41902C13 9.49604 13.0834 9.54415 13.1501 9.50559L17.1982 7.1641C17.9952 6.70309 19 7.26816 19 8.1774V15.8226C19 16.7318 17.9952 17.2969 17.1982 16.8359L13.1501 14.4944Z"/>
</clipPath><linearGradient id="paint0_linear_38_4042" x1="10" y1="6" x2="16.7447" y2="11.7812" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4042" x1="4" y1="7" x2="13" y2="20.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4042" x1="4.46875" y1="7.45455" x2="19" y2="7.45455" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
