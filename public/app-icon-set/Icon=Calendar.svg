<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7 5C7 4.44772 7.44772 4 8 4H9C9.55228 4 10 4.44772 10 5V9C10 9.55228 9.55228 10 9 10H8C7.44772 10 7 9.55228 7 9V5Z" fill="url(#paint0_linear_38_3996)"/>
<path d="M14 5C14 4.44772 14.4477 4 15 4H16C16.5523 4 17 4.44772 17 5V9C17 9.55228 16.5523 10 16 10H15C14.4477 10 14 9.55228 14 9V5Z" fill="url(#paint1_linear_38_3996)"/>
<foreignObject x="0" y="3" width="24" height="20"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_3996_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_3996)" data-figma-bg-blur-radius="3">
<rect x="3" y="6" width="18" height="14" rx="3" fill="#1D1D1D" fill-opacity="0.05"/>
<rect x="3" y="6" width="18" height="14" rx="3" fill="url(#paint2_linear_38_3996)" fill-opacity="0.2"/>
<rect x="3.1" y="6.1" width="17.8" height="13.8" rx="2.9" stroke="url(#paint3_linear_38_3996)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_3996)">
<rect x="5" y="15" width="4" height="3" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_3996)">
<rect x="5" y="11" width="4" height="3" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter3_i_38_3996)">
<rect x="10" y="15" width="4" height="3" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter4_i_38_3996)">
<rect x="10" y="11" width="4" height="3" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter5_i_38_3996)">
<rect x="5" y="8" width="14" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter6_i_38_3996)">
<rect x="15" y="15" width="4" height="3" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter7_i_38_3996)">
<rect x="15" y="11" width="4" height="3" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_3996" x="0" y="3" width="24" height="20" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3996"/>
</filter>
<clipPath id="bgblur_0_38_3996_clip_path" transform="translate(0 -3)"><rect x="3" y="6" width="18" height="14" rx="3"/>
</clipPath><filter id="filter1_i_38_3996" x="5" y="15" width="4" height="3.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3996"/>
</filter>
<filter id="filter2_i_38_3996" x="5" y="11" width="4" height="3.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3996"/>
</filter>
<filter id="filter3_i_38_3996" x="10" y="15" width="4" height="3.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3996"/>
</filter>
<filter id="filter4_i_38_3996" x="10" y="11" width="4" height="3.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3996"/>
</filter>
<filter id="filter5_i_38_3996" x="5" y="8" width="14" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3996"/>
</filter>
<filter id="filter6_i_38_3996" x="15" y="15" width="4" height="3.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3996"/>
</filter>
<filter id="filter7_i_38_3996" x="15" y="11" width="4" height="3.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3996"/>
</filter>
<linearGradient id="paint0_linear_38_3996" x1="7" y1="4" x2="12.1618" y2="12.6029" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_3996" x1="7" y1="4" x2="12.1618" y2="12.6029" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_3996" x1="3" y1="6" x2="16.23" y2="23.01" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint3_linear_38_3996" x1="3.5625" y1="6.63636" x2="21" y2="6.63636" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
