<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="4" y="11" width="16" height="4" rx="1" fill="url(#paint0_linear_38_4103)"/>
<foreignObject x="-2" y="5" width="28" height="12"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4103_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4103)" data-figma-bg-blur-radius="3">
<mask id="path-3-inside-1_38_4103" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 11C7 12.6569 5.65685 14 4 14C2.34315 14 1 12.6569 1 11C1 9.34315 2.34315 8 4 8C5.65685 8 7 9.34315 7 11ZM15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11C9 9.34315 10.3431 8 12 8C13.6569 8 15 9.34315 15 11ZM20 14C21.6569 14 23 12.6569 23 11C23 9.34315 21.6569 8 20 8C18.3431 8 17 9.34315 17 11C17 12.6569 18.3431 14 20 14Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 11C7 12.6569 5.65685 14 4 14C2.34315 14 1 12.6569 1 11C1 9.34315 2.34315 8 4 8C5.65685 8 7 9.34315 7 11ZM15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11C9 9.34315 10.3431 8 12 8C13.6569 8 15 9.34315 15 11ZM20 14C21.6569 14 23 12.6569 23 11C23 9.34315 21.6569 8 20 8C18.3431 8 17 9.34315 17 11C17 12.6569 18.3431 14 20 14Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 11C7 12.6569 5.65685 14 4 14C2.34315 14 1 12.6569 1 11C1 9.34315 2.34315 8 4 8C5.65685 8 7 9.34315 7 11ZM15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11C9 9.34315 10.3431 8 12 8C13.6569 8 15 9.34315 15 11ZM20 14C21.6569 14 23 12.6569 23 11C23 9.34315 21.6569 8 20 8C18.3431 8 17 9.34315 17 11C17 12.6569 18.3431 14 20 14Z" fill="url(#paint1_linear_38_4103)" fill-opacity="0.2"/>
<path d="M4 14.2C5.76731 14.2 7.2 12.7673 7.2 11H6.8C6.8 12.5464 5.5464 13.8 4 13.8V14.2ZM0.8 11C0.8 12.7673 2.23269 14.2 4 14.2V13.8C2.4536 13.8 1.2 12.5464 1.2 11H0.8ZM4 7.8C2.23269 7.8 0.8 9.23269 0.8 11H1.2C1.2 9.4536 2.4536 8.2 4 8.2V7.8ZM7.2 11C7.2 9.23269 5.76731 7.8 4 7.8V8.2C5.5464 8.2 6.8 9.4536 6.8 11H7.2ZM12 14.2C13.7673 14.2 15.2 12.7673 15.2 11H14.8C14.8 12.5464 13.5464 13.8 12 13.8V14.2ZM8.8 11C8.8 12.7673 10.2327 14.2 12 14.2V13.8C10.4536 13.8 9.2 12.5464 9.2 11H8.8ZM12 7.8C10.2327 7.8 8.8 9.23269 8.8 11H9.2C9.2 9.4536 10.4536 8.2 12 8.2V7.8ZM15.2 11C15.2 9.23269 13.7673 7.8 12 7.8V8.2C13.5464 8.2 14.8 9.4536 14.8 11H15.2ZM22.8 11C22.8 12.5464 21.5464 13.8 20 13.8V14.2C21.7673 14.2 23.2 12.7673 23.2 11H22.8ZM20 8.2C21.5464 8.2 22.8 9.4536 22.8 11H23.2C23.2 9.23269 21.7673 7.8 20 7.8V8.2ZM17.2 11C17.2 9.4536 18.4536 8.2 20 8.2V7.8C18.2327 7.8 16.8 9.23269 16.8 11H17.2ZM20 13.8C18.4536 13.8 17.2 12.5464 17.2 11H16.8C16.8 12.7673 18.2327 14.2 20 14.2V13.8Z" fill="url(#paint2_linear_38_4103)" fill-opacity="0.1" mask="url(#path-3-inside-1_38_4103)"/>
</g>
<defs>
<filter id="filter0_i_38_4103" x="-2" y="5" width="28" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4103"/>
</filter>
<clipPath id="bgblur_0_38_4103_clip_path" transform="translate(2 -5)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 11C7 12.6569 5.65685 14 4 14C2.34315 14 1 12.6569 1 11C1 9.34315 2.34315 8 4 8C5.65685 8 7 9.34315 7 11ZM15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11C9 9.34315 10.3431 8 12 8C13.6569 8 15 9.34315 15 11ZM20 14C21.6569 14 23 12.6569 23 11C23 9.34315 21.6569 8 20 8C18.3431 8 17 9.34315 17 11C17 12.6569 18.3431 14 20 14Z"/>
</clipPath><linearGradient id="paint0_linear_38_4103" x1="4" y1="11" x2="5.83529" y2="18.3412" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4103" x1="1" y1="8" x2="3.97" y2="18.89" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4103" x1="1.6875" y1="8.27273" x2="23" y2="8.27273" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
