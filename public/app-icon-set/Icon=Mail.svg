<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="5" y="4" width="14" height="6" rx="1" fill="url(#paint0_linear_38_4166)"/>
<foreignObject x="-1" y="2" width="26" height="21"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4166_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4166)" data-figma-bg-blur-radius="3">
<rect x="2" y="5" width="20" height="15" rx="3" fill="#1D1D1D" fill-opacity="0.05"/>
<rect x="2" y="5" width="20" height="15" rx="3" fill="url(#paint1_linear_38_4166)" fill-opacity="0.2"/>
<rect x="2.1" y="5.1" width="19.8" height="14.8" rx="2.9" stroke="url(#paint2_linear_38_4166)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4166)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.62038 7.67461C4.80009 7.46495 5.11574 7.44067 5.3254 7.62038L11.6746 13.0626C11.8619 13.2231 12.1382 13.2231 12.3254 13.0626L18.6746 7.62038C18.8843 7.44067 19.1999 7.46495 19.3796 7.67461C19.5593 7.88428 19.5351 8.19993 19.3254 8.37964L12.9762 13.8218C12.4145 14.3033 11.5856 14.3033 11.0238 13.8218L4.67461 8.37964C4.46495 8.19993 4.44067 7.88428 4.62038 7.67461Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4166" x="-1" y="2" width="26" height="21" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4166"/>
</filter>
<clipPath id="bgblur_0_38_4166_clip_path" transform="translate(1 -2)"><rect x="2" y="5" width="20" height="15" rx="3"/>
</clipPath><filter id="filter1_i_38_4166" x="4.5" y="7.5" width="15" height="7.18298" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4166"/>
</filter>
<linearGradient id="paint0_linear_38_4166" x1="5" y1="4" x2="9.23621" y2="13.8845" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4166" x1="2" y1="5" x2="16.04" y2="23.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4166" x1="2.625" y1="5.68182" x2="22" y2="5.68182" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
