<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.2929 17.2929L12.7071 11.7071C12.3166 11.3166 11.6834 11.3166 11.2929 11.7071L5.70711 17.2929C5.07714 17.9229 5.52331 19 6.41421 19H17.5858C18.4767 19 18.9229 17.9229 18.2929 17.2929Z" fill="url(#paint0_linear_38_3975)"/>
<foreignObject x="0" y="2" width="24" height="18"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_3975_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_3975)" data-figma-bg-blur-radius="3">
<rect x="3" y="5" width="18" height="12" rx="2" fill="#1D1D1D" fill-opacity="0.05"/>
<rect x="3" y="5" width="18" height="12" rx="2" fill="url(#paint1_linear_38_3975)" fill-opacity="0.2"/>
<rect x="3.1" y="5.1" width="17.8" height="11.8" rx="1.9" stroke="url(#paint2_linear_38_3975)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_3975)">
<circle cx="12" cy="15" r="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_3975" x="0" y="2" width="24" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3975"/>
</filter>
<clipPath id="bgblur_0_38_3975_clip_path" transform="translate(0 -2)"><rect x="3" y="5" width="18" height="12" rx="2"/>
</clipPath><filter id="filter1_i_38_3975" x="11" y="14" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_3975"/>
</filter>
<linearGradient id="paint0_linear_38_3975" x1="4" y1="11" x2="10.24" y2="23.48" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_3975" x1="3" y1="5" x2="13.8" y2="21.2" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_3975" x1="3.5625" y1="5.54545" x2="21" y2="5.54545" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
