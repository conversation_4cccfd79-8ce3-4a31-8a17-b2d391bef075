<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="14.5" cy="9.5" r="7.5" fill="url(#paint0_linear_38_4325)"/>
<foreignObject x="1" y="1" width="22" height="22"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4325_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4325)" data-figma-bg-blur-radius="3">
<rect x="4" y="4" width="16" height="16" rx="3" fill="#1D1D1D" fill-opacity="0.05"/>
<rect x="4" y="4" width="16" height="16" rx="3" fill="url(#paint1_linear_38_4325)" fill-opacity="0.2"/>
<rect x="4.1" y="4.1" width="15.8" height="15.8" rx="2.9" stroke="url(#paint2_linear_38_4325)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4325)">
<path d="M17.1971 9.70711C17.5877 9.31658 17.5877 8.68342 17.1971 8.29289C16.8066 7.90237 16.1734 7.90237 15.7829 8.29289L11.0851 12.9907L8.70711 10.6127C8.31658 10.2222 7.68342 10.2222 7.29289 10.6127C6.90237 11.0032 6.90237 11.6364 7.29289 12.0269L10.3577 15.0917C10.3642 15.0986 10.3709 15.1055 10.3777 15.1123C10.5516 15.2862 10.7737 15.3827 11.0009 15.4017C11.0087 15.4023 11.0165 15.4029 11.0242 15.4034C11.3001 15.4202 11.5817 15.3232 11.7926 15.1123C11.7999 15.105 11.8071 15.0976 11.8141 15.0901L17.1971 9.70711Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4325" x="1" y="1" width="22" height="22" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4325"/>
</filter>
<clipPath id="bgblur_0_38_4325_clip_path" transform="translate(-1 -1)"><rect x="4" y="4" width="16" height="16" rx="3"/>
</clipPath><filter id="filter1_i_38_4325" x="7" y="8" width="10.4902" height="7.90527" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4325"/>
</filter>
<linearGradient id="paint0_linear_38_4325" x1="7" y1="2" x2="21.625" y2="16.625" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4325" x1="4" y1="4" x2="19.6" y2="19.6" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4325" x1="4.5" y1="4.72727" x2="20" y2="4.72727" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
