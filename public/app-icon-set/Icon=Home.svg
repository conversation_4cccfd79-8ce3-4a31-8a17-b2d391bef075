<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="7" y="18" width="10" height="3" rx="1" fill="url(#paint0_linear_38_4023)"/>
<foreignObject x="2" y="1.82837" width="20" height="21.1716"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4023_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4023)" data-figma-bg-blur-radius="3">
<path d="M17.2426 9.24264L14.8284 6.82843C13.4951 5.49509 12.8284 4.82843 12 4.82843C11.1716 4.82843 10.5049 5.49509 9.17157 6.82843L6.75736 9.24264C5.89027 10.1097 5.45672 10.5433 5.22836 11.0946C5 11.6459 5 12.259 5 13.4853V16C5 17.8856 5 18.8284 5.58579 19.4142C6.17157 20 7.11438 20 9 20H15C16.8856 20 17.8284 20 18.4142 19.4142C19 18.8284 19 17.8856 19 16V13.4853C19 12.259 19 11.6459 18.7716 11.0946C18.5433 10.5433 18.1097 10.1097 17.2426 9.24264Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M17.2426 9.24264L14.8284 6.82843C13.4951 5.49509 12.8284 4.82843 12 4.82843C11.1716 4.82843 10.5049 5.49509 9.17157 6.82843L6.75736 9.24264C5.89027 10.1097 5.45672 10.5433 5.22836 11.0946C5 11.6459 5 12.259 5 13.4853V16C5 17.8856 5 18.8284 5.58579 19.4142C6.17157 20 7.11438 20 9 20H15C16.8856 20 17.8284 20 18.4142 19.4142C19 18.8284 19 17.8856 19 16V13.4853C19 12.259 19 11.6459 18.7716 11.0946C18.5433 10.5433 18.1097 10.1097 17.2426 9.24264Z" fill="url(#paint1_linear_38_4023)" fill-opacity="0.2"/>
<path d="M12 4.92871C12.3836 4.92871 12.7361 5.08166 13.1641 5.4082C13.5944 5.73651 14.0892 6.23077 14.7578 6.89941L17.1719 9.31348C18.0447 10.1863 18.4607 10.6042 18.6797 11.1328C18.8986 11.6614 18.9004 12.251 18.9004 13.4854V16C18.9004 16.9456 18.9002 17.6452 18.8281 18.1816C18.7564 18.715 18.6149 19.0726 18.3438 19.3438C18.0726 19.6149 17.715 19.7564 17.1816 19.8281C16.6452 19.9002 15.9456 19.9004 15 19.9004H9C8.05436 19.9004 7.35477 19.9002 6.81836 19.8281C6.28502 19.7564 5.92741 19.6149 5.65625 19.3438C5.38509 19.0726 5.24362 18.715 5.17188 18.1816C5.09976 17.6452 5.09961 16.9456 5.09961 16V13.4854C5.09961 12.251 5.10138 11.6614 5.32031 11.1328C5.53929 10.6042 5.95529 10.1863 6.82812 9.31348L9.24219 6.89941C9.91083 6.23077 10.4056 5.73651 10.8359 5.4082C11.2639 5.08166 11.6164 4.92871 12 4.92871Z" stroke="url(#paint2_linear_38_4023)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4023)">
<rect x="8" y="16" width="8" height="2" rx="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4023" x="2" y="1.82837" width="20" height="21.1716" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4023"/>
</filter>
<clipPath id="bgblur_0_38_4023_clip_path" transform="translate(-2 -1.82837)"><path d="M17.2426 9.24264L14.8284 6.82843C13.4951 5.49509 12.8284 4.82843 12 4.82843C11.1716 4.82843 10.5049 5.49509 9.17157 6.82843L6.75736 9.24264C5.89027 10.1097 5.45672 10.5433 5.22836 11.0946C5 11.6459 5 12.259 5 13.4853V16C5 17.8856 5 18.8284 5.58579 19.4142C6.17157 20 7.11438 20 9 20H15C16.8856 20 17.8284 20 18.4142 19.4142C19 18.8284 19 17.8856 19 16V13.4853C19 12.259 19 11.6459 18.7716 11.0946C18.5433 10.5433 18.1097 10.1097 17.2426 9.24264Z"/>
</clipPath><filter id="filter1_i_38_4023" x="8" y="16" width="8" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4023"/>
</filter>
<linearGradient id="paint0_linear_38_4023" x1="7" y1="18" x2="8.61009" y2="23.367" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4023" x1="5" y1="4" x2="20.4619" y2="17.5292" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4023" x1="5.4375" y1="4.72727" x2="19" y2="4.72727" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
