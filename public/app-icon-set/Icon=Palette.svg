<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="16" cy="12" r="5" fill="url(#paint0_linear_38_4176)"/>
<foreignObject x="1" y="1" width="22" height="22.7804"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4176_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4176)" data-figma-bg-blur-radius="3">
<path d="M4 11.9688C4 6.31625 8.16 4 12 4C16.68 4 20 8.25 20 11.48C20 13.52 19.36 15.56 15.52 15.56C12.3047 15.56 11.3329 16.9902 12.2289 19.4515C12.4908 20.1709 12.009 20.9509 11.2709 20.7477C8.07228 19.8668 4 16.7215 4 11.9688Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M4 11.9688C4 6.31625 8.16 4 12 4C16.68 4 20 8.25 20 11.48C20 13.52 19.36 15.56 15.52 15.56C12.3047 15.56 11.3329 16.9902 12.2289 19.4515C12.4908 20.1709 12.009 20.9509 11.2709 20.7477C8.07228 19.8668 4 16.7215 4 11.9688Z" fill="url(#paint1_linear_38_4176)" fill-opacity="0.2"/>
<path d="M12 4.09961C14.3084 4.09961 16.2831 5.14844 17.6826 6.59863C19.0838 8.05055 19.9004 9.89681 19.9004 11.4805C19.9003 12.4934 19.7399 13.4834 19.123 14.2207C18.509 14.9546 17.4228 15.46 15.5195 15.46C13.904 15.46 12.8172 15.8185 12.2549 16.5205C11.6893 17.2267 11.6826 18.2431 12.1348 19.4854C12.2553 19.8164 12.2033 20.1574 12.043 20.3887C11.8854 20.6159 11.6227 20.7407 11.2979 20.6514C8.12368 19.7773 4.09961 16.6595 4.09961 11.9688C4.09961 9.16953 5.12923 7.20654 6.625 5.94141C8.12346 4.67403 10.0987 4.09961 12 4.09961Z" stroke="#1D1D1D" stroke-opacity="0.05" stroke-width="0.2"/>
<path d="M12 4.09961C14.3084 4.09961 16.2831 5.14844 17.6826 6.59863C19.0838 8.05055 19.9004 9.89681 19.9004 11.4805C19.9003 12.4934 19.7399 13.4834 19.123 14.2207C18.509 14.9546 17.4228 15.46 15.5195 15.46C13.904 15.46 12.8172 15.8185 12.2549 16.5205C11.6893 17.2267 11.6826 18.2431 12.1348 19.4854C12.2553 19.8164 12.2033 20.1574 12.043 20.3887C11.8854 20.6159 11.6227 20.7407 11.2979 20.6514C8.12368 19.7773 4.09961 16.6595 4.09961 11.9688C4.09961 9.16953 5.12923 7.20654 6.625 5.94141C8.12346 4.67403 10.0987 4.09961 12 4.09961Z" stroke="url(#paint2_linear_38_4176)" stroke-opacity="0.2" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4176)">
<circle cx="17" cy="12" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_4176)">
<circle cx="12" cy="7" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter3_i_38_4176)">
<circle cx="16" cy="8" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter4_i_38_4176)">
<circle cx="8" cy="8" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter5_i_38_4176)">
<circle cx="7" cy="12" r="1" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter6_i_38_4176)">
<circle cx="9" cy="16" r="1" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4176" x="1" y="1" width="22" height="22.7804" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4176"/>
</filter>
<clipPath id="bgblur_0_38_4176_clip_path" transform="translate(-1 -1)"><path d="M4 11.9688C4 6.31625 8.16 4 12 4C16.68 4 20 8.25 20 11.48C20 13.52 19.36 15.56 15.52 15.56C12.3047 15.56 11.3329 16.9902 12.2289 19.4515C12.4908 20.1709 12.009 20.9509 11.2709 20.7477C8.07228 19.8668 4 16.7215 4 11.9688Z"/>
</clipPath><filter id="filter1_i_38_4176" x="16" y="11" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4176"/>
</filter>
<filter id="filter2_i_38_4176" x="11" y="6" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4176"/>
</filter>
<filter id="filter3_i_38_4176" x="15" y="7" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4176"/>
</filter>
<filter id="filter4_i_38_4176" x="7" y="7" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4176"/>
</filter>
<filter id="filter5_i_38_4176" x="6" y="11" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4176"/>
</filter>
<filter id="filter6_i_38_4176" x="8" y="15" width="2" height="2.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4176"/>
</filter>
<linearGradient id="paint0_linear_38_4176" x1="11" y1="7" x2="20.75" y2="16.75" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4176" x1="4" y1="4" x2="20.5446" y2="19.5714" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4176" x1="4" y1="4" x2="20.5446" y2="19.5714" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
</defs>
</svg>
