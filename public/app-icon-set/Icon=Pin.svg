<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.4 18.8C7.77771 18.2964 8.37049 18 9 18H15C15.6295 18 16.2223 18.2964 16.6 18.8L17.8 20.4C18.2944 21.0592 17.824 22 17 22H7C6.17595 22 5.70557 21.0592 6.2 20.4L7.4 18.8Z" fill="url(#paint0_linear_38_4158)"/>
<foreignObject x="2" y="0" width="20" height="23"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4158_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4158)" data-figma-bg-blur-radius="3">
<path d="M19 10C19 15 13.5 20 12 20C10.5 20 5 15 5 10C5 6.13401 8.13401 3 12 3C15.866 3 19 6.13401 19 10Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M19 10C19 15 13.5 20 12 20C10.5 20 5 15 5 10C5 6.13401 8.13401 3 12 3C15.866 3 19 6.13401 19 10Z" fill="url(#paint1_linear_38_4158)" fill-opacity="0.2"/>
<path d="M12 3.09961C15.8108 3.09961 18.9004 6.18924 18.9004 10C18.9004 12.4661 17.5413 14.9434 15.9854 16.8105C15.2085 17.7428 14.3858 18.5188 13.666 19.0605C13.3061 19.3314 12.9734 19.5426 12.6865 19.6855C12.3969 19.8298 12.164 19.9004 12 19.9004C11.836 19.9004 11.6031 19.8298 11.3135 19.6855C11.0266 19.5426 10.6939 19.3314 10.334 19.0605C9.61419 18.5188 8.79149 17.7428 8.01465 16.8105C6.45868 14.9434 5.09961 12.4661 5.09961 10C5.09961 6.18924 8.18924 3.09961 12 3.09961Z" stroke="url(#paint2_linear_38_4158)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4158)">
<circle cx="12" cy="10" r="3" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4158" x="2" y="0" width="20" height="23" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4158"/>
</filter>
<clipPath id="bgblur_0_38_4158_clip_path" transform="translate(-2 0)"><path d="M19 10C19 15 13.5 20 12 20C10.5 20 5 15 5 10C5 6.13401 8.13401 3 12 3C15.866 3 19 6.13401 19 10Z"/>
</clipPath><filter id="filter1_i_38_4158" x="9" y="7" width="6" height="6.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4158"/>
</filter>
<linearGradient id="paint0_linear_38_4158" x1="5" y1="18" x2="7.06038" y2="25.2113" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4158" x1="5" y1="3" x2="21.2674" y2="16.3967" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4158" x1="5.4375" y1="3.77273" x2="19" y2="3.77273" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
