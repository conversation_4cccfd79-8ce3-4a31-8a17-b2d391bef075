<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="12" cy="12" r="5" fill="url(#paint0_linear_38_4386)"/>
<foreignObject x="1" y="1" width="22" height="22"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4386_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4386)" data-figma-bg-blur-radius="3">
<mask id="path-3-inside-1_38_4386" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 7C10 8.65685 8.65685 10 7 10C5.34315 10 4 8.65685 4 7C4 5.34315 5.34315 4 7 4C8.65685 4 10 5.34315 10 7ZM20 17C20 18.6569 18.6569 20 17 20C15.3431 20 14 18.6569 14 17C14 15.3431 15.3431 14 17 14C18.6569 14 20 15.3431 20 17ZM18.6857 4.6068C18.0999 4.02102 17.1501 4.02101 16.5643 4.6068L4.60673 16.5644C4.02094 17.1502 4.02094 18.0999 4.60673 18.6857L5.31384 19.3928C5.89962 19.9786 6.84937 19.9786 7.43515 19.3928L19.3928 7.43523C19.9786 6.84944 19.9786 5.8997 19.3928 5.31391L18.6857 4.6068Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 7C10 8.65685 8.65685 10 7 10C5.34315 10 4 8.65685 4 7C4 5.34315 5.34315 4 7 4C8.65685 4 10 5.34315 10 7ZM20 17C20 18.6569 18.6569 20 17 20C15.3431 20 14 18.6569 14 17C14 15.3431 15.3431 14 17 14C18.6569 14 20 15.3431 20 17ZM18.6857 4.6068C18.0999 4.02102 17.1501 4.02101 16.5643 4.6068L4.60673 16.5644C4.02094 17.1502 4.02094 18.0999 4.60673 18.6857L5.31384 19.3928C5.89962 19.9786 6.84937 19.9786 7.43515 19.3928L19.3928 7.43523C19.9786 6.84944 19.9786 5.8997 19.3928 5.31391L18.6857 4.6068Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 7C10 8.65685 8.65685 10 7 10C5.34315 10 4 8.65685 4 7C4 5.34315 5.34315 4 7 4C8.65685 4 10 5.34315 10 7ZM20 17C20 18.6569 18.6569 20 17 20C15.3431 20 14 18.6569 14 17C14 15.3431 15.3431 14 17 14C18.6569 14 20 15.3431 20 17ZM18.6857 4.6068C18.0999 4.02102 17.1501 4.02101 16.5643 4.6068L4.60673 16.5644C4.02094 17.1502 4.02094 18.0999 4.60673 18.6857L5.31384 19.3928C5.89962 19.9786 6.84937 19.9786 7.43515 19.3928L19.3928 7.43523C19.9786 6.84944 19.9786 5.8997 19.3928 5.31391L18.6857 4.6068Z" fill="url(#paint1_linear_38_4386)" fill-opacity="0.2"/>
<path d="M16.5643 4.6068L16.4229 4.46538V4.46538L16.5643 4.6068ZM18.6857 4.6068L18.8271 4.46538V4.46538L18.6857 4.6068ZM4.60673 16.5644L4.74815 16.7058L4.60673 16.5644ZM4.60673 18.6857L4.74815 18.5443L4.60673 18.6857ZM5.31384 19.3928L5.45526 19.2514H5.45526L5.31384 19.3928ZM7.43515 19.3928L7.57658 19.5343L7.43515 19.3928ZM19.3928 7.43523L19.2513 7.29381L19.3928 7.43523ZM19.3928 5.31391L19.2513 5.45533V5.45533L19.3928 5.31391ZM7 10.2C8.76731 10.2 10.2 8.76731 10.2 7H9.8C9.8 8.5464 8.5464 9.8 7 9.8V10.2ZM3.8 7C3.8 8.76731 5.23269 10.2 7 10.2V9.8C5.4536 9.8 4.2 8.5464 4.2 7H3.8ZM7 3.8C5.23269 3.8 3.8 5.23269 3.8 7H4.2C4.2 5.4536 5.4536 4.2 7 4.2V3.8ZM10.2 7C10.2 5.23269 8.76731 3.8 7 3.8V4.2C8.5464 4.2 9.8 5.4536 9.8 7H10.2ZM17 20.2C18.7673 20.2 20.2 18.7673 20.2 17H19.8C19.8 18.5464 18.5464 19.8 17 19.8V20.2ZM13.8 17C13.8 18.7673 15.2327 20.2 17 20.2V19.8C15.4536 19.8 14.2 18.5464 14.2 17H13.8ZM17 13.8C15.2327 13.8 13.8 15.2327 13.8 17H14.2C14.2 15.4536 15.4536 14.2 17 14.2V13.8ZM20.2 17C20.2 15.2327 18.7673 13.8 17 13.8V14.2C18.5464 14.2 19.8 15.4536 19.8 17H20.2ZM16.7058 4.74822C17.2134 4.24054 18.0366 4.24054 18.5442 4.74822L18.8271 4.46538C18.1632 3.80149 17.0868 3.80149 16.4229 4.46538L16.7058 4.74822ZM4.74815 16.7058L16.7058 4.74822L16.4229 4.46538L4.46531 16.423L4.74815 16.7058ZM4.74815 18.5443C4.24047 18.0366 4.24047 17.2135 4.74815 16.7058L4.46531 16.423C3.80142 17.0869 3.80142 18.1633 4.46531 18.8272L4.74815 18.5443ZM5.45526 19.2514L4.74815 18.5443L4.46531 18.8272L5.17241 19.5343L5.45526 19.2514ZM7.29373 19.2514C6.78605 19.7591 5.96294 19.7591 5.45526 19.2514L5.17241 19.5343C5.83631 20.1982 6.91268 20.1982 7.57658 19.5343L7.29373 19.2514ZM19.2513 7.29381L7.29373 19.2514L7.57658 19.5343L19.5342 7.57665L19.2513 7.29381ZM19.2513 5.45533C19.759 5.96301 19.759 6.78613 19.2513 7.29381L19.5342 7.57665C20.1981 6.91276 20.1981 5.83638 19.5342 5.17249L19.2513 5.45533ZM18.5442 4.74822L19.2513 5.45533L19.5342 5.17249L18.8271 4.46538L18.5442 4.74822Z" fill="url(#paint2_linear_38_4386)" fill-opacity="0.1" mask="url(#path-3-inside-1_38_4386)"/>
</g>
<defs>
<filter id="filter0_i_38_4386" x="1" y="1" width="22" height="22" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4386"/>
</filter>
<clipPath id="bgblur_0_38_4386_clip_path" transform="translate(-1 -1)"><path fill-rule="evenodd" clip-rule="evenodd" d="M10 7C10 8.65685 8.65685 10 7 10C5.34315 10 4 8.65685 4 7C4 5.34315 5.34315 4 7 4C8.65685 4 10 5.34315 10 7ZM20 17C20 18.6569 18.6569 20 17 20C15.3431 20 14 18.6569 14 17C14 15.3431 15.3431 14 17 14C18.6569 14 20 15.3431 20 17ZM18.6857 4.6068C18.0999 4.02102 17.1501 4.02101 16.5643 4.6068L4.60673 16.5644C4.02094 17.1502 4.02094 18.0999 4.60673 18.6857L5.31384 19.3928C5.89962 19.9786 6.84937 19.9786 7.43515 19.3928L19.3928 7.43523C19.9786 6.84944 19.9786 5.8997 19.3928 5.31391L18.6857 4.6068Z"/>
</clipPath><linearGradient id="paint0_linear_38_4386" x1="7" y1="7" x2="16.75" y2="16.75" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4386" x1="4" y1="4" x2="19.6" y2="19.6" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4386" x1="4.5" y1="4.72727" x2="20" y2="4.72727" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
