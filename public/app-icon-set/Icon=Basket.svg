<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6 12L9 6M18 12L15 6" stroke="url(#paint0_linear_38_4255)" stroke-width="2" stroke-linecap="round"/>
<foreignObject x="-0.291016" y="6" width="24.5913" height="17.1163"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_38_4255_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_38_4255)" data-figma-bg-blur-radius="3">
<path d="M4.2046 17.0835L3.37795 14.0525C2.74514 11.7322 2.42873 10.572 3.02909 9.78601C3.62944 9 4.83196 9 7.237 9H16.7806C19.1785 9 20.3774 9 20.9778 9.78343C21.5782 10.5669 21.2665 11.7245 20.6431 14.0399L19.8054 17.1513C19.4184 18.5888 19.2249 19.3075 18.6799 19.7208C18.135 20.134 17.3907 20.1264 15.9021 20.1112L8.02284 20.0308C6.56808 20.016 5.84071 20.0086 5.30975 19.5987C4.77878 19.1889 4.58739 18.4871 4.2046 17.0835Z" fill="#1D1D1D" fill-opacity="0.05"/>
<path d="M4.2046 17.0835L3.37795 14.0525C2.74514 11.7322 2.42873 10.572 3.02909 9.78601C3.62944 9 4.83196 9 7.237 9H16.7806C19.1785 9 20.3774 9 20.9778 9.78343C21.5782 10.5669 21.2665 11.7245 20.6431 14.0399L19.8054 17.1513C19.4184 18.5888 19.2249 19.3075 18.6799 19.7208C18.135 20.134 17.3907 20.1264 15.9021 20.1112L8.02284 20.0308C6.56808 20.016 5.84071 20.0086 5.30975 19.5987C4.77878 19.1889 4.58739 18.4871 4.2046 17.0835Z" fill="url(#paint1_linear_38_4255)" fill-opacity="0.2"/>
<path d="M7.2373 9.09961H16.7803C17.9823 9.09961 18.8734 9.10063 19.5391 9.19727C20.202 9.29355 20.6212 9.48298 20.8984 9.84473C21.1755 10.2064 21.2493 10.6601 21.1699 11.3252C21.0902 11.993 20.8593 12.8531 20.5469 14.0137L19.709 17.125C19.5149 17.8461 19.3711 18.379 19.209 18.7832C19.0479 19.1848 18.8718 19.449 18.6191 19.6406C18.3664 19.8323 18.0641 19.9308 17.6338 19.9775C17.2009 20.0246 16.6498 20.0193 15.9033 20.0117L8.02344 19.9307C7.29413 19.9232 6.75576 19.9173 6.33301 19.8633C5.91281 19.8096 5.61731 19.7095 5.37109 19.5195C5.12489 19.3295 4.95231 19.0696 4.79395 18.6768C4.6346 18.2814 4.49276 17.7615 4.30078 17.0576L3.47461 14.0264C3.15743 12.8634 2.92293 12.0014 2.84082 11.332C2.75907 10.6652 2.8312 10.2096 3.1084 9.84668C3.38563 9.48371 3.80581 9.29386 4.4707 9.19727C5.13812 9.10031 6.03163 9.09961 7.2373 9.09961Z" stroke="url(#paint2_linear_38_4255)" stroke-opacity="0.1" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_i_38_4255)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 10.5C12.2761 10.5 12.5 10.7239 12.5 11V18C12.5 18.2761 12.2761 18.5 12 18.5C11.7239 18.5 11.5 18.2761 11.5 18V11C11.5 10.7239 11.7239 10.5 12 10.5Z" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_i_38_4255)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.0708 10.505C17.3441 10.544 17.5341 10.7973 17.495 11.0707L16.495 18.0707C16.456 18.344 16.2027 18.534 15.9294 18.4949C15.656 18.4559 15.466 18.2026 15.5051 17.9292L16.5051 10.9292C16.5441 10.6559 16.7974 10.4659 17.0708 10.505Z" fill="white" fill-opacity="0.1"/>
</g>
<g filter="url(#filter3_i_38_4255)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.92923 10.505C6.65586 10.544 6.46591 10.7973 6.50496 11.0707L7.50496 18.0707C7.54401 18.344 7.79728 18.534 8.07065 18.4949C8.34401 18.4559 8.53396 18.2026 8.49491 17.9292L7.49491 10.9292C7.45586 10.6559 7.20259 10.4659 6.92923 10.505Z" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_38_4255" x="-0.291016" y="6" width="24.5913" height="17.1163" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4255"/>
</filter>
<clipPath id="bgblur_0_38_4255_clip_path" transform="translate(0.291016 -6)"><path d="M4.2046 17.0835L3.37795 14.0525C2.74514 11.7322 2.42873 10.572 3.02909 9.78601C3.62944 9 4.83196 9 7.237 9H16.7806C19.1785 9 20.3774 9 20.9778 9.78343C21.5782 10.5669 21.2665 11.7245 20.6431 14.0399L19.8054 17.1513C19.4184 18.5888 19.2249 19.3075 18.6799 19.7208C18.135 20.134 17.3907 20.1264 15.9021 20.1112L8.02284 20.0308C6.56808 20.016 5.84071 20.0086 5.30975 19.5987C4.77878 19.1889 4.58739 18.4871 4.2046 17.0835Z"/>
</clipPath><filter id="filter1_i_38_4255" x="11.5" y="10.5" width="1" height="8.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4255"/>
</filter>
<filter id="filter2_i_38_4255" x="15.5" y="10.4999" width="2" height="8.50012" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4255"/>
</filter>
<filter id="filter3_i_38_4255" x="6.5" y="10.4999" width="2" height="8.50012" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_4255"/>
</filter>
<linearGradient id="paint0_linear_38_4255" x1="6" y1="6" x2="10.68" y2="15.36" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint1_linear_38_4255" x1="2" y1="9" x2="11.2383" y2="25.5815" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF2B5"/>
<stop offset="1" stop-color="#26AAD4"/>
</linearGradient>
<linearGradient id="paint2_linear_38_4255" x1="2.625" y1="9.50649" x2="22" y2="9.50649" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADECB8"/>
<stop offset="1" stop-color="#2FAED3"/>
</linearGradient>
</defs>
</svg>
