# Heey - Technical Overview

Heey is a full stack platform for creating and chatting with personalized 3D avatars. The application is built with **React 18** and **TypeScript** using **Vite** for bundling. It integrates with **Ready Player Me** for avatar generation and **Supabase** for authentication, database and storage.

## Key Features

- **AI‑assisted Chat** – visitors can interact with avatars and receive AI responses.
- **Ready Player Me Integration** – users create or upload avatars directly from the web interface.
- **3D Rendering** – avatars and immersive scenes rendered with React Three Fiber and Google Model Viewer.
- **Profile & Content Management** – Supabase database schema covers profiles, subscriptions, chats, spaces and more.
- **i18n** – UI text available in multiple languages via `i18next`.
- **Responsive Design** – Tailwind CSS, shadcn/ui components and custom glass‑morphic styling.

## Directory Structure

- `src/` – React components, hooks, pages and routes.
  - `components/` – reusable UI and 3D avatar controls.
  - `hooks/` – custom hooks (e.g. `useAvatarManager`).
  - `routes/` – top‑level pages such as `Avatar`, `Dashboard`, `Room`.
  - `contexts/` – React context providers including authentication.
  - `i18n/` – language definitions and configuration.
- `supabase/` – database migrations and serverless functions for the API.
- `public/` – static assets and the base `index.html`.

## Getting Started

1. **Install dependencies** (Node 18+ recommended)
   ```bash
   npm install --legacy-peer-deps
   ```
2. **Start the dev server**
   ```bash
   npm run dev
   ```
3. **Build for production**
   ```bash
   npm run build
   ```
4. **Run linter**
   ```bash
   npm run lint
   ```
   Linting currently reports several `@typescript-eslint` errors which should be addressed over time.

### Environment Variables

Create a `.env` file in the project root and provide the following keys:
```env
VITE_API_BASE_URL=http://localhost:3001/api
VITE_WEBSOCKET_URL=ws://localhost:3001
VITE_READY_PLAYER_ME_SUBDOMAIN=demo
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Supabase Setup

This repository contains a `supabase` directory with SQL migrations that define tables, RLS policies and storage buckets. To run Supabase locally, install the [Supabase CLI](https://supabase.com/docs/guides/cli) and execute:
```bash
supabase start
```
The functions under `supabase/functions` are deployed as edge functions and handle tasks such as avatar management.

## Contributing

1. Fork the repository and create a new branch for your feature.
2. Run `npm run lint` and `npm run build` before committing.
3. Submit a pull request describing your changes.

---
Built with ❤️ by the Heey team.
