# Design Document

## Overview

The Heey platform is a comprehensive no-code solution that enables TikTok users and influencers to create personalized 3D avatars, build biolinks-like pages, and engage with their audience through AI-powered avatar interactions. The platform is built using modern web technologies with a focus on scalability, user experience, and future token economy integration.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React/Vite App]
        B[3D Avatar Components]
        C[Chat Interface]
        D[Biolinks Pages]
        E[Payment Components]
    end
    
    subgraph "Backend Services"
        F[Supabase Database]
        G[Supabase Auth]
        H[Supabase Storage]
        I[Edge Functions]
    end
    
    subgraph "External APIs"
        J[Ready Player Me]
        K[Stripe Payments]
        L[Gemini AI]
        M[Sketchfab 3D Models]
    end
    
    A --> F
    A --> G
    B --> J
    C --> L
    E --> K
    B --> M
    I --> F
```

### Technology Stack

- **Frontend**: React 18, TypeScript, Vite, Tailwind CSS
- **UI Components**: Radix <PERSON>, shadcn/ui
- **3D Rendering**: React Three Fiber, Three.js, @google/model-viewer
- **State Management**: TanStack Query, React Context
- **Authentication**: Supabase Auth
- **Database**: Supabase (PostgreSQL)
- **Payments**: Stripe
- **Internationalization**: i18next
- **Avatar Creation**: Ready Player Me SDK
- **AI Integration**: Gemini AI services

## Components and Interfaces

### Core Components

#### 1. Avatar Management System

**AvatarManager Component**
- Handles Ready Player Me integration
- Manages avatar creation, editing, and storage
- Provides avatar preview and selection functionality

```typescript
interface AvatarData {
  id: string;
  user_id: string;
  rpm_avatar_id: string;
  avatar_url: string;
  thumbnail_url: string;
  name: string;
  is_active: boolean;
  metadata: Record<string, any>;
}

interface AvatarManagerProps {
  userId: string;
  onAvatarCreated: (avatar: AvatarData) => void;
  onAvatarSelected: (avatar: AvatarData) => void;
}
```

#### 2. 3D Environment System

**Avatar3D Component**
- Renders 3D avatars in interactive environments
- Integrates with Sketchfab GLB models for rooms
- Provides orbit controls and camera management

```typescript
interface Room3D {
  id: string;
  name: string;
  glb_url: string;
  thumbnail: string;
  category: string;
  is_premium: boolean;
}

interface Avatar3DProps {
  avatarUrl: string;
  roomData: Room3D;
  enableOrbitControls: boolean;
  onInteraction?: (event: InteractionEvent) => void;
}
```

#### 3. AI Chat System

**ChatInterface Component**
- Manages real-time chat with AI-powered avatars
- Handles message storage and retrieval
- Integrates with Gemini AI services

```typescript
interface ChatMessage {
  id: string;
  chat_id: string;
  sender_type: 'user' | 'visitor' | 'ai';
  sender_id: string;
  content: string;
  message_type: 'text' | 'image' | 'audio';
  created_at: string;
}

interface ChatSession {
  id: string;
  user_id: string;
  visitor_id: string;
  status: 'active' | 'archived' | 'deleted';
  messages: ChatMessage[];
}
```

#### 4. Biolinks System

**BioLinksPage Component**
- Renders personalized landing pages
- Displays user profile, avatar, and social links
- Tracks visitor analytics

```typescript
interface BioLinksProfile {
  username: string;
  display_name: string;
  bio: string;
  avatar_url: string;
  social_links: SocialLink[];
  is_public: boolean;
  custom_theme?: ThemeConfig;
}

interface SocialLink {
  platform: string;
  url: string;
  display_text: string;
  icon: string;
}
```

#### 5. Subscription Management

**SubscriptionManager Component**
- Handles Stripe integration for payments
- Manages subscription tiers and features
- Provides upgrade/downgrade functionality

```typescript
interface Subscription {
  id: string;
  user_id: string;
  plan_type: 'basic' | 'premium' | 'enterprise';
  status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  stripe_subscription_id: string;
  amount: number;
  currency: string;
  start_date: string;
  end_date: string;
}
```

### API Interfaces

#### Ready Player Me Integration

```typescript
interface RPMConfig {
  subdomain: string;
  quickStart: boolean;
  clearCache: boolean;
  bodyType: 'halfbody' | 'fullbody';
  language: string;
}

interface RPMAvatar {
  id: string;
  partner: string;
  modelUrl: string;
  updatedAt: string;
}
```

#### AI Service Integration

```typescript
interface AIServiceConfig {
  provider: 'gemini';
  model: string;
  apiKey: string;
  personality: PersonalityConfig;
}

interface PersonalityConfig {
  tone: string;
  style: string;
  knowledge_base: string[];
  response_length: 'short' | 'medium' | 'long';
}
```

## Data Models

### Database Schema Extensions

The existing Supabase schema supports most requirements, with the following key tables:

#### Profiles Table
```sql
-- Enhanced profiles table for biolinks functionality
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS custom_theme JSONB;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS page_views INTEGER DEFAULT 0;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS referral_code VARCHAR(20) UNIQUE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS referral_points INTEGER DEFAULT 0;
```

#### Referrals Table (New)
```sql
CREATE TABLE referrals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  referrer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  referred_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  referral_code VARCHAR(20) NOT NULL,
  points_awarded INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Rooms Table (New)
```sql
CREATE TABLE rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  glb_url TEXT NOT NULL,
  thumbnail_url TEXT,
  category VARCHAR(50),
  is_premium BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant S as Supabase
    participant R as Ready Player Me
    participant A as AI Service
    participant P as Stripe

    U->>F: Create Avatar
    F->>R: Initialize RPM SDK
    R->>F: Avatar Creation URL
    U->>R: Customize Avatar
    R->>F: Avatar Data
    F->>S: Store Avatar Info
    
    U->>F: Start Chat
    F->>S: Create Chat Session
    U->>F: Send Message
    F->>A: Process with AI
    A->>F: AI Response
    F->>S: Store Messages
    
    U->>F: Subscribe to Premium
    F->>P: Create Checkout Session
    P->>F: Payment Success
    F->>S: Update Subscription
```

## Error Handling

### Error Categories and Strategies

#### 1. Network and API Errors
- **Ready Player Me API failures**: Fallback to cached avatars or default avatar
- **AI Service timeouts**: Provide pre-defined responses or graceful degradation
- **Stripe payment failures**: Clear error messages with retry options

#### 2. Authentication Errors
- **Session expiration**: Automatic token refresh with fallback to login
- **Permission errors**: Clear messaging about required subscription tiers

#### 3. 3D Rendering Errors
- **GLB model loading failures**: Fallback to default environments
- **WebGL compatibility issues**: Provide 2D fallback interface

#### 4. Data Validation Errors
- **Profile data validation**: Client-side validation with server-side verification
- **Chat message validation**: Content filtering and length limits

### Error Handling Implementation

```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class PlatformErrorBoundary extends Component<Props, ErrorBoundaryState> {
  // Comprehensive error boundary for the entire platform
  // Includes error reporting and graceful fallbacks
}

// API Error Handler
const handleAPIError = (error: any, context: string) => {
  console.error(`${context} error:`, error);
  
  // Log to monitoring service
  logError(error, context);
  
  // Show user-friendly message
  toast({
    title: "Something went wrong",
    description: getErrorMessage(error),
    variant: "destructive"
  });
};
```

## Testing Strategy

### Testing Pyramid

#### 1. Unit Tests
- **Component testing**: React Testing Library for UI components
- **Hook testing**: Custom hooks with proper mocking
- **Utility function testing**: Pure function validation

#### 2. Integration Tests
- **API integration**: Supabase client integration tests
- **Authentication flow**: Complete auth journey testing
- **Payment flow**: Stripe integration testing (with test mode)

#### 3. End-to-End Tests
- **User journeys**: Complete user flows from registration to avatar creation
- **Cross-browser testing**: Ensure 3D rendering works across browsers
- **Mobile responsiveness**: Touch interactions and mobile-specific features

### Testing Implementation

```typescript
// Example test structure
describe('Avatar Creation Flow', () => {
  it('should create avatar successfully', async () => {
    // Mock Ready Player Me API
    // Test avatar creation component
    // Verify database storage
  });
  
  it('should handle RPM API failures gracefully', async () => {
    // Mock API failure
    // Verify fallback behavior
    // Check error messaging
  });
});

// Integration test example
describe('Chat System Integration', () => {
  it('should complete full chat flow', async () => {
    // Create test user and visitor
    // Initialize chat session
    // Send messages and verify AI responses
    // Check message storage
  });
});
```

### Performance Testing
- **3D rendering performance**: Frame rate monitoring and optimization
- **Database query optimization**: Query performance analysis
- **Bundle size optimization**: Code splitting and lazy loading

### Security Testing
- **Authentication security**: JWT token validation and refresh
- **Data privacy**: PII handling and GDPR compliance
- **API security**: Rate limiting and input validation

## Performance Considerations

### Frontend Optimization
- **Code splitting**: Route-based and component-based splitting
- **Lazy loading**: 3D models and heavy components
- **Image optimization**: WebP format with fallbacks
- **Bundle optimization**: Tree shaking and minification

### 3D Rendering Optimization
- **Model compression**: Optimized GLB files from Sketchfab
- **Level of detail (LOD)**: Multiple quality levels based on device capability
- **Texture optimization**: Compressed textures for mobile devices

### Database Optimization
- **Query optimization**: Proper indexing and query structure
- **Connection pooling**: Efficient database connection management
- **Caching strategy**: Redis for frequently accessed data

### CDN and Asset Delivery
- **Static asset CDN**: Fast delivery of 3D models and images
- **Geographic distribution**: Edge locations for global users
- **Compression**: Gzip/Brotli compression for all assets

This design provides a comprehensive foundation for the Heey platform update, ensuring scalability, maintainability, and excellent user experience while preparing for future token economy features.