
import React, { createContext, useContext, useState } from 'react';

interface HeaderVisibilityContextValue {
  isGlobalHeaderHidden: boolean;
  setGlobalHeaderHidden: (hidden: boolean) => void;
}

const HeaderVisibilityContext = createContext<HeaderVisibilityContextValue | undefined>(undefined);

export function HeaderVisibilityProvider({ children }: { children: React.ReactNode }) {
  const [isGlobalHeaderHidden, setIsGlobalHeaderHidden] = useState(false);

  return (
    <HeaderVisibilityContext.Provider value={{ 
      isGlobalHeaderHidden, 
      setGlobalHeaderHidden: setIsGlobalHeaderHidden 
    }}>
      {children}
    </HeaderVisibilityContext.Provider>
  );
}

export function useHeaderVisibility() {
  const context = useContext(HeaderVisibilityContext);
  if (!context) {
    throw new Error('useHeaderVisibility must be used within HeaderVisibilityProvider');
  }
  return context;
}
