
import React, { createContext, useContext, useEffect, useState } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';
import { getRandomProfileIcon } from '@/lib/profileIcon';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, metadata?: Record<string, unknown>) => Promise<{ error: unknown }>;
  signIn: (email: string, password: string) => Promise<{ error: unknown }>;
  signOut: () => Promise<void>;
  signInWithProvider: (provider: 'google' | 'twitter' | 'discord') => Promise<{ error: unknown }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { t } = useTranslation();

  const ensureProfileIcon = async (authUser: User) => {
    let icon = (authUser.user_metadata as Record<string, unknown>)?.profile_icon as string | undefined;

    if (!icon) {
      icon = getRandomProfileIcon();
      const { data } = await supabase.auth.updateUser({ data: { profile_icon: icon } });
      if (data?.user) {
        authUser = data.user;
        setUser(data.user);
      }
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('avatar_thumbnail')
      .eq('id', authUser.id)
      .single();

    if (profile && !profile.avatar_thumbnail) {
      await supabase.from('profiles').update({ avatar_thumbnail: icon }).eq('id', authUser.id);
    }
  };

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change:', event, session?.user?.email);
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        if (event === 'SIGNED_IN' && session?.user) {
          await ensureProfileIcon(session.user);
          toast({
            title: t('toasts.welcomeTitle'),
            description: t('toasts.welcomeDesc'),
          });
        }

        if (event === 'SIGNED_OUT') {
          console.log('User signed out - clearing state');
          setUser(null);
          setSession(null);
        }
      }
    );

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log('Initial session:', session?.user?.email);
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, [toast]);

  const signUp = async (email: string, password: string, metadata?: Record<string, unknown>) => {
    const redirectUrl = `${window.location.origin}/`;

    const icon = getRandomProfileIcon();

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: { profile_icon: icon, ...(metadata || {}) }
      }
    });

    if (data?.user) {
      await supabase.from('profiles').update({ avatar_thumbnail: icon }).eq('id', data.user.id);
    }
    
    if (error) {
      toast({
        title: t('toasts.signupFailed'),
        description: error.message,
        variant: 'destructive',
      });
    } else {
      toast({
        title: t('toasts.checkEmail'),
        description: t('toasts.signupMessage'),
      });
    }
    
    return { error };
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      toast({
        title: t('toasts.signinFailed'),
        description: error.message,
        variant: 'destructive',
      });
    }
    
    return { error };
  };

  const signOut = async () => {
    try {
      console.log('Signing out user...');
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('Sign out error:', error);
        throw error;
      }
      
      // Clear local state immediately
      setUser(null);
      setSession(null);
      
      console.log('Sign out successful');
      
    } catch (error) {
      console.error('Sign out failed:', error);
      toast({
        title: t('toasts.signoutFailed'),
        description: t('toasts.signoutError'),
        variant: 'destructive',
      });
      throw error;
    }
  };

  const signInWithProvider = async (provider: 'google' | 'twitter' | 'discord') => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/`,
      }
    });
    
    if (error) {
      toast({
        title: t('toasts.socialSigninFailed'),
        description: error.message,
        variant: 'destructive',
      });
    }
    
    return { error };
  };

  const value = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    signInWithProvider,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
