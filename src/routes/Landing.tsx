
import { MobileHeader } from '@/components/MobileHeader';
import { TikTokBackground } from '@/components/TikTokBackground';
import { Footer } from '@/components/Footer';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useMemo, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useRTL } from '@/hooks/useRTL';
import { useAuth } from '@/contexts/AuthContext';
import { useProfile } from '@/hooks/useProfile';
import { LoginModal } from '@/components/LoginModal';
import { AppIcon } from '@/components/AppIcon';
import { LightweightImageMarquee } from '@/components/LightweightImageMarquee';
import '@google/model-viewer';

export default function Landing() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile, isLoading } = useProfile();
  const [showLogin, setShowLogin] = useState(false);
  const { t } = useTranslation();
  const { isRTL } = useRTL();

  useEffect(() => {
    if (user && !isLoading) {
      if (profile?.avatar_url) {
        navigate('/topics');
      } else {
        navigate('/avatar');
      }
    }
  }, [user, profile, isLoading, navigate]);


  const features = [
    {
      icon: 'Message',
      title: t('landing.feature1Title'),
      description: t('landing.feature1Desc')
    },
    {
      icon: 'Compass',
      title: t('landing.feature2Title'),
      description: t('landing.feature2Desc')
    },
    {
      icon: 'ShieldCheck',
      title: t('landing.feature3Title'),
      description: t('landing.feature3Desc')
    },
    {
      icon: 'Arrow',
      title: t('landing.feature4Title'),
      description: t('landing.feature4Desc')
    }
  ];

  const userProfileIcons = useMemo(
    () => Array.from({ length: 42 }, (_, i) => `/userprofile/user_profile${i + 1}.svg`),
    []
  );

  const avatars = useMemo(() => {
    const shuffled = [...userProfileIcons];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled.slice(0, 20);
  }, [userProfileIcons]);

  return (
    <div className="relative min-h-screen mobile-full-screen flex flex-col overflow-x-hidden">
      <MobileHeader />

      <ErrorBoundary fallback={<div className="absolute inset-0 bg-background" />}>
        <TikTokBackground />
      </ErrorBoundary>



      <main className="flex-1 relative z-10 flex flex-col items-center justify-center text-center px-4 py-16 space-y-16 bg-transparent">
        {/* Hero Section */}
        <motion.div
          className="max-w-4xl space-y-8 bg-background/5 backdrop-blur-sm rounded-3xl p-8"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: 'easeOut' }}
        >
          <motion.div
          className={`inline-flex items-center space-x-2 bg-background/10 backdrop-blur-lg px-4 py-2 rounded-full text-sm font-medium border border-white/5 ${isRTL ? 'space-x-reverse' : ''}`}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <AppIcon name="Star" alt="Sparkles" className="w-4 h-4 text-primary" />
            <span>{t('landing.tagline')}</span>
          </motion.div>

          <motion.h1
            className="heading-xl lg:text-6xl xl:text-7xl font-bold brand-gradient text-transparent bg-clip-text leading-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {t('landing.titleLine1')}
            <br />
            <span className="text-foreground">{t('landing.titleLine2')}</span>
          </motion.h1>

          <motion.p
            className="body-lg lg:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            {t('landing.description')}
          </motion.p>

          <div className="relative w-full h-screen bg-transparent">
            <model-viewer
              src="/assets/models/mita_avatar.glb"
              camera-controls
              auto-rotate
              ar
              loading="lazy"
              style={{ width: '100%', height: '100%' }}
            ></model-viewer>
          </div>

          <motion.div 
          className={`flex flex-col sm:flex-row items-center justify-center gap-4 ${isRTL ? 'space-x-reverse' : ''}`}
            initial={{ opacity: 0, y: 20 }} 
            animate={{ opacity: 1, y: 0 }} 
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <Button
              size="lg"
              className="btn-primary text-lg px-8 py-4 h-14"
              onClick={() => {
                if (user) {
                  navigate('/avatar');
                } else {
                  setShowLogin(true);
                }
              }}
            >
              <AppIcon name="Energy" alt="Start" className="w-5 h-5 mr-2" />
              {t('landing.primaryCta')}
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="bg-background/10 backdrop-blur-lg hover:bg-background/20 text-lg px-8 py-4 h-14 border border-white/10"
              onClick={() => navigate('/topics')}
            >
              {t('landing.secondaryCta')}
            </Button>
          </motion.div>
        </motion.div>

        {/* Optimized Lightweight Image Marquee */}
        <div className="space-y-4 w-screen -mx-4 bg-transparent">
          <LightweightImageMarquee images={avatars} direction="left" speed={20} />
          <LightweightImageMarquee images={avatars} direction="right" speed={22} />
          <LightweightImageMarquee images={avatars} direction="left" speed={18} />
        </div>

        {/* Features Grid */}
        <motion.div
          className="w-full max-w-6xl bg-background/5 backdrop-blur-sm p-8 rounded-3xl border border-white/5"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1 }}
        >
          <motion.h2
            className="heading-md lg:heading-lg mb-12 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            {t('landing.featuresHeading')}
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => {
              return (
                <motion.div
                  key={index}
                  className="bg-background/10 backdrop-blur-lg p-6 rounded-3xl text-left group hover:bg-background/15 transition-all duration-300 border border-white/5"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.3 + index * 0.1 }}
                  whileHover={{ 
                    scale: 1.02,
                    transition: { duration: 0.2 }
                  }}
                >
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-2xl mb-4 bg-gradient-to-br from-primary/30 via-primary/10 to-background/20 group-hover:from-primary/40 transition-colors">
                    <AppIcon name={feature.icon} alt={feature.title} className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="heading-sm mb-2">{feature.title}</h3>
                  <p className="body-sm text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="bg-background/10 backdrop-blur-lg p-8 lg:p-12 rounded-3xl max-w-3xl mx-auto text-center space-y-6 border border-white/5"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 1.8 }}
        >
          <h3 className="heading-md lg:heading-lg">
            {t('landing.ctaHeading')}
          </h3>
          <p className="body-md text-muted-foreground max-w-xl mx-auto">
            {t('landing.ctaDescription')}
          </p>
          <Button
            size="lg"
            className="btn-primary text-lg px-12 py-4 h-14"
            onClick={() => {
              if (user) {
                navigate('/avatar');
              } else {
                setShowLogin(true);
              }
            }}
          >
            <AppIcon name="Star" alt="Sparkles" className="w-5 h-5 mr-2" />
            {t('landing.ctaButton')}
          </Button>
        </motion.div>
      </main>

      <Footer />
      <LoginModal
        isOpen={showLogin}
        onClose={() => setShowLogin(false)}
        onSuccess={() => navigate('/avatar')}
      />
    </div>
  );
}
