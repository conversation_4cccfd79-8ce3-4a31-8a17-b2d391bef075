import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { AvatarSidebar } from '@/components/AvatarSidebar';
import { Outlet } from 'react-router-dom';
import { useHideGlobalHeaderOnScroll } from '@/hooks/useHideGlobalHeaderOnScroll';

const avatarSections = [
  { id: 'create', title: 'Create', description: 'Create or update your avatar', icon: 'Phoro' },
  { id: 'myavatar', title: 'My Avatar', description: 'View your personal avatar', icon: 'Person' },
  { id: 'icon', title: 'Avatar Icon', description: 'Choose your profile icon', icon: 'Picture' },
  { id: 'room-selection', title: 'Room Selection', description: 'Choose your 3D avatar room', icon: 'Home' }
];

export default function AvatarLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const headerRef = useRef<HTMLDivElement>(null);
  useHideGlobalHeaderOnScroll(headerRef);

  const staticHeaderTitle = 'Avatar';

  return (
    <div className="min-h-full flex">
      <div className="hidden md:flex relative z-10">
        <AvatarSidebar sections={avatarSections} className="w-80 border-r border-border/50" />
      </div>

      <div className="flex-1 flex flex-col min-h-full relative z-10">
        <div 
          ref={headerRef} 
          className="md:hidden sticky top-0 z-50 glass-header border-b border-border/50 p-4 transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(true)} className="glass-button">
              <AppIcon name="Hamburger" alt="Menu" className="h-10 w-10" />
            </Button>
            <h1 className="heading-md">{staticHeaderTitle}</h1>
            <div className="w-10" />
          </div>
        </div>

        <div className="flex-1">
          <Outlet />
        </div>
      </div>

      {sidebarOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <motion.div
            initial={{ x: -320 }}
            animate={{ x: 0 }}
            exit={{ x: -320 }}
            className="w-80 h-full bg-background/95 backdrop-blur-xl relative z-10"
            onClick={(e) => e.stopPropagation()}
          >
            <AvatarSidebar sections={avatarSections} onClose={() => setSidebarOpen(false)} />
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}
