export interface BlockConfig {
  position: [number, number, number];
  size: [number, number, number];
  color: number;
}

export const blocks: BlockConfig[] = [
  { position: [0, 1, -5], size: [2, 2, 2], color: 0xa66cff },
  { position: [-3, 1, -3], size: [1.5, 3, 1.5], color: 0x6f7bf7 },
  { position: [3, 1, -2], size: [1.5, 2, 1.5], color: 0xffa6e1 },
  { position: [0, 0.5, 2], size: [4, 1, 4], color: 0x5ad6ff },
];
