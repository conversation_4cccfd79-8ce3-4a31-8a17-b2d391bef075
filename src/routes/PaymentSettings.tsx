
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageHero } from '@/components/PageHero';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';

export default function PaymentSettings() {
  const { toast } = useToast();
  const { t } = useTranslation();
  const [paymentMethods] = useState([
    { id: 1, type: 'card', last4: '4242', brand: 'Visa', isDefault: true },
    { id: 2, type: 'card', last4: '5555', brand: 'Mastercard', isDefault: false },
  ]);

  const handleAddPaymentMethod = () => {
    toast({
      title: "Payment method added",
      description: "Your new payment method has been successfully added",
    });
  };

  const handleRemovePaymentMethod = (id: number) => {
    toast({
      title: "Payment method removed",
      description: "The payment method has been successfully removed",
    });
  };

  return (
    <div className="min-h-full flex flex-col">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="mobile-container max-w-4xl py-4 md:py-8"
      >
        <PageHero
          iconName="Card"
          title="Payment Settings"
          description="Manage your payment methods and billing information"
        />

        <div className="space-y-6">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {paymentMethods.map((method) => (
                <div key={method.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded"></div>
                    <div>
                      <p className="font-medium">{method.brand} •••• {method.last4}</p>
                      {method.isDefault && (
                        <p className="text-sm text-muted-foreground">Default method</p>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemovePaymentMethod(method.id)}
                  >
                    Remove
                  </Button>
                </div>
              ))}
              
              <Button onClick={handleAddPaymentMethod} className="w-full">
                Add Payment Method
              </Button>
            </CardContent>
          </Card>
        </div>
      </motion.div>
    </div>
  );
}
