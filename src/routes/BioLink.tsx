import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { BioLinksSidebar } from '@/components/BioLinksSidebar';
import { BioLinksMainView } from '@/components/BioLinksMainView';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useHideGlobalHeaderOnScroll } from '@/hooks/useHideGlobalHeaderOnScroll';

export default function BioLink() {
  const { t } = useTranslation();
  const location = useLocation();
  const headerRef = useRef<HTMLDivElement>(null);
  useHideGlobalHeaderOnScroll(headerRef);
  const bioLinksections = [
    { id: 'set-links', title: t('bioLink.sections.setLinks'), description: t('bioLink.sections.setLinks'), icon: 'Link' },
    { id: 'preview', title: t('bioLink.sections.preview'), description: t('bioLink.sections.preview'), icon: 'PlayCircle' },
    { id: 'social-share', title: t('bioLink.sections.socialShare'), description: t('bioLink.sections.socialShare'), icon: 'Share' }
  ];

  const getInitialSection = () => 'set-links';

  const [selectedSection, setSelectedSection] = useState<string | null>(getInitialSection());
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const staticHeaderTitle = "Bio Link";

  useEffect(() => {
    if (location.pathname === '/bio-link') {
      setSelectedSection('set-links');
    }
  }, [location.pathname]);

  return (
    <div className="min-h-full flex">
      <div className="hidden md:flex relative z-10">
        <BioLinksSidebar
          sections={bioLinksections}
          selectedSection={selectedSection}
          onSelectSection={setSelectedSection}
          className="w-80 border-r border-border/50"
        />
      </div>

      <div className="flex-1 flex flex-col min-h-full relative z-10">
        <div 
          ref={headerRef} 
          className="md:hidden sticky top-0 z-50 glass-header border-b border-border/50 p-4 transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(true)}
              className="glass-button"
            >
              <AppIcon name="Hamburger" alt="Menu" className="h-10 w-10" />
            </Button>
            <h1 className="heading-md">{staticHeaderTitle}</h1>
            <div className="w-10" />
          </div>
        </div>

        <div className="flex-1">
          <BioLinksMainView selectedSection={selectedSection} />
        </div>
      </div>

      {sidebarOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <motion.div
            initial={{ x: -320 }}
            animate={{ x: 0 }}
            exit={{ x: -320 }}
            className="w-80 h-full bg-background/95 backdrop-blur-xl relative z-10"
            onClick={(e) => e.stopPropagation()}
          >
            <BioLinksSidebar
              sections={bioLinksections}
              selectedSection={selectedSection}
              onSelectSection={(sectionId) => {
                setSelectedSection(sectionId);
                setSidebarOpen(false);
              }}
              onClose={() => setSidebarOpen(false)}
            />
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}
