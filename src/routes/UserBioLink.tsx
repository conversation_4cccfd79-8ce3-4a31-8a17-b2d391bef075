
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MessageSquare, Users, Heart, Share2, MapPin, Calendar, Globe, Instagram, Twitter, Linkedin, Github } from 'lucide-react';
import { ChatWindow } from '@/components/ChatWindow';
import { LoginGate } from '@/components/LoginGate';
import { FirstTimeVisitorForm } from '@/components/FirstTimeVisitorForm';

interface UserProfile {
  username: string;
  displayName: string;
  bio: string;
  avatarUrl: string | null;
  location: string;
  joinDate: string;
  followers: number;
  following: number;
  links: Array<{
    title: string;
    url: string;
    type: 'website' | 'social' | 'custom';
  }>;
  socialLinks: {
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
  isOnline: boolean;
}

export default function UserBioLink() {
  const { username } = useParams();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [showChat, setShowChat] = useState(false);
  const [showProfileForm, setShowProfileForm] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);

  useEffect(() => {
    const user = localStorage.getItem('user');
    const visitorProfile = localStorage.getItem('visitor_profile');
    setIsLoggedIn(!!user);
    if (user && !visitorProfile) {
      setShowProfileForm(true);
    }

    // Mock profile data based on Myzoa layout
    setProfile({
      username: username || 'demo',
      displayName: username || 'Demo User',
      bio: 'Creating amazing digital experiences through AI and design. Passionate about technology, creativity, and connecting with amazing people.',
      avatarUrl: null,
      location: 'San Francisco, CA',
      joinDate: 'Joined January 2024',
      followers: 1234,
      following: 567,
      links: [
        { title: 'My Portfolio', url: 'https://portfolio.example.com', type: 'website' },
        { title: 'Latest Project', url: 'https://project.example.com', type: 'custom' },
        { title: 'Blog Posts', url: 'https://blog.example.com', type: 'custom' },
        { title: 'Newsletter', url: 'https://newsletter.example.com', type: 'custom' },
      ],
      socialLinks: {
        instagram: 'https://instagram.com/demo',
        twitter: 'https://twitter.com/demo',
        linkedin: 'https://linkedin.com/in/demo',
        github: 'https://github.com/demo',
      },
      isOnline: true
    });
  }, [username]);

  const handleLogin = () => {
    setIsLoggedIn(true);
    const profile = localStorage.getItem('visitor_profile');
    if (!profile) setShowProfileForm(true);
  };

  const handleSaveProfile = (name: string, linkedin: string) => {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const id = user.id || user.email || Date.now();
    localStorage.setItem('visitor_profile', JSON.stringify({ visitor_user_id: id, name, linkedin }));
    setShowProfileForm(false);
  };

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `${profile?.displayName}'s Profile`,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  if (!profile) {
    return (
      <div className="min-h-screen mobile-full-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-primary"></div>
      </div>
    );
  }

  return (
    <LoginGate onLogin={handleLogin}>
      <div className="min-h-screen mobile-full-screen bg-gradient-to-br from-background via-background/95 to-primary/5">
        {/* Header */}
        <div className="sticky top-0 z-50 glass-header border-b border-border/50 px-4 py-3">
          <div className="max-w-4xl mx-auto flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={profile.avatarUrl || undefined} />
                <AvatarFallback>{profile.displayName.charAt(0)}</AvatarFallback>
              </Avatar>
              <span className="font-semibold">{profile.displayName}</span>
              {profile.isOnline && (
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span className="caption text-muted-foreground">Online</span>
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className="glass-button"
              >
                <Share2 className="h-4 w-4" />
              </Button>
              <Button
                variant={isFollowing ? "default" : "outline"}
                size="sm"
                onClick={handleFollow}
                className={isFollowing ? "brand-gradient" : "glass-button"}
              >
                <Heart className={`h-4 w-4 mr-1 ${isFollowing ? 'fill-current' : ''}`} />
                {isFollowing ? 'Following' : 'Follow'}
              </Button>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Left Column - Profile Info */}
            <div className="lg:col-span-1 space-y-6">
              {/* Profile Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <Card className="glass-panel">
                  <CardContent className="p-6 text-center">
                    <Avatar className="h-24 w-24 mx-auto mb-4">
                      <AvatarImage src={profile.avatarUrl || undefined} />
                      <AvatarFallback className="text-2xl">{profile.displayName.charAt(0)}</AvatarFallback>
                    </Avatar>
                    
                    <h1 className="heading-md mb-2">{profile.displayName}</h1>
                    <p className="body-md text-muted-foreground mb-4">@{profile.username}</p>
                    
                    <p className="body-sm leading-relaxed mb-6">{profile.bio}</p>
                    
                    {/* Stats */}
                    <div className="flex justify-center gap-8 mb-6">
                      <div className="text-center">
                        <div className="heading-sm font-bold">{profile.followers}</div>
                        <div className="caption text-muted-foreground">Followers</div>
                      </div>
                      <div className="text-center">
                        <div className="heading-sm font-bold">{profile.following}</div>
                        <div className="caption text-muted-foreground">Following</div>
                      </div>
                    </div>

                    {/* Meta Info */}
                    <div className="space-y-2 body-sm text-muted-foreground">
                      <div className="flex items-center justify-center gap-2">
                        <MapPin className="h-4 w-4" />
                        {profile.location}
                      </div>
                      <div className="flex items-center justify-center gap-2">
                        <Calendar className="h-4 w-4" />
                        {profile.joinDate}
                      </div>
                    </div>

                    {/* Social Links */}
                    <div className="flex justify-center gap-3 mt-6">
                      {profile.socialLinks.instagram && (
                        <Button variant="ghost" size="sm" className="glass-button p-2">
                          <Instagram className="h-4 w-4" />
                        </Button>
                      )}
                      {profile.socialLinks.twitter && (
                        <Button variant="ghost" size="sm" className="glass-button p-2">
                          <Twitter className="h-4 w-4" />
                        </Button>
                      )}
                      {profile.socialLinks.linkedin && (
                        <Button variant="ghost" size="sm" className="glass-button p-2">
                          <Linkedin className="h-4 w-4" />
                        </Button>
                      )}
                      {profile.socialLinks.github && (
                        <Button variant="ghost" size="sm" className="glass-button p-2">
                          <Github className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Chat Button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                <Button
                  onClick={() => setShowChat(true)}
                  className="w-full brand-gradient hover:opacity-90"
                  size="lg"
                >
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Start a Conversation
                </Button>
              </motion.div>
            </div>

            {/* Right Column - Links */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="space-y-4"
              >
                <h2 className="heading-sm mb-6">Links & Projects</h2>
                
                {profile.links.map((link, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: 0.1 * index }}
                  >
                    <Card className="glass-panel hover:glass-button transition-all duration-300 cursor-pointer group">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/40 rounded-xl flex items-center justify-center">
                              <Globe className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                              <h3 className="font-semibold group-hover:text-primary transition-colors">
                                {link.title}
                              </h3>
                              <p className="body-sm text-muted-foreground">{link.url}</p>
                            </div>
                          </div>
                          <Badge variant="secondary" className="glass-button">
                            {link.type}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          </div>
        </div>

        {/* Chat Overlay */}
        {showChat && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowChat(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="w-full max-w-2xl h-[600px] glass-panel rounded-xl overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center p-4 border-b border-border/50">
                <h3 className="font-semibold">Chat with {profile.displayName}</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowChat(false)}
                  className="glass-button"
                >
                  ×
                </Button>
              </div>
              <div className="h-[calc(600px-80px)]">
                <ChatWindow isLoggedIn={isLoggedIn} onAuthRequired={handleLogin} />
              </div>
            </motion.div>
          </motion.div>
        )}

        <FirstTimeVisitorForm isOpen={showProfileForm} onSubmit={handleSaveProfile} />
      </div>
    </LoginGate>
  );
}
