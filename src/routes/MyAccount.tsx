import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { AccountSidebar } from '@/components/AccountSidebar';
import { Outlet, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useHideGlobalHeaderOnScroll } from '@/hooks/useHideGlobalHeaderOnScroll';

export default function MyAccount() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const headerRef = useRef<HTMLDivElement>(null);
  const location = useLocation();
  const { t } = useTranslation();
  useHideGlobalHeaderOnScroll(headerRef);

  const pathTitleMap: Record<string, string> = {
    '/my-account/account': t('accountSidebar.accountSetting'),
    '/my-account/payment': t('accountSidebar.paymentSetting'),
    '/my-account/upgrade': t('accountSidebar.upgrade'),
    '/my-account/token-rewards': t('accountSidebar.tokenRewards'),
  };

  const staticHeaderTitle = "My Account";

  return (
    <div className="min-h-full bg-background flex">
      <div className="hidden md:flex">
        <AccountSidebar className="w-80 border-r border-border/50" />
      </div>

      <div className="flex-1 flex flex-col min-h-full">
        <div 
          ref={headerRef} 
          className="md:hidden sticky top-0 z-50 glass-header border-b border-border/50 p-4 transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(true)}
              className="glass-button"
            >
              <AppIcon name="Hamburger" alt="Menu" className="h-10 w-10" />
            </Button>
            <h1 className="heading-md">{staticHeaderTitle}</h1>
            <div className="w-10" />
          </div>
        </div>

        <div className="flex-1">
          <Outlet />
        </div>
      </div>

      {sidebarOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <motion.div
            initial={{ x: -320 }}
            animate={{ x: 0 }}
            exit={{ x: -320 }}
            className="w-80 h-full bg-background/95 backdrop-blur-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <AccountSidebar onClose={() => setSidebarOpen(false)} />
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}
