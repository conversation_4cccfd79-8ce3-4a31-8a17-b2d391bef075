import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { RewardsSidebar } from '@/components/RewardsSidebar';
import { RewardsMainView } from '@/components/RewardsMainView';
import { useTranslation } from 'react-i18next';
import { useHideGlobalHeaderOnScroll } from '@/hooks/useHideGlobalHeaderOnScroll';

const rewardsSections = [
  { id: 'overview', title: 'Overview', description: 'Your $HEEY balance and recent activity', icon: 'Diamond' },
  { id: 'tasks', title: 'Tasks', description: 'Complete tasks to earn $HEEY tokens', icon: 'Pig' },
  { id: 'referrals', title: 'Referrals', description: 'Invite friends and earn bonus rewards', icon: 'Send' },
  { id: 'activity', title: 'Activity', description: 'View your transaction history', icon: 'Chart' }
];

export default function TokenRewards() {
  const { t } = useTranslation();
  const [selectedSection, setSelectedSection] = useState<string | null>(rewardsSections[0].id);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const headerRef = useRef<HTMLDivElement>(null);
  useHideGlobalHeaderOnScroll(headerRef);

  const activeSection = rewardsSections.find((s) => s.id === selectedSection);
  const pageTitle = activeSection ? activeSection.title : 'Rewards';

  return (
    <div className="min-h-full flex">
      <div className="hidden md:flex">
        <RewardsSidebar
          sections={rewardsSections}
          selectedSection={selectedSection}
          onSelectSection={setSelectedSection}
          className="w-80 border-r border-border/50"
        />
      </div>

      <div className="flex-1 flex flex-col min-h-full">
        <div 
          ref={headerRef} 
          className="md:hidden sticky top-0 z-50 glass-header border-b border-border/50 p-4 transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(true)}
              className="glass-button"
            >
              <AppIcon name="Hamburger" alt="Menu" className="h-10 w-10" />
            </Button>
            <h1 className="heading-md">Rewards</h1>
            <div className="w-10" />
          </div>
        </div>

        <div className="flex-1">
          <RewardsMainView selectedSection={selectedSection} />
        </div>
      </div>

      {sidebarOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <motion.div
            initial={{ x: -320 }}
            animate={{ x: 0 }}
            exit={{ x: -320 }}
            className="w-80 h-full bg-background/95 backdrop-blur-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <RewardsSidebar
              sections={rewardsSections}
              selectedSection={selectedSection}
              onSelectSection={(sectionId) => {
                setSelectedSection(sectionId);
                setSidebarOpen(false);
              }}
              onClose={() => setSidebarOpen(false)}
            />
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}
