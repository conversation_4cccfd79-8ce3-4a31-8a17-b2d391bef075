
import { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import '@google/model-viewer';
import { TikTokBackground } from '@/components/TikTokBackground';
import { Button } from '@/components/ui/button';
import { ErrorBoundary } from '@/components/ErrorBoundary';


interface Screen {
  model?: string;
  text: string | string[];
  title?: string;
}

const screens: Screen[] = [
  {
    model: '/assets/models/mita_avatar.glb',
    title: 'Create Your Digital Twin',
    text: 'Craft a digital twin that totally slays.'
  },
  {
    title: 'Live Your Double Life',
    text: [
      'Join the hype and live that double life.',
      "Your AI twin is about to go viral." ,
    ]
  },
  {
    model: '/assets/models/onboarding_3.glb',
    title: 'Connect with <PERSON>s',
    text: 'Spin up exclusive chat rooms for your fave fans.'
  }
];

const emojiSet = ['🔥', '😍', '🎉', '🚀', '💖'];

export default function Onboarding() {
  const [step, setStep] = useState(0);
  const navigate = useNavigate();

  const emojis = useMemo(
    () =>
      new Array(8).fill(0).map(() => ({
        emoji: emojiSet[Math.floor(Math.random() * emojiSet.length)],
        top: `${Math.random() * 85 + 5}%`,
        left: `${Math.random() * 85 + 5}%`,
        size: Math.random() * 1.2 + 0.8,
        delay: Math.random() * 3,
        duration: Math.random() * 2 + 2
      })),
    []
  );

  const next = () => setStep((s) => Math.min(s + 1, screens.length - 1));
  const back = () => setStep((s) => Math.max(s - 1, 0));

  const handleSkip = () => navigate('/login');
  const handleFinish = () => navigate('/login');

  const floatingLogos = useMemo(
    () => [
      { top: '8%', left: '12%', size: 3, duration: 45, opacity: 0.04, delay: 0 },
      { top: '15%', left: '78%', size: 4, duration: 38, opacity: 0.05, delay: 2 },
      { top: '25%', left: '25%', size: 2, duration: 52, opacity: 0.03, delay: 4 },
      { top: '35%', left: '85%', size: 5, duration: 41, opacity: 0.06, delay: 1 },
      { top: '45%', left: '15%', size: 3, duration: 48, opacity: 0.04, delay: 3 },
      { top: '55%', left: '70%', size: 4, duration: 35, opacity: 0.05, delay: 5 },
      { top: '65%', left: '40%', size: 3, duration: 44, opacity: 0.04, delay: 2 },
      { top: '75%', left: '20%', size: 2, duration: 39, opacity: 0.03, delay: 4 },
    ],
    []
  );

  return (
    <div className="relative min-h-screen mobile-full-screen flex flex-col overflow-hidden bg-background">
      <ErrorBoundary fallback={<div className='absolute inset-0 bg-background' />}>
        <TikTokBackground />
      </ErrorBoundary>

      {/* Floating logos like landing page */}
      {floatingLogos.map((logo, i) => (
        <motion.img
          key={i}
          src="/lovable-uploads/4fd12ff6-660e-4077-8399-cc47ddfdbf40.png"
          alt=""
          className="pointer-events-none absolute select-none"
          style={{
            top: logo.top,
            left: logo.left,
            width: `${logo.size}rem`,
            opacity: logo.opacity,
            filter: 'blur(0.5px)'
          }}
          initial={{ 
            rotate: 0,
            scale: 0.8,
            opacity: 0
          }}
          animate={{ 
            rotate: 360,
            scale: [0.8, 1.1, 0.8],
            opacity: [0, logo.opacity, logo.opacity * 0.7, logo.opacity]
          }}
          transition={{ 
            rotate: {
              repeat: Infinity, 
              ease: 'linear', 
              duration: logo.duration
            },
            scale: {
              repeat: Infinity,
              ease: 'easeInOut',
              duration: logo.duration * 0.7
            },
            opacity: {
              repeat: Infinity,
              ease: 'easeInOut',
              duration: logo.duration * 0.5
            },
            delay: logo.delay
          }}
        />
      ))}

      {/* Floating Emojis */}
      <div className="absolute inset-0 pointer-events-none z-10">
        {emojis.map((e, i) => (
          <motion.span
            key={i}
            className="absolute select-none text-2xl"
            style={{ 
              top: e.top, 
              left: e.left, 
              fontSize: `${e.size}rem`,
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
            }}
            initial={{ opacity: 0, y: 20, scale: 0.5 }}
            animate={{ 
              opacity: [0.6, 1, 0.6], 
              y: [-10, 10, -10], 
              scale: [e.size * 0.8, e.size * 1.1, e.size * 0.8],
              rotate: [-5, 5, -5]
            }}
            transition={{ 
              repeat: Infinity, 
              duration: e.duration,
              delay: e.delay,
              ease: "easeInOut"
            }}
          >
            {e.emoji}
          </motion.span>
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-20 flex-1 flex flex-col">
        {/* Header */}
        <div className="flex justify-end items-center p-6">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleSkip} 
            className="glass-button text-muted-foreground hover:text-foreground"
          >
            Skip
          </Button>
        </div>

        {/* Content Area */}
        <div className="flex-1 flex flex-col items-center justify-center px-6 py-8">
          <AnimatePresence mode="wait">
            {screens.map((screen, index) =>
              index === step ? (
                <motion.div
                  key={index}
                  className="w-full max-w-4xl mx-auto"
                  initial={{ opacity: 0, x: 100, scale: 0.9 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  exit={{ opacity: 0, x: -100, scale: 0.9 }}
                  transition={{ 
                    duration: 0.6, 
                    ease: [0.23, 1, 0.32, 1]
                  }}
                >
                  {/* Hero Section */}
                  <motion.div
                    className="text-center space-y-8 glass-panel rounded-3xl p-8"
                    initial={{ opacity: 0, y: 40 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                  >
                    {/* Title */}
                    <motion.div
                      className="space-y-4"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: 0.4 }}
                    >
                      {screen.title && (
                        <motion.div
                          className="inline-flex items-center space-x-2 glass-button px-4 py-2 rounded-full text-sm font-medium"
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.8, delay: 0.3 }}
                        >
                          <span className="text-primary">✨</span>
                          <span>Step {step + 1} of {screens.length}</span>
                        </motion.div>
                      )}

                      <motion.h1
                        className="heading-xl lg:text-5xl font-bold brand-gradient text-transparent bg-clip-text leading-tight"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.5 }}
                      >
                        {screen.title || 'Welcome to Heey'}
                      </motion.h1>

                      {/* Text Content */}
                      <motion.div 
                        className="space-y-4 max-w-2xl mx-auto"
                        initial={{ y: 30, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.6, duration: 0.5 }}
                      >
                        {Array.isArray(screen.text) ? (
                          screen.text.map((line, i) => (
                            <motion.p 
                              key={i} 
                              className="body-lg lg:text-xl text-muted-foreground leading-relaxed"
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.7 + (0.15 * i), duration: 0.4 }}
                            >
                              {line}
                            </motion.p>
                          ))
                        ) : (
                          <motion.p 
                            className="body-lg lg:text-xl text-muted-foreground leading-relaxed"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.7, duration: 0.4 }}
                          >
                            {screen.text}
                          </motion.p>
                        )}
                      </motion.div>
                    </motion.div>

                    {/* 3D Model - Using same setup as landing page */}
                    {screen.model && (
                      <motion.div
                        className="relative w-full h-80 sm:h-96 glass-panel rounded-3xl overflow-hidden"
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ delay: 0.8, duration: 0.5 }}
                      >
                        <model-viewer
                          src={screen.model}
                          camera-controls
                          auto-rotate
                          ar
                          style={{ width: '100%', height: '100%' }}
                        ></model-viewer>
                      </motion.div>
                    )}

                    {/* Avatar Scroll Section for Step 1 */}
                    {step === 1 && (
                      <motion.div 
                        className="w-screen -mx-8 mt-8 space-y-4 overflow-hidden"
                        initial={{ opacity: 0, y: 50 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.9, duration: 0.5 }}
                      >
                        {[0, 1].map((row) => (
                          <div key={row} className="overflow-hidden w-full">
                            <div
                              className={`optimized-marquee flex items-center gap-4 whitespace-nowrap will-change-transform ${
                                row % 2 === 0 ? 'animate-scroll-left' : 'animate-scroll-right'
                              }`}
                            >
                              {Array.from({ length: 15 }, (_, i) => (
                                <motion.img
                                  key={i}
                                  src={`/userprofile/user_profile${(i % 42) + 1}.svg`}
                                  alt="avatar"
                                  loading="lazy"
                                  decoding="async"
                                  width={80}
                                  height={80}
                                  className="w-16 h-16 sm:w-20 sm:h-20 rounded-full object-cover ring-2 ring-primary/20 hover:ring-primary/40 transition-all duration-300"
                                  whileHover={{ scale: 1.1 }}
                                  transition={{ type: 'spring', stiffness: 300 }}
                                />
                              ))}
                            </div>
                          </div>
                        ))}
                      </motion.div>
                    )}

                    {/* Navigation Buttons */}
                    <motion.div 
                      className="flex items-center justify-between pt-8"
                      initial={{ opacity: 0, y: 20 }} 
                      animate={{ opacity: 1, y: 0 }} 
                      transition={{ duration: 0.8, delay: 1 }}
                    >
                      <Button 
                        variant="outline" 
                        onClick={back} 
                        disabled={step === 0}
                        className="glass-button disabled:opacity-30"
                      >
                        Back
                      </Button>

                      {step < screens.length - 1 ? (
                        <Button 
                          onClick={next}
                          className="btn-primary body-lg px-8 py-4 h-12"
                        >
                          Next ✨
                        </Button>
                      ) : (
                        <Button 
                          onClick={handleFinish}
                          className="btn-primary body-lg px-8 py-4 h-12"
                        >
                          Let's goooo! 🚀
                        </Button>
                      )}
                    </motion.div>
                  </motion.div>
                </motion.div>
              ) : null
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
