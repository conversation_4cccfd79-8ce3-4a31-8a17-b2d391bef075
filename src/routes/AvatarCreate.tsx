
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { processRPMExport } from '@/lib/avatarService';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

function AvatarCreator() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isFrameReady, setIsFrameReady] = useState(false);
  const [showManualExport, setShowManualExport] = useState(false);
  const [avatarStatus, setAvatarStatus] = useState<'loading' | 'ready' | 'draft' | 'exporting' | 'saving' | 'saved' | 'error'>('loading');

  const RPM_SUBDOMAIN = import.meta.env.VITE_READY_PLAYER_ME_SUBDOMAIN || 'demo';
  const RPM_URL = `https://${RPM_SUBDOMAIN}.readyplayer.me/avatar?frameApi&language=en&clearCache=true&bodyType=fullbody&quickStart=true`;

  console.log('RPM Configuration:', { RPM_SUBDOMAIN, RPM_URL });

  const subscribe = () => {
    const iframe = iframeRef.current;
    const events = [
      'v1.frame.ready',
      'v1.avatar.exported',
      'v1.user.set',
      'v1.avatar.draft',
      'v1.error',
    ];
    events.forEach((eventName) => {
      iframe?.contentWindow?.postMessage(
        {
          target: 'readyplayerme',
          type: 'subscribe',
          eventName,
        },
        '*'
      );
    });
  };

  const handleLoad = () => {
    setIsFrameReady(true);
    subscribe();
  };

  useEffect(() => {
    if (!user) {
      navigate('/');
      return;
    }

    // Test database connection on component mount
    const testDatabaseConnection = async () => {
      try {
        console.log('Testing database connection...');
        const { supabase } = await import('@/integrations/supabase/client');
        const { data, error } = await supabase.from('avatars').select('count').limit(1);
        if (error) {
          console.error('Database connection test failed:', error);
        } else {
          console.log('Database connection successful');
        }
      } catch (error) {
        console.error('Database connection error:', error);
      }
    };

    testDatabaseConnection();

    const handleMessage = async (event: MessageEvent) => {
      // Only process messages from Ready Player Me
      if (!event.origin.includes('readyplayer.me')) {
        console.log('Ignoring non-RPM message from:', event.origin);
        return;
      }

      console.log('RPM Event received:', event.data);

      try {
        switch (event.data?.eventName) {
          case 'v1.frame.ready':
            console.log('RPM Frame is ready');
            setIsFrameReady(true);
            setAvatarStatus('ready');
            toast({
              title: 'Avatar Creator Ready',
              description: 'You can now start creating your avatar!',
            });
            break;

          case 'v1.avatar.exported': {
            const avatarUrl = event.data?.data?.url;
            console.log('Avatar exported with URL:', avatarUrl);
            if (avatarUrl) {
              await handleAvatarSave(avatarUrl);
            } else {
              throw new Error('No avatar URL received from Ready Player Me');
            }
            break;
          }

          case 'v1.user.set':
            console.log('RPM User set:', event.data.data);
            break;

          case 'v1.avatar.draft':
            console.log('RPM Avatar draft created - export button should be available');
            setShowManualExport(true);
            setAvatarStatus('draft');
            toast({
              title: 'Avatar Draft Created',
              description: 'Your avatar is ready! Look for the Export button to save it.',
            });
            break;

          case 'v1.error':
            console.error('RPM Error:', event.data?.data);
            throw new Error(event.data?.data?.message || 'Ready Player Me error occurred');

          default:
            console.log('Unhandled RPM event:', event.data?.eventName, event.data);
        }
      } catch (error) {
        console.error('Error handling RPM message:', error);
        toast({
          title: "Avatar Creation Error",
          description: error instanceof Error ? error.message : 'Failed to process avatar creation',
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [user, navigate, toast]);

  useEffect(() => {
    const timer = setTimeout(() => setIsFrameReady(true), 10000);
    return () => clearTimeout(timer);
  }, []);

  const handleAvatarSave = async (avatarUrl: string) => {
    setIsLoading(true);
    setAvatarStatus('saving');

    try {
      console.log('Saving avatar:', avatarUrl);
      console.log('User ID:', user?.id);

      if (!user?.id) throw new Error('Not authenticated');

      // Test database connection first
      console.log('Testing database connection...');
      const result = await processRPMExport(user.id, avatarUrl);
      console.log('processRPMExport result:', result);

      if (!result.success) {
        setAvatarStatus('error');
        throw new Error(result.error?.message || 'Failed to save avatar');
      }

      setAvatarStatus('saved');
      toast({
        title: 'Avatar Created Successfully!',
        description: 'Your avatar has been saved to your account.',
      });

      // Navigate back to avatar page after a short delay
      setTimeout(() => {
        navigate('/avatar');
      }, 1500);

    } catch (error) {
      console.error('Failed to save avatar:', error);
      setAvatarStatus('error');
      toast({
        title: 'Failed to Save Avatar',
        description: error instanceof Error ? error.message : 'An error occurred while saving your avatar',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    navigate('/avatar');
  };

  const handleManualExport = () => {
    const iframe = iframeRef.current;
    if (iframe?.contentWindow) {
      // Try to trigger export via postMessage
      iframe.contentWindow.postMessage(
        {
          target: 'readyplayerme',
          type: 'export',
        },
        '*'
      );

      toast({
        title: 'Export Requested',
        description: 'Attempting to export your avatar...',
      });
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleGoBack}
          className="flex items-center gap-2"
          disabled={isLoading}
        >
          <ArrowLeft className="h-4 w-4" />
          {t('avatarCreate.back')}
        </Button>
        
        <div className="flex items-center gap-2">
          <AppIcon
            name="Phoro"
            alt={t('avatarCreate.title')}
            className="h-6 w-6 text-primary"
          />
          <div>
            <h1 className="body-lg font-semibold">{t('avatarCreate.title')}</h1>
            <p className="body-xs text-muted-foreground">
              Status: {avatarStatus === 'loading' && 'Loading...'}
              {avatarStatus === 'ready' && 'Ready to create'}
              {avatarStatus === 'draft' && 'Avatar ready to export'}
              {avatarStatus === 'saving' && 'Saving avatar...'}
              {avatarStatus === 'saved' && 'Avatar saved!'}
              {avatarStatus === 'error' && 'Error occurred'}
            </p>
          </div>
        </div>

        {/* Manual Export Button and Test Button */}
        <div className="flex items-center gap-2">
          {showManualExport && (
            <Button
              onClick={handleManualExport}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              Export Avatar
            </Button>
          )}
          {/* Test button for development */}
          {process.env.NODE_ENV === 'development' && (
            <Button
              onClick={() => handleAvatarSave('https://models.readyplayer.me/test-avatar.glb')}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              Test Save
            </Button>
          )}
        </div>}
      </div>

      {/* Loading Overlay */}
      {(isLoading || !isFrameReady) && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10"
        >
          <div className="text-center space-y-4">
            <Loader2 className="w-8 h-8 animate-spin mx-auto" />
            <p className="body-sm text-muted-foreground">
              {isLoading ? t('avatarCreate.saving') : t('avatarCreate.loading')}
            </p>
          </div>
        </motion.div>
      )}

      {/* Ready Player Me iframe */}
      <div className="flex-1 relative">
        <iframe
          ref={iframeRef}
          src={RPM_URL}
          className="w-full h-full border-none"
          allow="camera *; microphone *; fullscreen"
          title="Ready Player Me Avatar Creator"
          onLoad={handleLoad}
        />
      </div>

      {/* Instructions */}
      <div className="p-4 bg-muted/50 border-t border-border">
        <div className="text-center space-y-2">
          <p className="body-sm text-muted-foreground">
            {isFrameReady
              ? "Customize your avatar and click the Export button when ready!"
              : "Loading avatar creator..."
            }
          </p>
          {showManualExport && (
            <p className="body-xs text-primary font-medium">
              If you don't see an Export button in the avatar creator, use the Export button above.
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

export default function AvatarCreate() {
  return <AvatarCreator />;
}
