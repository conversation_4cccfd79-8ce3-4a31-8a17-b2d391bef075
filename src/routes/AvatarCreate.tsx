
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { processRPMExport } from '@/lib/avatarService';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

function AvatarCreator() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isFrameReady, setIsFrameReady] = useState(false);

  const RPM_SUBDOMAIN = import.meta.env.VITE_READY_PLAYER_ME_SUBDOMAIN || 'demo';
  const RPM_URL = `https://${RPM_SUBDOMAIN}.readyplayer.me/avatar?frameApi&language=en&clearCache=true&bodyType=fullbody&quickStart=true`;

  const subscribe = () => {
    const iframe = iframeRef.current;
    const events = [
      'v1.frame.ready',
      'v1.avatar.exported',
      'v1.user.set',
      'v1.avatar.draft',
      'v1.error',
    ];
    events.forEach((eventName) => {
      iframe?.contentWindow?.postMessage(
        {
          target: 'readyplayerme',
          type: 'subscribe',
          eventName,
        },
        '*'
      );
    });
  };

  const handleLoad = () => {
    setIsFrameReady(true);
    subscribe();
  };

  useEffect(() => {
    if (!user) {
      navigate('/');
      return;
    }

    const handleMessage = async (event: MessageEvent) => {
      // Only process messages from Ready Player Me
      if (!event.origin.includes('readyplayer.me')) return;

      console.log('RPM Event received:', event.data);

      try {
        switch (event.data?.eventName) {
          case 'v1.frame.ready':
            console.log('RPM Frame is ready');
            setIsFrameReady(true);
            break;

          case 'v1.avatar.exported': {
            const avatarUrl = event.data?.data?.url;
            if (avatarUrl) {
              await handleAvatarSave(avatarUrl);
            } else {
              throw new Error('No avatar URL received');
            }
            break;
          }

          case 'v1.user.set':
            console.log('RPM User set:', event.data.data);
            break;

          case 'v1.avatar.draft':
            console.log('RPM Avatar draft created');
            break;

          case 'v1.error':
            throw new Error(event.data?.data?.message || 'Ready Player Me error occurred');

          default:
            console.log('Unhandled RPM event:', event.data?.eventName);
        }
      } catch (error) {
        console.error('Error handling RPM message:', error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : 'Failed to process avatar creation',
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [user, navigate, toast]);

  useEffect(() => {
    const timer = setTimeout(() => setIsFrameReady(true), 10000);
    return () => clearTimeout(timer);
  }, []);

  const handleAvatarSave = async (avatarUrl: string) => {
    setIsLoading(true);
    
    try {
      console.log('Saving avatar:', avatarUrl);
      
      if (!user?.id) throw new Error('Not authenticated');
      const result = await processRPMExport(user.id, avatarUrl);
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to save avatar');
      }
      
      toast({
        title: t('toasts.avatarCreated'),
        description: t('toasts.avatarCreatedDesc'),
      });

      // Navigate back to avatar page
      navigate('/avatar');
      
    } catch (error) {
      console.error('Failed to save avatar:', error);
      toast({
        title: t('toasts.avatarSaveFailed'),
        description: error instanceof Error ? error.message : t('toasts.avatarErrorDesc'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    navigate('/avatar');
  };

  if (!user) {
    return null;
  }

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleGoBack}
          className="flex items-center gap-2"
          disabled={isLoading}
        >
          <ArrowLeft className="h-4 w-4" />
          {t('avatarCreate.back')}
        </Button>
        
        <div className="flex items-center gap-2">
          <AppIcon
            name="Phoro"
            alt={t('avatarCreate.title')}
            className="h-6 w-6 text-primary"
          />
          <h1 className="body-lg font-semibold">{t('avatarCreate.title')}</h1>
        </div>
        
        <div className="w-20" /> {/* Spacer for center alignment */}
      </div>

      {/* Loading Overlay */}
      {(isLoading || !isFrameReady) && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10"
        >
          <div className="text-center space-y-4">
            <Loader2 className="w-8 h-8 animate-spin mx-auto" />
            <p className="body-sm text-muted-foreground">
              {isLoading ? t('avatarCreate.saving') : t('avatarCreate.loading')}
            </p>
          </div>
        </motion.div>
      )}

      {/* Ready Player Me iframe */}
      <div className="flex-1 relative">
        <iframe
          ref={iframeRef}
          src={RPM_URL}
          className="w-full h-full border-none"
          allow="camera *; microphone *; fullscreen"
          title="Ready Player Me Avatar Creator"
          onLoad={handleLoad}
        />
      </div>

      {/* Instructions */}
      <div className="p-4 bg-muted/50 border-t border-border">
        <p className="body-sm text-center text-muted-foreground">
          {t('avatarCreate.instructions')}
        </p>
      </div>
    </div>
  );
}

export default function AvatarCreate() {
  return <AvatarCreator />;
}
