import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { AvatarUploadPanel } from '@/components/AvatarUploadPanel';
import { PageHero } from '@/components/PageHero';

export default function AvatarIcon() {
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  return (
    <div className="p-4 md:p-8 space-y-6">
      <PageHero
        iconName="Picture"
        title="Upload Avatar Icon"
        description="Add your custom avatar profile picture to make it truly yours."
      />

      {/* Upload Custom Avatar */}
      <Card className="glass-card w-full">
        <CardContent className="p-6">
          <AvatarUploadPanel
            avatarFile={avatarFile}
            onAvatarChange={setAvatarFile}
          />
        </CardContent>
      </Card>

    </div>
  );
}
