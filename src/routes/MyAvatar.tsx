import { PageHero } from '@/components/PageHero';
import { AvatarViewer } from '@/components/AvatarViewer';
import { useAvatarManager } from '@/hooks/useAvatarManager';
import { useTranslation } from 'react-i18next';

export default function MyAvatar() {
  const { avatarData } = useAvatarManager();
  const { t } = useTranslation();

  return (
    <div className="space-y-6 p-4 md:p-8">
      <PageHero
        iconName="Person"
        title={t('avatarPage.myAvatar')}
        description=""
      />
      <div className="w-full h-[70vh]">
        <AvatarViewer avatarUrl={avatarData?.url || ''} className="w-full h-full" />
      </div>
    </div>
  );
}
