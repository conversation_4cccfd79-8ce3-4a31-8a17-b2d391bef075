import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { KnowledgeSidebar } from '@/components/KnowledgeSidebar';
import { KnowledgeMainView } from '@/components/KnowledgeMainView';
import { useTranslation } from 'react-i18next';
import { useHideGlobalHeaderOnScroll } from '@/hooks/useHideGlobalHeaderOnScroll';

interface SocialLinks {
  twitter: string;
  instagram: string;
  linkedin: string;
  github: string;
  website: string;
}

const baseSections = [
  { id: 'documents', icon: 'Document' },
  { id: 'social', icon: 'Mail' },
  { id: 'personality', icon: 'Conversation' },
];

export default function Knowledge() {
  const { t } = useTranslation();
  const [selectedSection, setSelectedSection] = useState<string | null>(baseSections[0].id);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const headerRef = useRef<HTMLDivElement>(null);
  useHideGlobalHeaderOnScroll(headerRef);
  const knowledgeSections = baseSections.map((s) => ({
    ...s,
    title: t(`knowledgeSidebar.sections.${s.id}`),
    description: t(`knowledgeSidebar.sections.${s.id}Desc`)
  }));
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [socialLinks, setSocialLinks] = useState<SocialLinks>({
    twitter: '',
    instagram: '',
    linkedin: '',
    github: '',
    website: ''
  });
  const [chatSettings, setChatSettings] = useState({
    personality: '',
    tone: 'friendly',
    expertise: '',
  });

  const handleFilesChange = (files: File[]) => {
    setUploadedFiles(files);
  };

  const handleSocialLinksChange = (links: SocialLinks) => {
    setSocialLinks(links);
  };

  const handleChatSettingsChange = (settings: typeof chatSettings) => {
    setChatSettings(settings);
  };

  const staticHeaderTitle = "Knowledge";

  return (
    <div className="min-h-full flex">
      <div className="hidden md:flex">
        <KnowledgeSidebar
          sections={knowledgeSections}
          selectedSection={selectedSection}
          onSelectSection={setSelectedSection}
          className="w-80 border-r border-border/50"
        />
      </div>

      <div className="flex-1 flex flex-col min-h-full">
        <div 
          ref={headerRef} 
          className="md:hidden sticky top-0 z-50 glass-header border-b border-border/50 p-4 transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(true)}
              className="glass-button"
            >
              <AppIcon name="Hamburger" alt="Menu" className="h-10 w-10" />
            </Button>
            <h1 className="heading-md">{staticHeaderTitle}</h1>
            <div className="w-10" />
          </div>
        </div>

        <div className="flex-1">
          <KnowledgeMainView
            selectedSection={selectedSection}
            uploadedFiles={uploadedFiles}
            onFilesChange={handleFilesChange}
            socialLinks={socialLinks}
            onSocialLinksChange={handleSocialLinksChange}
            chatSettings={chatSettings}
            onChatSettingsChange={handleChatSettingsChange}
          />
        </div>
      </div>

      {sidebarOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <motion.div
            initial={{ x: -320 }}
            animate={{ x: 0 }}
            exit={{ x: -320 }}
            className="w-80 h-full bg-background/95 backdrop-blur-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <KnowledgeSidebar
              sections={knowledgeSections}
              selectedSection={selectedSection}
              onSelectSection={(sectionId) => {
                setSelectedSection(sectionId);
                setSidebarOpen(false);
              }}
              onClose={() => setSidebarOpen(false)}
            />
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}
