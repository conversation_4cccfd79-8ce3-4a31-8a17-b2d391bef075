
import { useState } from 'react';
import { motion } from 'framer-motion';
import { PageHero } from '@/components/PageHero';
import { SubscriptionPlans } from '@/components/subscription/SubscriptionPlans';
import { useToast } from '@/hooks/use-toast';

export default function Upgrade() {
  const [currentPlan, setCurrentPlan] = useState<string | null>('personal-brand-promoter'); // Mock current plan
  const { toast } = useToast();

  const handlePlanSelection = (planId: string, paymentMethod: string) => {
    // Mock subscription logic
    setCurrentPlan(planId);
    
    toast({
      title: "Subscription Updated!",
      description: `Successfully subscribed to ${planId.replace('-', ' ')} via ${paymentMethod}`,
    });
  };

  return (
    <div className="min-h-full flex flex-col">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="mobile-container max-w-7xl py-4 md:py-8"
      >
        <PageHero
          iconName="Crown"
          title="Upgrade Your Experience"
          description="Unlock the full potential of AI-powered conversations and avatar creation with our premium subscription plans"
        />

        <SubscriptionPlans
          currentPlan={currentPlan}
          onPlanSelect={handlePlanSelection}
        />

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <p className="body-sm text-muted-foreground">
            All plans include a 7-day free trial. Cancel anytime.
          </p>
        </motion.div>
      </motion.div>
    </div>
  );
}
