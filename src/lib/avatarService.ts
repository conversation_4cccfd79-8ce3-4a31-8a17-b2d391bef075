// Avatar Management Service
// Core functions for avatar CRUD operations and Ready Player Me integration

import { supabase } from '@/integrations/supabase/client';
import type { 
  AvatarData, 
  Room3D, 
  UserRoom, 
  AvatarAPIResponse,
  AvatarListResponse,
  RoomListResponse,
  AvatarError,
  RPMError,
  AvatarValidationResult
} from '@/types/avatar';

// Avatar CRUD Operations

export async function createAvatar(
  userId: string,
  avatarData: Partial<AvatarData>
): Promise<AvatarAPIResponse<AvatarData>> {
  try {
    // Validate required fields
    if (!avatarData.avatar_url) {
      throw new AvatarError('Avatar URL is required', 'MISSING_AVATAR_URL');
    }

    // Deactivate other avatars if this is being set as active
    if (avatarData.is_active) {
      await deactivateUserAvatars(userId);
    }

    const { data, error } = await supabase
      .from('avatars')
      .insert({
        user_id: userId,
        ...avatarData,
        is_active: avatarData.is_active ?? true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw new AvatarError('Failed to create avatar', 'CREATE_ERROR', { error });
    }

    return {
      success: true,
      data: data as AvatarData
    };
  } catch (error) {
    console.error('Error creating avatar:', error);
    return {
      success: false,
      error: {
        code: error instanceof AvatarError ? error.code : 'UNKNOWN_ERROR',
        message: error instanceof Error ? error.message : 'Failed to create avatar'
      }
    };
  }
}

export async function getUserAvatars(
  userId: string,
  options: { page?: number; limit?: number; activeOnly?: boolean } = {}
): Promise<AvatarListResponse> {
  try {
    const { page = 1, limit = 10, activeOnly = false } = options;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('avatars')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (activeOnly) {
      query = query.eq('is_active', true);
    }

    const { data, error, count } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      throw new AvatarError('Failed to fetch avatars', 'FETCH_ERROR', { error });
    }

    return {
      success: true,
      data: data as AvatarData[],
      pagination: {
        page,
        limit,
        total: count || 0,
        hasMore: (count || 0) > offset + limit
      }
    };
  } catch (error) {
    console.error('Error fetching user avatars:', error);
    return {
      success: false,
      error: {
        code: error instanceof AvatarError ? error.code : 'UNKNOWN_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch avatars'
      },
      data: [],
      pagination: { page: 1, limit: 10, total: 0, hasMore: false }
    };
  }
}

export async function updateAvatar(
  avatarId: string,
  updates: Partial<AvatarData>
): Promise<AvatarAPIResponse<AvatarData>> {
  try {
    // If setting as active, deactivate other avatars for this user
    if (updates.is_active) {
      const { data: avatar } = await supabase
        .from('avatars')
        .select('user_id')
        .eq('id', avatarId)
        .single();

      if (avatar) {
        await deactivateUserAvatars(avatar.user_id, avatarId);
      }
    }

    const { data, error } = await supabase
      .from('avatars')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', avatarId)
      .select()
      .single();

    if (error) {
      throw new AvatarError('Failed to update avatar', 'UPDATE_ERROR', { error });
    }

    return {
      success: true,
      data: data as AvatarData
    };
  } catch (error) {
    console.error('Error updating avatar:', error);
    return {
      success: false,
      error: {
        code: error instanceof AvatarError ? error.code : 'UNKNOWN_ERROR',
        message: error instanceof Error ? error.message : 'Failed to update avatar'
      }
    };
  }
}

export async function deleteAvatar(avatarId: string): Promise<AvatarAPIResponse<void>> {
  try {
    const { error } = await supabase
      .from('avatars')
      .delete()
      .eq('id', avatarId);

    if (error) {
      throw new AvatarError('Failed to delete avatar', 'DELETE_ERROR', { error });
    }

    return { success: true };
  } catch (error) {
    console.error('Error deleting avatar:', error);
    return {
      success: false,
      error: {
        code: error instanceof AvatarError ? error.code : 'UNKNOWN_ERROR',
        message: error instanceof Error ? error.message : 'Failed to delete avatar'
      }
    };
  }
}

export async function getActiveAvatar(userId: string): Promise<AvatarAPIResponse<AvatarData | null>> {
  try {
    const { data, error } = await supabase
      .from('avatars')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw new AvatarError('Failed to fetch active avatar', 'FETCH_ERROR', { error });
    }

    return {
      success: true,
      data: data ? (data as AvatarData) : null
    };
  } catch (error) {
    console.error('Error fetching active avatar:', error);
    return {
      success: false,
      error: {
        code: error instanceof AvatarError ? error.code : 'UNKNOWN_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch active avatar'
      }
    };
  }
}

// Room Management Functions

export async function getRooms(
  options: { 
    category?: string; 
    isPremium?: boolean; 
    page?: number; 
    limit?: number;
    search?: string;
  } = {}
): Promise<RoomListResponse> {
  try {
    const { category, isPremium, page = 1, limit = 12, search } = options;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('rooms')
      .select('*', { count: 'exact' })
      .eq('is_active', true)
      .order('popularity_score', { ascending: false });

    if (category) {
      query = query.eq('category', category);
    }

    if (isPremium !== undefined) {
      query = query.eq('is_premium', isPremium);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    const { data, error, count } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      throw new AvatarError('Failed to fetch rooms', 'FETCH_ERROR', { error });
    }

    return {
      success: true,
      data: data as Room3D[],
      pagination: {
        page,
        limit,
        total: count || 0,
        hasMore: (count || 0) > offset + limit
      }
    };
  } catch (error) {
    console.error('Error fetching rooms:', error);
    return {
      success: false,
      error: {
        code: error instanceof AvatarError ? error.code : 'UNKNOWN_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch rooms'
      },
      data: [],
      pagination: { page: 1, limit: 12, total: 0, hasMore: false }
    };
  }
}

export async function getUserRooms(userId: string): Promise<AvatarAPIResponse<UserRoom[]>> {
  try {
    const { data, error } = await supabase
      .from('user_rooms')
      .select(`
        *,
        rooms:room_id (
          id,
          name,
          glb_url,
          thumbnail_url,
          category,
          is_premium
        )
      `)
      .eq('user_id', userId)
      .order('is_default', { ascending: false })
      .order('last_used_at', { ascending: false });

    if (error) {
      throw new AvatarError('Failed to fetch user rooms', 'FETCH_ERROR', { error });
    }

    return {
      success: true,
      data: data as UserRoom[]
    };
  } catch (error) {
    console.error('Error fetching user rooms:', error);
    return {
      success: false,
      error: {
        code: error instanceof AvatarError ? error.code : 'UNKNOWN_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch user rooms'
      }
    };
  }
}

export async function setUserRoom(
  userId: string,
  roomId: string,
  isDefault = false
): Promise<AvatarAPIResponse<UserRoom>> {
  try {
    // If setting as default, remove default flag from other rooms
    if (isDefault) {
      await supabase
        .from('user_rooms')
        .update({ is_default: false })
        .eq('user_id', userId);
    }

    const { data, error } = await supabase
      .from('user_rooms')
      .upsert({
        user_id: userId,
        room_id: roomId,
        is_default: isDefault,
        last_used_at: new Date().toISOString(),
        usage_count: 1,
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: 1.0
      }, {
        onConflict: 'user_id,room_id'
      })
      .select()
      .single();

    if (error) {
      throw new AvatarError('Failed to set user room', 'UPDATE_ERROR', { error });
    }

    // Update room popularity
    await supabase.rpc('update_room_usage', {
      user_id_input: userId,
      room_id_input: roomId
    });

    return {
      success: true,
      data: data as UserRoom
    };
  } catch (error) {
    console.error('Error setting user room:', error);
    return {
      success: false,
      error: {
        code: error instanceof AvatarError ? error.code : 'UNKNOWN_ERROR',
        message: error instanceof Error ? error.message : 'Failed to set user room'
      }
    };
  }
}

// Ready Player Me Integration

export async function createRPMAvatar(
  userId: string,
  config: { bodyType?: string; gender?: string; template?: string } = {}
): Promise<AvatarAPIResponse<{ url: string; avatarId: string }>> {
  try {
    const rpmUrl = generateRPMURL(config);
    
    return {
      success: true,
      data: {
        url: rpmUrl,
        avatarId: '' // Will be populated when avatar is exported
      }
    };
  } catch (error) {
    console.error('Error creating RPM avatar:', error);
    return {
      success: false,
      error: {
        code: 'RPM_ERROR',
        message: error instanceof Error ? error.message : 'Failed to create RPM avatar'
      }
    };
  }
}

export function generateRPMURL(config: Record<string, unknown> = {}): string {
  const baseUrl = 'https://heey.readyplayer.me/avatar';
  const params = new URLSearchParams();
  
  // Default configuration
  params.set('frameApi', 'true');
  params.set('clearCache', 'true');
  params.set('bodyType', config.bodyType as string || 'halfbody');
  params.set('quickStart', 'true');
  
  if (config.gender) {
    params.set('gender', config.gender as string);
  }
  
  if (config.template) {
    params.set('template', config.template as string);
  }

  return `${baseUrl}?${params.toString()}`;
}

export async function processRPMExport(
  userId: string,
  avatarUrl: string,
  metadata: Record<string, unknown> = {}
): Promise<AvatarAPIResponse<AvatarData>> {
  try {
    // Validate RPM URL
    if (!avatarUrl.includes('readyplayer.me') && !avatarUrl.includes('.glb')) {
      throw new RPMError('Invalid Ready Player Me avatar URL');
    }

    // Extract avatar ID from URL
    const urlParts = avatarUrl.split('/');
    const rpmAvatarId = urlParts[urlParts.length - 1]?.replace('.glb', '');

    // Generate thumbnail URL
    const thumbnailUrl = avatarUrl.replace('.glb', '.png');

    // Create avatar record
    const avatarData: Partial<AvatarData> = {
      rpm_avatar_id: rpmAvatarId,
      avatar_url: avatarUrl,
      thumbnail_url: thumbnailUrl,
      name: metadata.name as string || 'My Avatar',
      metadata: {
        ...metadata,
        source: 'ready_player_me',
        imported_at: new Date().toISOString()
      },
      is_active: true
    };

    return await createAvatar(userId, avatarData);
  } catch (error) {
    console.error('Error processing RPM export:', error);
    return {
      success: false,
      error: {
        code: error instanceof RPMError ? error.code : 'PROCESSING_ERROR',
        message: error instanceof Error ? error.message : 'Failed to process RPM avatar'
      }
    };
  }
}

// Utility Functions

async function deactivateUserAvatars(userId: string, exceptAvatarId?: string): Promise<void> {
  let query = supabase
    .from('avatars')
    .update({ is_active: false })
    .eq('user_id', userId);

  if (exceptAvatarId) {
    query = query.neq('id', exceptAvatarId);
  }

  await query;
}

export function validateAvatarData(data: Partial<AvatarData>): AvatarValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!data.avatar_url) {
    errors.push('Avatar URL is required');
  }

  // URL validation
  if (data.avatar_url && !isValidAvatarURL(data.avatar_url)) {
    errors.push('Avatar URL must be a valid GLB file URL');
  }

  // Name validation
  if (data.name && data.name.length > 100) {
    errors.push('Avatar name must be less than 100 characters');
  }

  // Thumbnail validation
  if (data.thumbnail_url && !isValidImageURL(data.thumbnail_url)) {
    warnings.push('Thumbnail URL should be a valid image URL');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

function isValidAvatarURL(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.pathname.endsWith('.glb') || 
           url.includes('readyplayer.me') ||
           url.includes('models.readyplayer.me');
  } catch {
    return false;
  }
}

function isValidImageURL(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    const validExtensions = ['.png', '.jpg', '.jpeg', '.webp', '.gif'];
    return validExtensions.some(ext => parsedUrl.pathname.toLowerCase().endsWith(ext));
  } catch {
    return false;
  }
}

export async function getAvatarAnalytics(userId: string): Promise<AvatarAPIResponse<unknown>> {
  try {
    // This would integrate with analytics system
    // For now, return mock data structure
    const analytics = {
      total_interactions: 0,
      unique_visitors: 0,
      avg_session_duration: 0,
      popular_interactions: [],
      sentiment_scores: { positive: 0, neutral: 0, negative: 0 },
      conversation_topics: []
    };

    return {
      success: true,
      data: analytics
    };
  } catch (error) {
    console.error('Error fetching avatar analytics:', error);
    return {
      success: false,
      error: {
        code: 'ANALYTICS_ERROR',
        message: 'Failed to fetch avatar analytics'
      }
    };
  }
}