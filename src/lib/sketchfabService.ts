// Sketchfab Integration Service
// Handles Sketchfab API integration for 3D model loading and management

import type { Room3D, AvatarAPIResponse } from '@/types/avatar';

export interface SketchfabModel {
  uid: string;
  name: string;
  description: string;
  user: {
    username: string;
    displayName: string;
    profileUrl: string;
  };
  thumbnails: {
    images: Array<{
      url: string;
      width: number;
      height: number;
    }>;
  };
  archives: {
    gltf: {
      url: string;
      size: number;
    };
  };
  license: {
    label: string;
    requirements: string;
  };
  tags: Array<{
    name: string;
    slug: string;
  }>;
  categories: Array<{
    name: string;
    slug: string;
  }>;
  isDownloadable: boolean;
  viewCount: number;
  likeCount: number;
  publishedAt: string;
  updatedAt: string;
}

export interface SketchfabSearchOptions {
  query?: string;
  categories?: string[];
  tags?: string[];
  minFaceCount?: number;
  maxFaceCount?: number;
  minAnimationCount?: number;
  maxAnimationCount?: number;
  downloadable?: boolean;
  rigged?: boolean;
  animated?: boolean;
  pbr?: boolean;
  staffPicked?: boolean;
  sortBy?: 'relevance' | 'recent' | 'popular' | 'likes';
  count?: number;
  cursor?: string;
}

export interface SketchfabAPIResponse<T> {
  results: T[];
  next?: string;
  previous?: string;
  cursors?: {
    next?: string;
    previous?: string;
  };
}

export class SketchfabService {
  private apiToken: string;
  private baseUrl = 'https://api.sketchfab.com/v3';

  constructor(apiToken: string) {
    this.apiToken = apiToken;
  }

  /**
   * Search for 3D models on Sketchfab
   */
  async searchModels(options: SketchfabSearchOptions = {}): Promise<AvatarAPIResponse<SketchfabModel[]>> {
    try {
      const params = new URLSearchParams();
      
      // Basic search parameters
      if (options.query) {
        params.set('q', options.query);
      }
      
      if (options.downloadable) {
        params.set('downloadable', 'true');
      }
      
      if (options.rigged) {
        params.set('rigged', 'true');
      }
      
      if (options.animated) {
        params.set('animated', 'true');
      }
      
      if (options.pbr) {
        params.set('pbr', 'true');
      }
      
      if (options.staffPicked) {
        params.set('staffpicked', 'true');
      }
      
      // Categories filter
      if (options.categories && options.categories.length > 0) {
        params.set('categories', options.categories.join(','));
      }
      
      // Tags filter
      if (options.tags && options.tags.length > 0) {
        params.set('tags', options.tags.join(','));
      }
      
      // Face count filters
      if (options.minFaceCount) {
        params.set('min_face_count', options.minFaceCount.toString());
      }
      
      if (options.maxFaceCount) {
        params.set('max_face_count', options.maxFaceCount.toString());
      }
      
      // Animation count filters
      if (options.minAnimationCount) {
        params.set('min_animation_count', options.minAnimationCount.toString());
      }
      
      if (options.maxAnimationCount) {
        params.set('max_animation_count', options.maxAnimationCount.toString());
      }
      
      // Sorting and pagination
      params.set('sort_by', options.sortBy || 'relevance');
      params.set('count', (options.count || 24).toString());
      
      if (options.cursor) {
        params.set('cursor', options.cursor);
      }

      const response = await fetch(`${this.baseUrl}/search?type=models&${params.toString()}`, {
        headers: {
          'Authorization': `Token ${this.apiToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Sketchfab API error: ${response.status} ${response.statusText}`);
      }

      const data: SketchfabAPIResponse<SketchfabModel> = await response.json();

      return {
        success: true,
        data: data.results,
        pagination: {
          page: 1, // Sketchfab uses cursor-based pagination
          limit: options.count || 24,
          total: data.results.length,
          hasMore: !!data.cursors?.next
        }
      };
    } catch (error) {
      console.error('Error searching Sketchfab models:', error);
      return {
        success: false,
        error: {
          code: 'SKETCHFAB_SEARCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to search Sketchfab models'
        }
      };
    }
  }

  /**
   * Get detailed model information
   */
  async getModel(modelId: string): Promise<AvatarAPIResponse<SketchfabModel>> {
    try {
      const response = await fetch(`${this.baseUrl}/models/${modelId}`, {
        headers: {
          'Authorization': `Token ${this.apiToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Sketchfab API error: ${response.status} ${response.statusText}`);
      }

      const model: SketchfabModel = await response.json();

      return {
        success: true,
        data: model
      };
    } catch (error) {
      console.error('Error fetching Sketchfab model:', error);
      return {
        success: false,
        error: {
          code: 'SKETCHFAB_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch Sketchfab model'
        }
      };
    }
  }

  /**
   * Get model download URL (requires proper permissions)
   */
  async getModelDownloadUrl(modelId: string): Promise<AvatarAPIResponse<string>> {
    try {
      const response = await fetch(`${this.baseUrl}/models/${modelId}/download`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${this.apiToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Sketchfab download error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      return {
        success: true,
        data: data.gltf?.url || data.source?.url
      };
    } catch (error) {
      console.error('Error getting Sketchfab download URL:', error);
      return {
        success: false,
        error: {
          code: 'SKETCHFAB_DOWNLOAD_ERROR',
          message: error instanceof Error ? error.message : 'Failed to get download URL'
        }
      };
    }
  }

  /**
   * Convert Sketchfab model to Room3D format
   */
  convertToRoom(model: SketchfabModel, category = 'general'): Partial<Room3D> {
    const thumbnailUrl = model.thumbnails.images.find(img => img.width >= 512)?.url ||
                        model.thumbnails.images[0]?.url;

    return {
      name: model.name,
      description: model.description,
      glb_url: model.archives?.gltf?.url || '',
      thumbnail_url: thumbnailUrl,
      category,
      is_premium: !model.isDownloadable, // Non-downloadable models might be premium
      is_active: true,
      popularity_score: model.viewCount + model.likeCount,
      download_count: 0,
      rating: 0,
      tags: model.tags.map(tag => tag.name),
      metadata: {
        sketchfab_id: model.uid,
        author: model.user.displayName,
        license: model.license.label,
        face_count: 0, // Would need to be determined from model analysis
        published_at: model.publishedAt,
        source: 'sketchfab'
      }
    };
  }

  /**
   * Search for room-suitable models
   */
  async searchRoomModels(options: {
    category?: string;
    query?: string;
    limit?: number;
  } = {}): Promise<AvatarAPIResponse<Room3D[]>> {
    try {
      const searchOptions: SketchfabSearchOptions = {
        downloadable: true,
        maxFaceCount: 50000, // Reasonable limit for web use
        count: options.limit || 12,
        sortBy: 'popular'
      };

      // Map category to Sketchfab search terms
      if (options.category) {
        const categoryMappings: Record<string, string[]> = {
          'interior': ['interior', 'room', 'house', 'apartment'],
          'outdoor': ['outdoor', 'landscape', 'nature', 'environment'],
          'office': ['office', 'workplace', 'desk', 'meeting'],
          'futuristic': ['sci-fi', 'futuristic', 'cyberpunk', 'space'],
          'fantasy': ['fantasy', 'magical', 'medieval', 'mystical']
        };

        const searchTerms = categoryMappings[options.category];
        if (searchTerms) {
          searchOptions.query = searchTerms.join(' OR ');
        }
      }

      if (options.query) {
        searchOptions.query = options.query;
      }

      const result = await this.searchModels(searchOptions);

      if (!result.success) {
        return result as AvatarAPIResponse<Room3D[]>;
      }

      // Convert Sketchfab models to Room3D format
      const rooms = result.data?.map(model => 
        this.convertToRoom(model, options.category)
      ).filter(room => room.glb_url) as Room3D[];

      return {
        success: true,
        data: rooms || [],
        pagination: result.pagination
      };
    } catch (error) {
      console.error('Error searching room models:', error);
      return {
        success: false,
        error: {
          code: 'ROOM_SEARCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to search room models'
        }
      };
    }
  }

  /**
   * Get featured/curated models for rooms
   */
  async getFeaturedRoomModels(): Promise<AvatarAPIResponse<Room3D[]>> {
    try {
      const searchOptions: SketchfabSearchOptions = {
        staffPicked: true,
        downloadable: true,
        maxFaceCount: 30000,
        count: 6,
        sortBy: 'popular',
        categories: ['architecture', 'places-travel']
      };

      const result = await this.searchModels(searchOptions);

      if (!result.success) {
        return result as AvatarAPIResponse<Room3D[]>;
      }

      const rooms = result.data?.map(model => 
        this.convertToRoom(model, 'featured')
      ).filter(room => room.glb_url) as Room3D[];

      return {
        success: true,
        data: rooms || []
      };
    } catch (error) {
      console.error('Error fetching featured room models:', error);
      return {
        success: false,
        error: {
          code: 'FEATURED_ROOMS_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch featured rooms'
        }
      };
    }
  }

  /**
   * Validate model suitability for room use
   */
  validateRoomModel(model: SketchfabModel): {
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check if downloadable
    if (!model.isDownloadable) {
      issues.push('Model is not downloadable');
    }

    // Check if GLB format is available
    if (!model.archives?.gltf?.url) {
      issues.push('GLB format not available');
    }

    // Check file size (if available)
    if (model.archives?.gltf?.size && model.archives.gltf.size > 50 * 1024 * 1024) {
      recommendations.push('Model file size is large (>50MB). Consider optimization.');
    }

    // Check license compatibility
    if (model.license.requirements.includes('attribution')) {
      recommendations.push('Model requires attribution. Ensure proper crediting.');
    }

    // Check if suitable for environments
    const environmentKeywords = ['room', 'interior', 'environment', 'scene', 'space'];
    const hasEnvironmentKeywords = model.tags.some(tag => 
      environmentKeywords.some(keyword => tag.name.toLowerCase().includes(keyword))
    );

    if (!hasEnvironmentKeywords) {
      recommendations.push('Model may not be optimized for room environments.');
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations
    };
  }

  /**
   * Get model categories available on Sketchfab
   */
  async getCategories(): Promise<AvatarAPIResponse<Array<{ name: string; slug: string }>>> {
    try {
      const response = await fetch(`${this.baseUrl}/categories`, {
        headers: {
          'Authorization': `Token ${this.apiToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Sketchfab API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      return {
        success: true,
        data: data.results || []
      };
    } catch (error) {
      console.error('Error fetching Sketchfab categories:', error);
      return {
        success: false,
        error: {
          code: 'CATEGORIES_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch categories'
        }
      };
    }
  }
}

// Utility functions

export function validateSketchfabUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.hostname === 'sketchfab.com' || 
           parsedUrl.hostname === 'www.sketchfab.com';
  } catch {
    return false;
  }
}

export function extractSketchfabModelId(url: string): string | null {
  try {
    const parsedUrl = new URL(url);
    const pathParts = parsedUrl.pathname.split('/');
    
    // Sketchfab URLs typically follow /3d-models/model-name-{MODEL_ID}
    const modelPart = pathParts.find(part => part.length === 32 && /^[a-f0-9]+$/.test(part));
    
    if (modelPart) {
      return modelPart;
    }
    
    // Alternative format: /models/{MODEL_ID}
    const modelsIndex = pathParts.indexOf('models');
    if (modelsIndex !== -1 && pathParts[modelsIndex + 1]) {
      return pathParts[modelsIndex + 1];
    }
    
    return null;
  } catch {
    return null;
  }
}

export function optimizeModelForWeb(model: SketchfabModel): {
  optimized: boolean;
  suggestions: string[];
} {
  const suggestions: string[] = [];
  let optimized = true;

  // Check file size
  if (model.archives?.gltf?.size && model.archives.gltf.size > 20 * 1024 * 1024) {
    optimized = false;
    suggestions.push('Reduce model file size to under 20MB for better web performance');
  }

  // Check if model has too many details for web use
  suggestions.push('Consider using texture compression for faster loading');
  suggestions.push('Optimize geometry for web rendering');
  suggestions.push('Use LOD (Level of Detail) models for better performance');

  return {
    optimized,
    suggestions
  };
}

// Export default instance (will be initialized with API token from environment)
export const sketchfabService = new SketchfabService(
  process.env.VITE_SKETCHFAB_API_TOKEN || ''
);