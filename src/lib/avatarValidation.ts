// Avatar Validation Utilities
// Validation functions for avatar data and configurations

import type { 
  AvatarData, 
  Room3D, 
  AvatarConfig, 
  AvatarValidationResult,
  ReadyPlayerMeConfig 
} from '@/types/avatar';

export function validateAvatarData(data: Partial<AvatarData>): AvatarValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const recommendations: string[] = [];

  // Required fields validation
  if (!data.avatar_url) {
    errors.push('Avatar URL is required');
  } else {
    // URL format validation
    if (!isValidAvatarURL(data.avatar_url)) {
      errors.push('Avatar URL must be a valid GLB file URL or Ready Player Me URL');
    }

    // File size check (if possible to determine)
    if (data.avatar_url.includes('filesize') || data.avatar_url.includes('size=')) {
      const sizeMatch = data.avatar_url.match(/size=(\d+)/);
      if (sizeMatch && parseInt(sizeMatch[1]) > 50 * 1024 * 1024) { // 50MB
        warnings.push('Avatar file size is large (>50MB). Consider optimizing for better performance.');
      }
    }
  }

  // Name validation
  if (data.name) {
    if (data.name.length < 1) {
      errors.push('Avatar name cannot be empty');
    } else if (data.name.length > 100) {
      errors.push('Avatar name must be less than 100 characters');
    } else if (data.name.length < 3) {
      warnings.push('Avatar name is very short. Consider using a more descriptive name.');
    }

    // Check for inappropriate content (basic)
    if (containsProfanity(data.name)) {
      errors.push('Avatar name contains inappropriate content');
    }
  }

  // Thumbnail validation
  if (data.thumbnail_url) {
    if (!isValidImageURL(data.thumbnail_url)) {
      warnings.push('Thumbnail URL should be a valid image URL (PNG, JPG, WEBP)');
    }
  } else if (data.avatar_url) {
    recommendations.push('Consider adding a thumbnail image for better user experience');
  }

  // Metadata validation
  if (data.metadata) {
    try {
      if (typeof data.metadata !== 'object') {
        errors.push('Metadata must be a valid object');
      } else {
        // Check metadata size
        const metadataString = JSON.stringify(data.metadata);
        if (metadataString.length > 10000) { // 10KB limit
          warnings.push('Metadata is quite large. Consider reducing to improve performance.');
        }
      }
    } catch {
      errors.push('Metadata contains invalid JSON data');
    }
  }

  // Ready Player Me specific validation
  if (data.rpm_avatar_id) {
    if (!isValidRPMId(data.rpm_avatar_id)) {
      errors.push('Invalid Ready Player Me avatar ID format');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    recommendations
  };
}

export function validateRoomData(data: Partial<Room3D>): AvatarValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const recommendations: string[] = [];

  // Required fields
  if (!data.name) {
    errors.push('Room name is required');
  } else {
    if (data.name.length < 3) {
      warnings.push('Room name should be at least 3 characters long');
    }
    if (data.name.length > 100) {
      errors.push('Room name must be less than 100 characters');
    }
  }

  if (!data.glb_url) {
    errors.push('Room GLB URL is required');
  } else {
    if (!isValidGLBURL(data.glb_url)) {
      errors.push('Room GLB URL must be a valid GLB file URL');
    }
  }

  // Category validation
  const validCategories = ['interior', 'outdoor', 'office', 'futuristic', 'fantasy', 'abstract'];
  if (data.category && !validCategories.includes(data.category)) {
    warnings.push(`Room category should be one of: ${validCategories.join(', ')}`);
  }

  // Description validation
  if (data.description) {
    if (data.description.length > 500) {
      warnings.push('Room description should be less than 500 characters for better readability');
    }
    if (containsProfanity(data.description)) {
      errors.push('Room description contains inappropriate content');
    }
  }

  // Tags validation
  if (data.tags) {
    if (data.tags.length > 10) {
      warnings.push('Consider using fewer than 10 tags for better organization');
    }
    
    const invalidTags = data.tags.filter(tag => tag.length > 20);
    if (invalidTags.length > 0) {
      warnings.push('Some tags are too long (>20 characters). Consider shortening them.');
    }
  }

  // Rating validation
  if (data.rating !== undefined) {
    if (data.rating < 0 || data.rating > 5) {
      errors.push('Room rating must be between 0 and 5');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    recommendations
  };
}

export function validateAvatarConfig(config: Partial<AvatarConfig>): AvatarValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const recommendations: string[] = [];

  // Position validation
  if (config.position) {
    const { x, y, z } = config.position;
    if (typeof x !== 'number' || typeof y !== 'number' || typeof z !== 'number') {
      errors.push('Position coordinates must be numbers');
    } else {
      if (Math.abs(x) > 100 || Math.abs(y) > 100 || Math.abs(z) > 100) {
        warnings.push('Position coordinates are quite large. Avatar might appear outside typical bounds.');
      }
    }
  }

  // Rotation validation
  if (config.rotation) {
    const { x, y, z } = config.rotation;
    if (typeof x !== 'number' || typeof y !== 'number' || typeof z !== 'number') {
      errors.push('Rotation values must be numbers');
    } else {
      // Convert to degrees for validation
      const toDegrees = (rad: number) => rad * (180 / Math.PI);
      if (Math.abs(toDegrees(x)) > 360 || Math.abs(toDegrees(y)) > 360 || Math.abs(toDegrees(z)) > 360) {
        warnings.push('Rotation values seem large. Consider normalizing to 0-360 degrees.');
      }
    }
  }

  // Scale validation
  if (config.scale !== undefined) {
    if (typeof config.scale !== 'number') {
      errors.push('Scale must be a number');
    } else {
      if (config.scale <= 0) {
        errors.push('Scale must be greater than 0');
      } else if (config.scale < 0.1) {
        warnings.push('Scale is very small. Avatar might be barely visible.');
      } else if (config.scale > 10) {
        warnings.push('Scale is very large. Avatar might appear oversized.');
      }
    }
  }

  // Lighting validation
  if (config.lighting) {
    const { ambient, directional } = config.lighting;
    if (typeof ambient !== 'number' || typeof directional !== 'number') {
      errors.push('Lighting values must be numbers');
    } else {
      if (ambient < 0 || ambient > 1 || directional < 0 || directional > 1) {
        warnings.push('Lighting values should typically be between 0 and 1');
      }
    }
  }

  // Animations validation
  if (config.animations) {
    const validAnimationTypes = ['idle', 'talking', 'greeting', 'wave', 'dance'];
    Object.entries(config.animations).forEach(([type, animation]) => {
      if (!validAnimationTypes.includes(type)) {
        warnings.push(`Animation type '${type}' is not commonly supported`);
      }
      if (typeof animation !== 'string' || animation.length === 0) {
        errors.push(`Animation '${type}' must be a non-empty string`);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    recommendations
  };
}

export function validateRPMConfig(config: Partial<ReadyPlayerMeConfig>): AvatarValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const recommendations: string[] = [];

  // Subdomain validation
  if (!config.subdomain) {
    errors.push('Ready Player Me subdomain is required');
  } else {
    if (!/^[a-z0-9-]+$/.test(config.subdomain)) {
      errors.push('Subdomain must contain only lowercase letters, numbers, and hyphens');
    }
  }

  // Body type validation
  if (config.bodyType && !['halfbody', 'fullbody'].includes(config.bodyType)) {
    errors.push('Body type must be either "halfbody" or "fullbody"');
  }

  // Language validation
  if (config.language) {
    const supportedLanguages = ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'];
    if (!supportedLanguages.includes(config.language)) {
      warnings.push(`Language '${config.language}' might not be supported. Supported languages: ${supportedLanguages.join(', ')}`);
    }
  }

  // Customizations validation
  if (config.customizations) {
    const validCustomizations = ['hair', 'beard', 'eyes', 'eyebrows', 'facewear', 'headwear', 'clothing'];
    Object.keys(config.customizations).forEach(key => {
      if (!validCustomizations.includes(key)) {
        warnings.push(`Customization option '${key}' might not be supported`);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    recommendations
  };
}

// Helper validation functions

function isValidAvatarURL(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    
    // Check for GLB file extension
    if (parsedUrl.pathname.toLowerCase().endsWith('.glb')) {
      return true;
    }
    
    // Check for Ready Player Me URLs
    if (url.includes('readyplayer.me') || url.includes('models.readyplayer.me')) {
      return true;
    }
    
    // Check for common 3D model hosting services
    const validHosts = ['amazonaws.com', 'googleapis.com', 'cloudfront.net', 'cdn.'];
    if (validHosts.some(host => parsedUrl.hostname.includes(host)) && parsedUrl.pathname.endsWith('.glb')) {
      return true;
    }
    
    return false;
  } catch {
    return false;
  }
}

function isValidImageURL(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    const validExtensions = ['.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg'];
    return validExtensions.some(ext => parsedUrl.pathname.toLowerCase().endsWith(ext));
  } catch {
    return false;
  }
}

function isValidGLBURL(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.pathname.toLowerCase().endsWith('.glb');
  } catch {
    return false;
  }
}

function isValidRPMId(id: string): boolean {
  // Ready Player Me avatar IDs are typically UUIDs or similar format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  const rpmIdRegex = /^[a-zA-Z0-9-_]{20,}$/; // RPM IDs are at least 20 characters
  
  return uuidRegex.test(id) || rpmIdRegex.test(id);
}

function containsProfanity(text: string): boolean {
  // Basic profanity filter - in production, use a comprehensive service
  const profanityList = [
    // Add appropriate words for content filtering
    'spam', 'fake', 'scam' // Example non-profane but problematic words
  ];
  
  const lowerText = text.toLowerCase();
  return profanityList.some(word => lowerText.includes(word));
}

// Validation presets for common scenarios

export const VALIDATION_PRESETS = {
  STRICT: {
    requireThumbnail: true,
    maxNameLength: 50,
    maxDescriptionLength: 200,
    allowedDomains: ['readyplayer.me', 'models.readyplayer.me'],
    requireMetadata: true
  },
  
  RELAXED: {
    requireThumbnail: false,
    maxNameLength: 100,
    maxDescriptionLength: 500,
    allowedDomains: [], // Allow all domains
    requireMetadata: false
  },
  
  PRODUCTION: {
    requireThumbnail: true,
    maxNameLength: 80,
    maxDescriptionLength: 300,
    allowedDomains: ['readyplayer.me', 'amazonaws.com', 'cloudfront.net'],
    requireMetadata: false
  }
};

export function validateWithPreset(
  data: Partial<AvatarData>, 
  preset: keyof typeof VALIDATION_PRESETS
): AvatarValidationResult {
  const config = VALIDATION_PRESETS[preset];
  const baseValidation = validateAvatarData(data);
  
  // Apply preset-specific rules
  if (config.requireThumbnail && !data.thumbnail_url) {
    baseValidation.errors.push('Thumbnail is required for this validation preset');
  }
  
  if (data.name && data.name.length > config.maxNameLength) {
    baseValidation.errors.push(`Name must be less than ${config.maxNameLength} characters`);
  }
  
  if (config.allowedDomains.length > 0 && data.avatar_url) {
    try {
      const url = new URL(data.avatar_url);
      const isAllowedDomain = config.allowedDomains.some(domain => 
        url.hostname.includes(domain)
      );
      if (!isAllowedDomain) {
        baseValidation.errors.push(`Avatar URL must be from allowed domains: ${config.allowedDomains.join(', ')}`);
      }
    } catch {
      // URL validation will be caught by base validation
    }
  }
  
  return {
    isValid: baseValidation.errors.length === 0,
    errors: baseValidation.errors,
    warnings: baseValidation.warnings,
    recommendations: baseValidation.recommendations
  };
}