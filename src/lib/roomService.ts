// Room Management Service
// Handles 3D room data operations and user room configurations

import { supabase } from '@/integrations/supabase/client';
import type { Room3D, UserRoom, AvatarAPIResponse } from '@/types/avatar';

// Room CRUD Operations

export async function getAllRooms(options: {
  category?: string;
  isPremium?: boolean;
  isActive?: boolean;
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: 'popularity' | 'newest' | 'rating' | 'name';
} = {}): Promise<AvatarAPIResponse<Room3D[]>> {
  try {
    const {
      category,
      isPremium,
      isActive = true,
      page = 1,
      limit = 12,
      search,
      sortBy = 'popularity'
    } = options;

    const offset = (page - 1) * limit;

    let query = supabase
      .from('rooms')
      .select('*', { count: 'exact' })
      .eq('is_active', isActive);

    // Apply filters
    if (category) {
      query = query.eq('category', category);
    }

    if (isPremium !== undefined) {
      query = query.eq('is_premium', isPremium);
    }

    if (search) {
      query = query.or(
        `name.ilike.%${search}%,description.ilike.%${search}%,tags.cs.{${search}}`
      );
    }

    // Apply sorting
    switch (sortBy) {
      case 'popularity':
        query = query.order('popularity_score', { ascending: false });
        break;
      case 'newest':
        query = query.order('created_at', { ascending: false });
        break;
      case 'rating':
        query = query.order('rating', { ascending: false });
        break;
      case 'name':
        query = query.order('name', { ascending: true });
        break;
    }

    const { data, error, count } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(`Failed to fetch rooms: ${error.message}`);
    }

    return {
      success: true,
      data: data as Room3D[],
      pagination: {
        page,
        limit,
        total: count || 0,
        hasMore: (count || 0) > offset + limit
      }
    };
  } catch (error) {
    console.error('Error fetching rooms:', error);
    return {
      success: false,
      error: {
        code: 'FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch rooms'
      }
    };
  }
}

export async function getRoomById(roomId: string): Promise<AvatarAPIResponse<Room3D | null>> {
  try {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('id', roomId)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') { // No rows returned
      throw new Error(`Failed to fetch room: ${error.message}`);
    }

    return {
      success: true,
      data: data ? (data as Room3D) : null
    };
  } catch (error) {
    console.error('Error fetching room:', error);
    return {
      success: false,
      error: {
        code: 'FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch room'
      }
    };
  }
}

export async function getRoomCategories(): Promise<AvatarAPIResponse<string[]>> {
  try {
    const { data, error } = await supabase
      .from('rooms')
      .select('category')
      .eq('is_active', true)
      .not('category', 'is', null);

    if (error) {
      throw new Error(`Failed to fetch categories: ${error.message}`);
    }

    // Get unique categories
    const categories = [...new Set(data.map(item => item.category))].filter(Boolean);

    return {
      success: true,
      data: categories as string[]
    };
  } catch (error) {
    console.error('Error fetching room categories:', error);
    return {
      success: false,
      error: {
        code: 'FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch categories'
      }
    };
  }
}

export async function getPopularRooms(limit = 6): Promise<AvatarAPIResponse<Room3D[]>> {
  try {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('is_active', true)
      .order('popularity_score', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to fetch popular rooms: ${error.message}`);
    }

    return {
      success: true,
      data: data as Room3D[]
    };
  } catch (error) {
    console.error('Error fetching popular rooms:', error);
    return {
      success: false,
      error: {
        code: 'FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch popular rooms'
      }
    };
  }
}

// User Room Configuration

export async function getUserRoomConfig(userId: string): Promise<AvatarAPIResponse<UserRoom[]>> {
  try {
    const { data, error } = await supabase
      .rpc('get_user_room_config', { user_id_input: userId });

    if (error) {
      throw new Error(`Failed to fetch user room config: ${error.message}`);
    }

    return {
      success: true,
      data: data as UserRoom[]
    };
  } catch (error) {
    console.error('Error fetching user room config:', error);
    return {
      success: false,
      error: {
        code: 'FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch user room config'
      }
    };
  }
}

export async function setUserDefaultRoom(
  userId: string,
  roomId: string,
  config: {
    position?: { x: number; y: number; z: number };
    rotation?: { x: number; y: number; z: number };
    scale?: number;
  } = {}
): Promise<AvatarAPIResponse<UserRoom>> {
  try {
    // Remove default flag from other rooms
    await supabase
      .from('user_rooms')
      .update({ is_default: false })
      .eq('user_id', userId);

    // Set new default room
    const { data, error } = await supabase
      .from('user_rooms')
      .upsert({
        user_id: userId,
        room_id: roomId,
        is_default: true,
        position: config.position || { x: 0, y: 0, z: 0 },
        rotation: config.rotation || { x: 0, y: 0, z: 0 },
        scale: config.scale || 1.0,
        last_used_at: new Date().toISOString(),
        usage_count: 1
      }, {
        onConflict: 'user_id,room_id'
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to set default room: ${error.message}`);
    }

    // Update room usage statistics
    await updateRoomUsage(userId, roomId);

    return {
      success: true,
      data: data as UserRoom
    };
  } catch (error) {
    console.error('Error setting default room:', error);
    return {
      success: false,
      error: {
        code: 'UPDATE_ERROR',
        message: error instanceof Error ? error.message : 'Failed to set default room'
      }
    };
  }
}

export async function addUserRoom(
  userId: string,
  roomId: string,
  config: {
    position?: { x: number; y: number; z: number };
    rotation?: { x: number; y: number; z: number };
    scale?: number;
    isDefault?: boolean;
  } = {}
): Promise<AvatarAPIResponse<UserRoom>> {
  try {
    // If setting as default, remove default flag from other rooms
    if (config.isDefault) {
      await supabase
        .from('user_rooms')
        .update({ is_default: false })
        .eq('user_id', userId);
    }

    const { data, error } = await supabase
      .from('user_rooms')
      .upsert({
        user_id: userId,
        room_id: roomId,
        is_default: config.isDefault || false,
        position: config.position || { x: 0, y: 0, z: 0 },
        rotation: config.rotation || { x: 0, y: 0, z: 0 },
        scale: config.scale || 1.0,
        last_used_at: new Date().toISOString(),
        usage_count: 1
      }, {
        onConflict: 'user_id,room_id'
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to add user room: ${error.message}`);
    }

    // Update room statistics
    await updateRoomUsage(userId, roomId);

    return {
      success: true,
      data: data as UserRoom
    };
  } catch (error) {
    console.error('Error adding user room:', error);
    return {
      success: false,
      error: {
        code: 'CREATE_ERROR',
        message: error instanceof Error ? error.message : 'Failed to add user room'
      }
    };
  }
}

export async function updateUserRoomConfig(
  userId: string,
  roomId: string,
  config: {
    position?: { x: number; y: number; z: number };
    rotation?: { x: number; y: number; z: number };
    scale?: number;
    isDefault?: boolean;
  }
): Promise<AvatarAPIResponse<UserRoom>> {
  try {
    // If setting as default, remove default flag from other rooms
    if (config.isDefault) {
      await supabase
        .from('user_rooms')
        .update({ is_default: false })
        .eq('user_id', userId)
        .neq('room_id', roomId);
    }

    const updateData: Record<string, unknown> = {
      last_used_at: new Date().toISOString()
    };

    if (config.position) updateData.position = config.position;
    if (config.rotation) updateData.rotation = config.rotation;
    if (config.scale !== undefined) updateData.scale = config.scale;
    if (config.isDefault !== undefined) updateData.is_default = config.isDefault;

    const { data, error } = await supabase
      .from('user_rooms')
      .update(updateData)
      .eq('user_id', userId)
      .eq('room_id', roomId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update room config: ${error.message}`);
    }

    return {
      success: true,
      data: data as UserRoom
    };
  } catch (error) {
    console.error('Error updating room config:', error);
    return {
      success: false,
      error: {
        code: 'UPDATE_ERROR',
        message: error instanceof Error ? error.message : 'Failed to update room config'
      }
    };
  }
}

export async function removeUserRoom(
  userId: string,
  roomId: string
): Promise<AvatarAPIResponse<void>> {
  try {
    const { error } = await supabase
      .from('user_rooms')
      .delete()
      .eq('user_id', userId)
      .eq('room_id', roomId);

    if (error) {
      throw new Error(`Failed to remove user room: ${error.message}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error removing user room:', error);
    return {
      success: false,
      error: {
        code: 'DELETE_ERROR',
        message: error instanceof Error ? error.message : 'Failed to remove user room'
      }
    };
  }
}

// Room Statistics and Analytics

export async function updateRoomUsage(
  userId: string,
  roomId: string
): Promise<AvatarAPIResponse<void>> {
  try {
    const { error } = await supabase
      .rpc('update_room_usage', {
        user_id_input: userId,
        room_id_input: roomId
      });

    if (error) {
      throw new Error(`Failed to update room usage: ${error.message}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating room usage:', error);
    return {
      success: false,
      error: {
        code: 'UPDATE_ERROR',
        message: error instanceof Error ? error.message : 'Failed to update room usage'
      }
    };
  }
}

export async function getRoomStats(roomId: string): Promise<AvatarAPIResponse<{
  totalUsers: number;
  averageUsage: number;
  popularityScore: number;
  rating: number;
  downloadCount: number;
}>> {
  try {
    const { data, error } = await supabase
      .from('rooms')
      .select(`
        popularity_score,
        rating,
        download_count,
        user_rooms(count)
      `)
      .eq('id', roomId)
      .single();

    if (error) {
      throw new Error(`Failed to fetch room stats: ${error.message}`);
    }

    // Calculate average usage
    const { data: usageData, error: usageError } = await supabase
      .from('user_rooms')
      .select('usage_count')
      .eq('room_id', roomId);

    if (usageError) {
      throw new Error(`Failed to fetch usage data: ${usageError.message}`);
    }

    const averageUsage = usageData.length > 0
      ? usageData.reduce((sum, item) => sum + (item.usage_count || 0), 0) / usageData.length
      : 0;

    return {
      success: true,
      data: {
        totalUsers: data.user_rooms?.length || 0,
        averageUsage,
        popularityScore: data.popularity_score || 0,
        rating: data.rating || 0,
        downloadCount: data.download_count || 0
      }
    };
  } catch (error) {
    console.error('Error fetching room stats:', error);
    return {
      success: false,
      error: {
        code: 'FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch room stats'
      }
    };
  }
}

// Room Search and Filtering

export async function searchRooms(
  query: string,
  filters: {
    category?: string;
    isPremium?: boolean;
    minRating?: number;
    tags?: string[];
  } = {}
): Promise<AvatarAPIResponse<Room3D[]>> {
  try {
    let dbQuery = supabase
      .from('rooms')
      .select('*')
      .eq('is_active', true);

    // Text search
    if (query) {
      dbQuery = dbQuery.or(
        `name.ilike.%${query}%,description.ilike.%${query}%`
      );
    }

    // Apply filters
    if (filters.category) {
      dbQuery = dbQuery.eq('category', filters.category);
    }

    if (filters.isPremium !== undefined) {
      dbQuery = dbQuery.eq('is_premium', filters.isPremium);
    }

    if (filters.minRating) {
      dbQuery = dbQuery.gte('rating', filters.minRating);
    }

    if (filters.tags && filters.tags.length > 0) {
      dbQuery = dbQuery.overlaps('tags', filters.tags);
    }

    const { data, error } = await dbQuery
      .order('popularity_score', { ascending: false })
      .limit(20);

    if (error) {
      throw new Error(`Failed to search rooms: ${error.message}`);
    }

    return {
      success: true,
      data: data as Room3D[]
    };
  } catch (error) {
    console.error('Error searching rooms:', error);
    return {
      success: false,
      error: {
        code: 'SEARCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to search rooms'
      }
    };
  }
}

// Room Validation

export function validateRoomConfig(config: {
  position?: { x: number; y: number; z: number };
  rotation?: { x: number; y: number; z: number };
  scale?: number;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (config.position) {
    const { x, y, z } = config.position;
    if (!isFinite(x) || !isFinite(y) || !isFinite(z)) {
      errors.push('Position coordinates must be valid numbers');
    }
    if (Math.abs(x) > 1000 || Math.abs(y) > 1000 || Math.abs(z) > 1000) {
      errors.push('Position coordinates are too large');
    }
  }

  if (config.rotation) {
    const { x, y, z } = config.rotation;
    if (!isFinite(x) || !isFinite(y) || !isFinite(z)) {
      errors.push('Rotation values must be valid numbers');
    }
  }

  if (config.scale !== undefined) {
    if (!isFinite(config.scale) || config.scale <= 0) {
      errors.push('Scale must be a positive number');
    }
    if (config.scale > 100) {
      errors.push('Scale value is too large');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}