// Chat Service
// Manages chat sessions, messages, and real-time communication

import { supabase } from '@/integrations/supabase/client';
import type { 
  ChatSession, 
  ChatMessage, 
  Visitor, 
  ChatAPIResponse,
  ChatListResponse,
  MessageListResponse,
  ChatContext,
  ChatAnalytics,
  ChatTopic,
  ChatEvent,
  ChatEventType
} from '@/types/chat';
import { ChatError, AIServiceError } from '@/types/chat';

export class ChatService {
  private eventListeners: Map<string, Set<(event: ChatEvent) => void>> = new Map();

  // Session Management
  async createChatSession(profileOwnerId: string, visitorId: string): Promise<ChatAPIResponse<ChatSession>> {
    try {
      const sessionId = this.generateSessionId();
      
      const sessionData = {
        user_id: profileOwnerId,
        visitor_id: visitorId,
        session_id: sessionId,
        status: 'active' as const,
        started_at: new Date().toISOString(),
        total_messages: 0
      };

      const { data, error } = await supabase
        .from('chats')
        .insert(sessionData)
        .select()
        .single();

      if (error) {
        throw new ChatError('Failed to create chat session', 'SESSION_CREATE_ERROR', undefined, { error });
      }

      // Emit session started event
      this.emitEvent({
        type: 'chat_started',
        chat_id: data.id,
        user_id: profileOwnerId,
        visitor_id: visitorId,
        timestamp: new Date().toISOString()
      });

      return { success: true, data };
    } catch (error) {
      console.error('Chat session creation error:', error);
      return {
        success: false,
        error: {
          code: 'SESSION_CREATE_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create chat session'
        }
      };
    }
  }

  async getChatSession(sessionId: string): Promise<ChatAPIResponse<ChatSession>> {
    try {
      const { data, error } = await supabase
        .from('chats')
        .select('*')
        .eq('session_id', sessionId)
        .single();

      if (error) {
        throw new ChatError('Chat session not found', 'SESSION_NOT_FOUND', undefined, { error });
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'SESSION_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch chat session'
        }
      };
    }
  }

  async endChatSession(sessionId: string): Promise<ChatAPIResponse<ChatSession>> {
    try {
      const { data, error } = await supabase
        .from('chats')
        .update({
          status: 'ended',
          ended_at: new Date().toISOString()
        })
        .eq('session_id', sessionId)
        .select()
        .single();

      if (error) {
        throw new ChatError('Failed to end chat session', 'SESSION_END_ERROR', undefined, { error });
      }

      // Emit session ended event
      this.emitEvent({
        type: 'chat_ended',
        chat_id: data.id,
        timestamp: new Date().toISOString()
      });

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'SESSION_END_ERROR',
          message: error instanceof Error ? error.message : 'Failed to end chat session'
        }
      };
    }
  }

  async getUserChatSessions(userId: string, page = 1, limit = 20): Promise<ChatListResponse> {
    try {
      const offset = (page - 1) * limit;

      const { data, error, count } = await supabase
        .from('chats')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new ChatError('Failed to fetch chat sessions', 'SESSIONS_FETCH_ERROR', undefined, { error });
      }

      return {
        success: true,
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          hasMore: (count || 0) > offset + limit
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'SESSIONS_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch chat sessions'
        }
      };
    }
  }

  // Message Management
  async sendMessage(
    chatId: string,
    senderId: string,
    senderType: 'user' | 'visitor' | 'ai',
    content: string,
    messageType: 'text' | 'image' | 'audio' | 'video' | 'file' = 'text'
  ): Promise<ChatAPIResponse<ChatMessage>> {
    try {
      const messageData = {
        chat_id: chatId,
        sender_id: senderId,
        sender_type: senderType,
        content,
        message_type: messageType,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('chat_messages')
        .insert(messageData)
        .select()
        .single();

      if (error) {
        throw new ChatError('Failed to send message', 'MESSAGE_SEND_ERROR', chatId, { error });
      }

      // Update session message count
      await this.updateSessionMessageCount(chatId);

      // Emit message sent event
      this.emitEvent({
        type: 'message_sent',
        chat_id: chatId,
        user_id: senderType === 'user' ? senderId : undefined,
        visitor_id: senderType === 'visitor' ? senderId : undefined,
        data: { message: data },
        timestamp: new Date().toISOString()
      });

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'MESSAGE_SEND_ERROR',
          message: error instanceof Error ? error.message : 'Failed to send message'
        }
      };
    }
  }

  async getChatMessages(chatId: string, page = 1, limit = 50): Promise<MessageListResponse> {
    try {
      const offset = (page - 1) * limit;

      const { data, error, count } = await supabase
        .from('chat_messages')
        .select('*', { count: 'exact' })
        .eq('chat_id', chatId)
        .order('created_at', { ascending: true })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new ChatError('Failed to fetch messages', 'MESSAGES_FETCH_ERROR', chatId, { error });
      }

      // Get chat info
      const { data: chatData } = await supabase
        .from('chats')
        .select('id, status')
        .eq('id', chatId)
        .single();

      return {
        success: true,
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          hasMore: (count || 0) > offset + limit
        },
        chat_info: chatData ? {
          id: chatData.id,
          status: chatData.status,
          participant_count: 2 // Visitor + Profile owner
        } : undefined
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'MESSAGES_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch messages'
        }
      };
    }
  }

  // Visitor Management
  async createOrUpdateVisitor(
    profileOwnerId: string,
    visitorData: Partial<Visitor>
  ): Promise<ChatAPIResponse<Visitor>> {
    try {
      // Check if visitor already exists
      let existingVisitor = null;
      if (visitorData.email) {
        const { data } = await supabase
          .from('visitors')
          .select('*')
          .eq('profile_owner_id', profileOwnerId)
          .eq('email', visitorData.email)
          .single();
        
        existingVisitor = data;
      }

      if (existingVisitor) {
        // Update existing visitor
        const { data, error } = await supabase
          .from('visitors')
          .update({
            last_visit_at: new Date().toISOString(),
            total_visits: existingVisitor.total_visits + 1,
            ...visitorData
          })
          .eq('id', existingVisitor.id)
          .select()
          .single();

        if (error) {
          throw new ChatError('Failed to update visitor', 'VISITOR_UPDATE_ERROR', undefined, { error });
        }

        return { success: true, data };
      } else {
        // Create new visitor
        const newVisitorData = {
          profile_owner_id: profileOwnerId,
          first_visit_at: new Date().toISOString(),
          last_visit_at: new Date().toISOString(),
          total_visits: 1,
          ...visitorData
        };

        const { data, error } = await supabase
          .from('visitors')
          .insert(newVisitorData)
          .select()
          .single();

        if (error) {
          throw new ChatError('Failed to create visitor', 'VISITOR_CREATE_ERROR', undefined, { error });
        }

        return { success: true, data };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'VISITOR_ERROR',
          message: error instanceof Error ? error.message : 'Failed to handle visitor'
        }
      };
    }
  }

  async getVisitor(visitorId: string): Promise<ChatAPIResponse<Visitor>> {
    try {
      const { data, error } = await supabase
        .from('visitors')
        .select('*')
        .eq('id', visitorId)
        .single();

      if (error) {
        throw new ChatError('Visitor not found', 'VISITOR_NOT_FOUND', undefined, { error });
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'VISITOR_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch visitor'
        }
      };
    }
  }

  // Analytics and Context
  async buildChatContext(sessionId: string): Promise<ChatAPIResponse<ChatContext>> {
    try {
      const sessionResult = await this.getChatSession(sessionId);
      if (!sessionResult.success || !sessionResult.data) {
        throw new ChatError('Session not found for context building', 'CONTEXT_SESSION_ERROR');
      }

      const session = sessionResult.data;

      // Get recent messages
      const messagesResult = await this.getChatMessages(session.id, 1, 10);
      const recentMessages = messagesResult.success ? messagesResult.data || [] : [];

      // Get user profile info
      const { data: profileData } = await supabase
        .from('profiles')
        .select('full_name, bio, interests')
        .eq('id', session.user_id)
        .single();

      // Get conversation topics
      const { data: topicsData } = await supabase
        .from('topics')
        .select('topic, sentiment')
        .eq('chat_id', session.id)
        .order('created_at', { ascending: false })
        .limit(5);

      // Calculate session duration
      const startTime = new Date(session.started_at).getTime();
      const currentTime = session.ended_at ? new Date(session.ended_at).getTime() : Date.now();
      const durationMinutes = Math.floor((currentTime - startTime) / (1000 * 60));

      // Calculate sentiment trend
      const sentimentValues = topicsData?.map(topic => 
        topic.sentiment === 'positive' ? 1 : topic.sentiment === 'negative' ? -1 : 0
      ) || [];
      const sentimentTrend = sentimentValues.length > 0 
        ? sentimentValues.reduce((a, b) => a + b, 0) / sentimentValues.length 
        : 0;

      const context: ChatContext = {
        recent_messages: recentMessages,
        user_profile: {
          name: profileData?.full_name || undefined,
          bio: profileData?.bio || undefined,
          interests: profileData?.interests || undefined
        },
        conversation_history: {
          topics: topicsData?.map(t => t.topic) || [],
          sentiment_trend: sentimentTrend,
          key_points: [] // Could be extracted from message analysis
        },
        session_info: {
          duration_minutes: durationMinutes,
          message_count: session.total_messages,
          last_interaction: recentMessages[recentMessages.length - 1]?.created_at || session.started_at
        }
      };

      return { success: true, data: context };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'CONTEXT_BUILD_ERROR',
          message: error instanceof Error ? error.message : 'Failed to build chat context'
        }
      };
    }
  }

  async createChatAnalytics(chatId: string, userId: string): Promise<ChatAPIResponse<ChatAnalytics>> {
    try {
      // Get all messages for analysis
      const messagesResult = await this.getChatMessages(chatId);
      const messages = messagesResult.success ? messagesResult.data || [] : [];

      if (messages.length === 0) {
        throw new ChatError('No messages found for analytics', 'ANALYTICS_NO_DATA');
      }

      // Calculate metrics
      const messageCount = messages.length;
      const aiMessages = messages.filter(m => m.sender_type === 'ai');
      const userMessages = messages.filter(m => m.sender_type !== 'ai');

      // Calculate average response time (simplified)
      let totalResponseTime = 0;
      let responseCount = 0;

      for (let i = 1; i < messages.length; i++) {
        const prevMessage = messages[i - 1];
        const currentMessage = messages[i];
        
        if (prevMessage.sender_type !== 'ai' && currentMessage.sender_type === 'ai') {
          const responseTime = new Date(currentMessage.created_at).getTime() - 
                              new Date(prevMessage.created_at).getTime();
          totalResponseTime += responseTime;
          responseCount++;
        }
      }

      const avgResponseTime = responseCount > 0 ? totalResponseTime / responseCount : undefined;

      // Get conversation duration
      const firstMessage = messages[0];
      const lastMessage = messages[messages.length - 1];
      const duration = (new Date(lastMessage.created_at).getTime() - 
                       new Date(firstMessage.created_at).getTime()) / (1000 * 60);

      // Get topics from database
      const { data: topicsData } = await supabase
        .from('topics')
        .select('topic')
        .eq('chat_id', chatId);

      const analyticsData = {
        chat_id: chatId,
        user_id: userId,
        message_count: messageCount,
        avg_response_time_ms: avgResponseTime,
        conversation_duration_minutes: Math.max(duration, 0),
        topics_discussed: topicsData?.map(t => t.topic) || [],
        ai_performance_score: this.calculateAIPerformanceScore(aiMessages.length, userMessages.length),
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('chat_analytics')
        .insert(analyticsData)
        .select()
        .single();

      if (error) {
        throw new ChatError('Failed to create analytics', 'ANALYTICS_CREATE_ERROR', chatId, { error });
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ANALYTICS_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create chat analytics'
        }
      };
    }
  }

  async saveChatTopic(
    chatId: string, 
    userId: string, 
    topic: string, 
    sentiment: 'positive' | 'negative' | 'neutral',
    confidence: number,
    keywords: string[] = []
  ): Promise<ChatAPIResponse<ChatTopic>> {
    try {
      const topicData = {
        chat_id: chatId,
        user_id: userId,
        topic,
        sentiment,
        confidence,
        keywords,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('topics')
        .insert(topicData)
        .select()
        .single();

      if (error) {
        throw new ChatError('Failed to save topic', 'TOPIC_SAVE_ERROR', chatId, { error });
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'TOPIC_SAVE_ERROR',
          message: error instanceof Error ? error.message : 'Failed to save chat topic'
        }
      };
    }
  }

  async getUserTopics(
    userId: string,
    limit = 50
  ): Promise<ChatAPIResponse<ChatTopic[]>> {
    try {
      const { data, error } = await supabase
        .from('topics')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw new ChatError('Failed to fetch topics', 'TOPICS_FETCH_ERROR', undefined, { error });
      }

      return { success: true, data: (data || []) as ChatTopic[] };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'TOPICS_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch topics'
        }
      };
    }
  }

  // Real-time Events
  addEventListener(eventType: ChatEventType | 'all', callback: (event: ChatEvent) => void): void {
    const key = eventType;
    if (!this.eventListeners.has(key)) {
      this.eventListeners.set(key, new Set());
    }
    this.eventListeners.get(key)?.add(callback);
  }

  removeEventListener(eventType: ChatEventType | 'all', callback: (event: ChatEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emitEvent(event: ChatEvent): void {
    // Emit to specific event type listeners
    const typeListeners = this.eventListeners.get(event.type);
    typeListeners?.forEach(callback => callback(event));

    // Emit to 'all' event listeners
    const allListeners = this.eventListeners.get('all');
    allListeners?.forEach(callback => callback(event));
  }

  // Utility Methods
  private generateSessionId(): string {
    return `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async updateSessionMessageCount(chatId: string): Promise<void> {
    try {
      const { data: countData } = await supabase
        .from('chat_messages')
        .select('id', { count: 'exact' })
        .eq('chat_id', chatId);

      if (countData) {
        await supabase
          .from('chats')
          .update({ total_messages: countData.length })
          .eq('id', chatId);
      }
    } catch (error) {
      console.error('Failed to update session message count:', error);
    }
  }

  private calculateAIPerformanceScore(aiMessageCount: number, userMessageCount: number): number {
    if (userMessageCount === 0) return 0;
    
    // Simple scoring based on message ratio and engagement
    const responseRatio = aiMessageCount / userMessageCount;
    const baseScore = Math.min(responseRatio, 1) * 0.8; // Max 0.8 for perfect response ratio
    
    // Add bonus for engagement (more messages = higher engagement)
    const engagementBonus = Math.min(userMessageCount / 10, 0.2); // Max 0.2 bonus
    
    return Math.min(baseScore + engagementBonus, 1.0);
  }
}

// Export singleton instance
export const chatService = new ChatService();

// Export factory function for testing
export const createChatService = (): ChatService => new ChatService();