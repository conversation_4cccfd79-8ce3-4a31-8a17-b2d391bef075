// Referral Validation
// Validation utilities for referral codes, rewards, and fraud detection

import type {
  Referral,
  ReferralProgram,
  ReferralReward,
  ReferralLink,
  ReferralValidationRules,
  ReferralValidationResult,
  ReferralStatus,
  RewardType,
  PayoutMethod,
  UTMParams
} from '@/types/referral';
import { ReferralError, ReferralCodeError, FraudDetectionError } from '@/types/referral';

// Default validation rules
export const defaultReferralValidationRules: ReferralValidationRules = {
  referral_code: {
    min_length: 4,
    max_length: 12,
    allowed_chars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
    case_sensitive: false
  },
  rewards: {
    min_amount: 0.01,
    max_amount: 1000,
    auto_approval_threshold: 50
  },
  fraud_detection: {
    max_same_ip_referrals: 5,
    min_time_between_referrals: 24 * 60 * 60 * 1000, // 24 hours in ms
    suspicious_patterns: [
      'rapid_signup',
      'same_device',
      'similar_emails',
      'high_velocity',
      'bot_behavior'
    ]
  },
  payout: {
    min_payout_amount: 25,
    max_payout_amount: 10000,
    payout_frequency_days: 30
  }
};

// Suspicious patterns for fraud detection
const SUSPICIOUS_EMAIL_PATTERNS = [
  /^(.+)\+\d+@/, // Plus addressing
  /^(.+)\.{2,}(.+)@/, // Multiple dots
  /^(.+)_\d{3,}@/, // Underscore with many numbers
  /temp|trash|fake|test|spam/i, // Temporary email keywords
];

const SUSPICIOUS_IP_RANGES = [
  '10.0.0.0/8',
  '**********/12',
  '***********/16',
  '*********/8'
];

const BOT_USER_AGENTS = [
  'bot',
  'crawler',
  'spider',
  'scraper',
  'automated',
  'headless'
];

export class ReferralValidator {
  private rules: ReferralValidationRules;
  private ipCache: Map<string, { count: number; firstSeen: number }> = new Map();

  constructor(rules: ReferralValidationRules = defaultReferralValidationRules) {
    this.rules = rules;
  }

  // Referral Code Validation
  validateReferralCode(code: string): ReferralValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!code || typeof code !== 'string') {
      errors.push('Referral code is required');
      return { isValid: false, errors, warnings };
    }

    const cleanCode = this.rules.referral_code.case_sensitive ? code : code.toUpperCase();

    // Length validation
    if (cleanCode.length < this.rules.referral_code.min_length) {
      errors.push(`Referral code must be at least ${this.rules.referral_code.min_length} characters`);
    }

    if (cleanCode.length > this.rules.referral_code.max_length) {
      errors.push(`Referral code cannot exceed ${this.rules.referral_code.max_length} characters`);
    }

    // Character validation
    const allowedChars = this.rules.referral_code.allowed_chars;
    const invalidChars = cleanCode.split('').filter(char => !allowedChars.includes(char));
    
    if (invalidChars.length > 0) {
      errors.push(`Referral code contains invalid characters: ${[...new Set(invalidChars)].join(', ')}`);\n    }\n\n    // Pattern validation\n    if (/^[0-9]+$/.test(cleanCode)) {\n      warnings.push('Referral code is all numbers, consider including letters');\n    }\n\n    if (/^(.)\\1+$/.test(cleanCode)) {\n      warnings.push('Referral code contains repeating characters');\n    }\n\n    // Check for common words that might be confused\n    const confusingWords = ['NULL', 'NONE', 'TEST', 'ADMIN', 'ROOT'];\n    if (confusingWords.includes(cleanCode)) {\n      errors.push('Referral code cannot be a reserved word');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  // Referral Validation\n  validateReferral(referral: Partial<Referral>): ReferralValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n    const recommendedActions: string[] = [];\n\n    // Required fields\n    if (!referral.referrer_id) {\n      errors.push('Referrer ID is required');\n    }\n\n    if (!referral.referral_code) {\n      errors.push('Referral code is required');\n    } else {\n      const codeValidation = this.validateReferralCode(referral.referral_code);\n      errors.push(...codeValidation.errors);\n      warnings.push(...codeValidation.warnings);\n    }\n\n    // Status validation\n    if (referral.status) {\n      const validStatuses: ReferralStatus[] = [\n        'pending', 'active', 'converted', 'expired', 'cancelled', 'fraudulent'\n      ];\n      if (!validStatuses.includes(referral.status)) {\n        errors.push(`Invalid referral status: ${referral.status}`);\n      }\n    }\n\n    // Email validation (if provided)\n    if (referral.email) {\n      const emailValidation = this.validateEmail(referral.email);\n      if (!emailValidation.isValid) {\n        errors.push(...emailValidation.errors);\n        warnings.push(...emailValidation.warnings);\n      }\n    }\n\n    // Date validation\n    if (referral.signup_date && referral.conversion_date) {\n      const signupDate = new Date(referral.signup_date);\n      const conversionDate = new Date(referral.conversion_date);\n      \n      if (conversionDate < signupDate) {\n        errors.push('Conversion date cannot be before signup date');\n      }\n    }\n\n    // Expiration validation\n    if (referral.expires_at) {\n      const expirationDate = new Date(referral.expires_at);\n      const now = new Date();\n      \n      if (expirationDate <= now && referral.status === 'pending') {\n        warnings.push('Referral has expired');\n        recommendedActions.push('Update referral status to expired');\n      }\n    }\n\n    // Self-referral check\n    if (referral.referrer_id === referral.referred_user_id) {\n      errors.push('Self-referral is not allowed');\n    }\n\n    // Reward amount validation\n    if (referral.reward_earned !== undefined) {\n      if (referral.reward_earned < 0) {\n        errors.push('Reward earned cannot be negative');\n      }\n      if (referral.reward_earned > this.rules.rewards.max_amount) {\n        warnings.push(`Reward amount (${referral.reward_earned}) exceeds maximum (${this.rules.rewards.max_amount})`);\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings,\n      recommended_actions: recommendedActions.length > 0 ? recommendedActions : undefined\n    };\n  }\n\n  // Program Validation\n  validateReferralProgram(program: Partial<ReferralProgram>): ReferralValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // Required fields\n    if (!program.name || program.name.trim().length === 0) {\n      errors.push('Program name is required');\n    }\n\n    if (!program.reward_structure) {\n      errors.push('Reward structure is required');\n    } else {\n      const rewardValidation = this.validateRewardStructure(program.reward_structure);\n      errors.push(...rewardValidation.errors);\n      warnings.push(...rewardValidation.warnings);\n    }\n\n    // Payout validation\n    if (program.min_payout_amount !== undefined) {\n      if (program.min_payout_amount < this.rules.payout.min_payout_amount) {\n        warnings.push(`Minimum payout amount is below recommended minimum (${this.rules.payout.min_payout_amount})`);\n      }\n      if (program.min_payout_amount > this.rules.payout.max_payout_amount) {\n        errors.push(`Minimum payout amount exceeds maximum allowed (${this.rules.payout.max_payout_amount})`);\n      }\n    }\n\n    // Tracking duration validation\n    if (program.tracking_duration_days !== undefined) {\n      if (program.tracking_duration_days < 1) {\n        errors.push('Tracking duration must be at least 1 day');\n      }\n      if (program.tracking_duration_days > 365) {\n        warnings.push('Tracking duration longer than 1 year is unusual');\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  // Reward Structure Validation\n  validateRewardStructure(structure: any): ReferralValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // Type validation\n    const validTypes: RewardType[] = [\n      'cash', 'credit', 'discount', 'free_trial', 'upgrade', 'points', 'gift_card'\n    ];\n    if (!structure.type || !validTypes.includes(structure.type)) {\n      errors.push('Invalid or missing reward type');\n    }\n\n    // Amount validation\n    if (structure.amount !== undefined) {\n      if (structure.amount < this.rules.rewards.min_amount) {\n        errors.push(`Reward amount must be at least ${this.rules.rewards.min_amount}`);\n      }\n      if (structure.amount > this.rules.rewards.max_amount) {\n        errors.push(`Reward amount cannot exceed ${this.rules.rewards.max_amount}`);\n      }\n    }\n\n    // Percentage validation\n    if (structure.percentage !== undefined) {\n      if (structure.percentage < 0 || structure.percentage > 100) {\n        errors.push('Reward percentage must be between 0 and 100');\n      }\n    }\n\n    // Currency validation for cash rewards\n    if (structure.type === 'cash' && !structure.currency) {\n      errors.push('Currency is required for cash rewards');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  // Reward Validation\n  validateReward(reward: Partial<ReferralReward>): ReferralValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // Required fields\n    if (!reward.referral_id) {\n      errors.push('Referral ID is required');\n    }\n\n    if (!reward.referrer_id) {\n      errors.push('Referrer ID is required');\n    }\n\n    if (!reward.referred_user_id) {\n      errors.push('Referred user ID is required');\n    }\n\n    if (!reward.type) {\n      errors.push('Reward type is required');\n    }\n\n    // Amount validation\n    if (reward.amount !== undefined) {\n      if (reward.amount < this.rules.rewards.min_amount) {\n        errors.push(`Reward amount must be at least ${this.rules.rewards.min_amount}`);\n      }\n      if (reward.amount > this.rules.rewards.max_amount) {\n        errors.push(`Reward amount cannot exceed ${this.rules.rewards.max_amount}`);\n      }\n\n      // Auto-approval check\n      if (reward.amount > this.rules.rewards.auto_approval_threshold) {\n        warnings.push('Reward amount exceeds auto-approval threshold and will require manual review');\n      }\n    }\n\n    // Currency validation\n    if (reward.type === 'cash' && !reward.currency) {\n      errors.push('Currency is required for cash rewards');\n    }\n\n    // Self-reward check\n    if (reward.referrer_id === reward.referred_user_id) {\n      errors.push('Cannot reward self-referral');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  // Fraud Detection\n  detectFraud(referral: Partial<Referral>): ReferralValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n    let fraudScore = 0;\n    const detectedPatterns: string[] = [];\n\n    // IP address analysis\n    if (referral.metadata?.ip_address) {\n      const ipAnalysis = this.analyzeIpAddress(referral.metadata.ip_address);\n      fraudScore += ipAnalysis.score;\n      detectedPatterns.push(...ipAnalysis.patterns);\n    }\n\n    // Email analysis\n    if (referral.email) {\n      const emailAnalysis = this.analyzeEmail(referral.email);\n      fraudScore += emailAnalysis.score;\n      detectedPatterns.push(...emailAnalysis.patterns);\n    }\n\n    // User agent analysis\n    if (referral.metadata?.user_agent) {\n      const uaAnalysis = this.analyzeUserAgent(referral.metadata.user_agent);\n      fraudScore += uaAnalysis.score;\n      detectedPatterns.push(...uaAnalysis.patterns);\n    }\n\n    // Timing analysis\n    if (referral.created_at) {\n      const timingAnalysis = this.analyzeTiming(referral.created_at, referral.referrer_id);\n      fraudScore += timingAnalysis.score;\n      detectedPatterns.push(...timingAnalysis.patterns);\n    }\n\n    // Determine result based on fraud score\n    if (fraudScore >= 0.8) {\n      errors.push('High fraud risk detected');\n    } else if (fraudScore >= 0.5) {\n      warnings.push('Medium fraud risk detected');\n    } else if (fraudScore >= 0.3) {\n      warnings.push('Low fraud risk detected');\n    }\n\n    return {\n      isValid: fraudScore < 0.8,\n      errors,\n      warnings,\n      fraud_score: fraudScore,\n      recommended_actions: detectedPatterns.length > 0 ? [\n        'Manual review recommended',\n        ...detectedPatterns.map(pattern => `Detected: ${pattern}`)\n      ] : undefined\n    };\n  }\n\n  // Email validation with fraud detection\n  private validateEmail(email: string): ReferralValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // Basic email format validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(email)) {\n      errors.push('Invalid email format');\n      return { isValid: false, errors, warnings };\n    }\n\n    // Check for suspicious patterns\n    for (const pattern of SUSPICIOUS_EMAIL_PATTERNS) {\n      if (pattern.test(email)) {\n        warnings.push('Email appears to be temporary or suspicious');\n        break;\n      }\n    }\n\n    // Check domain\n    const domain = email.split('@')[1].toLowerCase();\n    const suspiciousDomains = [\n      '10minutemail.com',\n      'tempmail.org',\n      'guerrillamail.com',\n      'mailinator.com'\n    ];\n\n    if (suspiciousDomains.includes(domain)) {\n      warnings.push('Email domain is known for temporary emails');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  // IP address analysis\n  private analyzeIpAddress(ipAddress: string): { score: number; patterns: string[] } {\n    let score = 0;\n    const patterns: string[] = [];\n\n    // Check for private IP ranges\n    for (const range of SUSPICIOUS_IP_RANGES) {\n      if (this.isIpInRange(ipAddress, range)) {\n        score += 0.3;\n        patterns.push('private_ip_range');\n        break;\n      }\n    }\n\n    // Check IP frequency\n    const ipData = this.ipCache.get(ipAddress);\n    if (ipData) {\n      const timeSinceFirst = Date.now() - ipData.firstSeen;\n      const hoursSinceFirst = timeSinceFirst / (1000 * 60 * 60);\n      \n      if (ipData.count >= this.rules.fraud_detection.max_same_ip_referrals) {\n        score += 0.4;\n        patterns.push('high_ip_frequency');\n      }\n      \n      if (ipData.count >= 3 && hoursSinceFirst < 1) {\n        score += 0.3;\n        patterns.push('rapid_ip_usage');\n      }\n      \n      this.ipCache.set(ipAddress, { count: ipData.count + 1, firstSeen: ipData.firstSeen });\n    } else {\n      this.ipCache.set(ipAddress, { count: 1, firstSeen: Date.now() });\n    }\n\n    return { score: Math.min(score, 1.0), patterns };\n  }\n\n  // Email analysis for fraud patterns\n  private analyzeEmail(email: string): { score: number; patterns: string[] } {\n    let score = 0;\n    const patterns: string[] = [];\n\n    // Check for suspicious patterns\n    for (const pattern of SUSPICIOUS_EMAIL_PATTERNS) {\n      if (pattern.test(email)) {\n        score += 0.2;\n        patterns.push('suspicious_email_pattern');\n        break;\n      }\n    }\n\n    // Check for sequential patterns\n    const localPart = email.split('@')[0];\n    if (/\\d{3,}$/.test(localPart)) {\n      score += 0.1;\n      patterns.push('sequential_email_number');\n    }\n\n    return { score: Math.min(score, 1.0), patterns };\n  }\n\n  // User agent analysis\n  private analyzeUserAgent(userAgent: string): { score: number; patterns: string[] } {\n    let score = 0;\n    const patterns: string[] = [];\n\n    // Check for bot patterns\n    const lowerUA = userAgent.toLowerCase();\n    for (const botPattern of BOT_USER_AGENTS) {\n      if (lowerUA.includes(botPattern)) {\n        score += 0.5;\n        patterns.push('bot_user_agent');\n        break;\n      }\n    }\n\n    // Check for empty or suspicious user agents\n    if (!userAgent || userAgent.length < 10) {\n      score += 0.3;\n      patterns.push('suspicious_user_agent');\n    }\n\n    return { score: Math.min(score, 1.0), patterns };\n  }\n\n  // Timing analysis\n  private analyzeTiming(createdAt: string, referrerId?: string): { score: number; patterns: string[] } {\n    let score = 0;\n    const patterns: string[] = [];\n\n    const creationTime = new Date(createdAt).getTime();\n    const now = Date.now();\n    const timeDiff = now - creationTime;\n\n    // Check if referral was created too quickly\n    if (timeDiff < 5000) { // Less than 5 seconds\n      score += 0.3;\n      patterns.push('very_rapid_signup');\n    } else if (timeDiff < 30000) { // Less than 30 seconds\n      score += 0.1;\n      patterns.push('rapid_signup');\n    }\n\n    return { score: Math.min(score, 1.0), patterns };\n  }\n\n  // Utility method to check if IP is in range\n  private isIpInRange(ip: string, range: string): boolean {\n    // Simplified IP range check - would use proper IP library in production\n    return range.includes(ip.split('.')[0]);\n  }\n\n  // UTM Parameters validation\n  validateUTMParams(utmParams: UTMParams): ReferralValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // Check for valid UTM parameter formats\n    if (utmParams.utm_source && utmParams.utm_source.length > 100) {\n      warnings.push('UTM source is unusually long');\n    }\n\n    if (utmParams.utm_campaign && utmParams.utm_campaign.length > 100) {\n      warnings.push('UTM campaign is unusually long');\n    }\n\n    // Check for suspicious patterns in UTM params\n    const suspiciousPatterns = ['<script', 'javascript:', 'data:'];\n    Object.values(utmParams).forEach(value => {\n      if (value && suspiciousPatterns.some(pattern => value.includes(pattern))) {\n        errors.push('UTM parameters contain suspicious content');\n      }\n    });\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  // Payout method validation\n  validatePayoutMethod(method: PayoutMethod, details?: Record<string, unknown>): ReferralValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    const validMethods: PayoutMethod[] = [\n      'bank_transfer', 'paypal', 'stripe', 'crypto', 'gift_card', 'account_credit'\n    ];\n\n    if (!validMethods.includes(method)) {\n      errors.push(`Invalid payout method: ${method}`);\n    }\n\n    // Method-specific validation\n    switch (method) {\n      case 'paypal':\n        if (details?.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(details.email as string)) {\n          errors.push('Invalid PayPal email format');\n        }\n        break;\n      \n      case 'bank_transfer':\n        if (!details?.account_number || !details?.routing_number) {\n          errors.push('Bank transfer requires account and routing numbers');\n        }\n        break;\n      \n      case 'crypto':\n        if (!details?.wallet_address) {\n          errors.push('Crypto payout requires wallet address');\n        }\n        break;\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  // Rule Management\n  updateRules(newRules: Partial<ReferralValidationRules>): void {\n    this.rules = { ...this.rules, ...newRules };\n  }\n\n  getRules(): ReferralValidationRules {\n    return { ...this.rules };\n  }\n\n  // Clear IP cache (for testing or maintenance)\n  clearIpCache(): void {\n    this.ipCache.clear();\n  }\n}\n\n// Utility functions\nexport const validateReferralCode = (\n  code: string,\n  rules?: ReferralValidationRules\n): ReferralValidationResult => {\n  const validator = new ReferralValidator(rules);\n  return validator.validateReferralCode(code);\n};\n\nexport const validateReferral = (\n  referral: Partial<Referral>,\n  rules?: ReferralValidationRules\n): ReferralValidationResult => {\n  const validator = new ReferralValidator(rules);\n  return validator.validateReferral(referral);\n};\n\nexport const detectReferralFraud = (\n  referral: Partial<Referral>,\n  rules?: ReferralValidationRules\n): ReferralValidationResult => {\n  const validator = new ReferralValidator(rules);\n  return validator.detectFraud(referral);\n};\n\n// Referral code generation utility\nexport const generateReferralCode = (userId: string, length = 8): string => {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n  const userPart = userId.slice(-2).toUpperCase();\n  const randomPart = Array.from({ length: length - 2 }, () => \n    chars.charAt(Math.floor(Math.random() * chars.length))\n  ).join('');\n  \n  return userPart + randomPart;\n};\n\n// Safe referral content sanitizer\nexport const sanitizeReferralContent = (content: string): string => {\n  return content\n    .replace(/<script[\\s\\S]*?<\\/script>/gi, '')\n    .replace(/javascript:/gi, '')\n    .replace(/data:/gi, '')\n    .replace(/on\\w+=/gi, '')\n    .trim();\n};\n\n// Format validation errors for API responses\nexport const formatReferralValidationErrors = (result: ReferralValidationResult): ReferralError => {\n  return new ReferralError(\n    result.errors.join(', '),\n    'VALIDATION_ERROR'\n  );\n};\n\n// Export singleton validator instance\nexport const referralValidator = new ReferralValidator();