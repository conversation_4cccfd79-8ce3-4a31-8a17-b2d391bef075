// Chat Validation
// Validation utilities for chat messages, sessions, and content

import type { 
  ChatMessage,
  ChatSession,
  Visitor,
  ChatValidationRules,
  MessageValidationResult,
  MessageType,
  ChatStatus
} from '@/types/chat';
import { MessageValidationError } from '@/types/chat';

// Default validation rules
export const defaultChatValidationRules: ChatValidationRules = {
  message: {
    min_length: 1,
    max_length: 2000,
    allowed_types: ['text', 'image', 'audio', 'video', 'file', 'system'],
    rate_limit_per_minute: 30
  },
  session: {
    max_duration_hours: 24,
    max_messages_per_session: 1000,
    idle_timeout_minutes: 30
  },
  content: {
    profanity_filter: true,
    spam_detection: true,
    personal_info_detection: true
  }
};

// Profanity filter words (basic list - would be expanded in production)
const PROFANITY_WORDS = [
  'spam', 'scam', 'fake', 'fraud', 'hack', 'illegal',
  // Add more words as needed
];

// Personal info patterns
const PERSONAL_INFO_PATTERNS = [
  /\b\d{3}-\d{3}-\d{4}\b/, // Phone numbers
  /\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/, // Credit card numbers
  /\b\d{3}-\d{2}-\d{4}\b/, // SSN pattern
  /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email addresses
];

// Spam patterns
const SPAM_PATTERNS = [
  /click\s+here/i,
  /free\s+money/i,
  /guaranteed\s+income/i,
  /make\s+money\s+fast/i,
  /limited\s+time\s+offer/i,
  /act\s+now/i,
  /urgent/i,
];

export class ChatValidator {
  private rules: ChatValidationRules;
  private messageRateTracker: Map<string, number[]> = new Map();

  constructor(rules: ChatValidationRules = defaultChatValidationRules) {
    this.rules = rules;
  }

  // Message Validation
  validateMessage(message: Partial<ChatMessage>, senderId?: string): MessageValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];
    let filteredContent = message.content;

    // Required fields validation
    if (!message.content || typeof message.content !== 'string') {
      errors.push('Message content is required');
    }

    if (!message.sender_type) {
      errors.push('Sender type is required');
    }

    if (!message.chat_id) {
      errors.push('Chat ID is required');
    }

    if (!message.sender_id) {
      errors.push('Sender ID is required');
    }

    if (message.content) {
      // Length validation
      if (message.content.length < this.rules.message.min_length) {
        errors.push(`Message must be at least ${this.rules.message.min_length} characters long`);
      }

      if (message.content.length > this.rules.message.max_length) {
        errors.push(`Message cannot exceed ${this.rules.message.max_length} characters`);
      }

      // Message type validation
      if (message.message_type && !this.rules.message.allowed_types.includes(message.message_type)) {
        errors.push(`Message type '${message.message_type}' is not allowed`);
      }

      // Content validation
      if (this.rules.content.profanity_filter) {
        const profanityResult = this.checkProfanity(message.content);
        if (profanityResult.hasProfanity) {
          warnings.push('Message contains inappropriate language');
          filteredContent = profanityResult.filteredContent;
        }
      }

      if (this.rules.content.spam_detection) {
        if (this.checkSpam(message.content)) {
          warnings.push('Message appears to be spam');
          suggestions.push('Please avoid promotional language and focus on genuine conversation');
        }
      }

      if (this.rules.content.personal_info_detection) {
        const personalInfoResult = this.checkPersonalInfo(message.content);
        if (personalInfoResult.hasPersonalInfo) {
          warnings.push('Message may contain personal information');
          suggestions.push('Avoid sharing personal details like phone numbers, emails, or financial information');
          filteredContent = personalInfoResult.filteredContent;
        }
      }

      // Rate limiting
      if (senderId) {
        if (this.checkRateLimit(senderId)) {
          errors.push('Rate limit exceeded. Please slow down your messaging');
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      filtered_content: filteredContent
    };
  }

  // Session Validation
  validateSession(session: Partial<ChatSession>): MessageValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!session.user_id) {
      errors.push('User ID is required');
    }

    if (!session.visitor_id) {
      errors.push('Visitor ID is required');
    }

    if (!session.session_id) {
      errors.push('Session ID is required');
    }

    // Status validation
    const validStatuses: ChatStatus[] = ['active', 'archived', 'deleted', 'paused', 'ended'];
    if (session.status && !validStatuses.includes(session.status)) {
      errors.push(`Invalid session status: ${session.status}`);
    }

    // Duration validation
    if (session.started_at && session.ended_at) {
      const duration = new Date(session.ended_at).getTime() - new Date(session.started_at).getTime();
      const maxDuration = this.rules.session.max_duration_hours * 60 * 60 * 1000;
      
      if (duration > maxDuration) {
        warnings.push(`Session duration exceeds ${this.rules.session.max_duration_hours} hours`);
      }
    }

    // Message count validation
    if (session.total_messages && session.total_messages > this.rules.session.max_messages_per_session) {
      warnings.push(`Session has exceeded maximum message limit of ${this.rules.session.max_messages_per_session}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Visitor Validation
  validateVisitor(visitor: Partial<Visitor>): MessageValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!visitor.profile_owner_id) {
      errors.push('Profile owner ID is required');
    }

    // Email validation
    if (visitor.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(visitor.email)) {
        errors.push('Invalid email format');
      }
    }

    // Display name validation
    if (visitor.display_name) {
      if (visitor.display_name.length < 1 || visitor.display_name.length > 50) {
        errors.push('Display name must be between 1 and 50 characters');
      }

      // Check for inappropriate content in display name
      if (this.checkProfanity(visitor.display_name).hasProfanity) {
        errors.push('Display name contains inappropriate content');
      }
    }

    // Visit counts validation
    if (visitor.total_visits !== undefined && visitor.total_visits < 0) {
      errors.push('Total visits cannot be negative');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Content validation helpers
  private checkProfanity(content: string): { hasProfanity: boolean; filteredContent: string } {
    let filteredContent = content;
    let hasProfanity = false;

    for (const word of PROFANITY_WORDS) {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      if (regex.test(content)) {
        hasProfanity = true;
        filteredContent = filteredContent.replace(regex, '*'.repeat(word.length));
      }
    }

    return { hasProfanity, filteredContent };
  }

  private checkSpam(content: string): boolean {
    return SPAM_PATTERNS.some(pattern => pattern.test(content));
  }

  private checkPersonalInfo(content: string): { hasPersonalInfo: boolean; filteredContent: string } {
    let filteredContent = content;
    let hasPersonalInfo = false;

    for (const pattern of PERSONAL_INFO_PATTERNS) {
      if (pattern.test(content)) {
        hasPersonalInfo = true;
        filteredContent = filteredContent.replace(pattern, '[REDACTED]');
      }
    }

    return { hasPersonalInfo, filteredContent };
  }

  private checkRateLimit(senderId: string): boolean {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Get or create rate tracking array for this sender
    if (!this.messageRateTracker.has(senderId)) {
      this.messageRateTracker.set(senderId, []);
    }

    const timestamps = this.messageRateTracker.get(senderId)!;

    // Remove timestamps older than 1 minute
    const recentTimestamps = timestamps.filter(timestamp => timestamp > oneMinuteAgo);
    this.messageRateTracker.set(senderId, recentTimestamps);

    // Check if rate limit exceeded
    if (recentTimestamps.length >= this.rules.message.rate_limit_per_minute) {
      return true;
    }

    // Add current timestamp
    recentTimestamps.push(now);
    this.messageRateTracker.set(senderId, recentTimestamps);

    return false;
  }

  // Validation rule management
  updateRules(newRules: Partial<ChatValidationRules>): void {
    this.rules = { ...this.rules, ...newRules };
  }

  getRules(): ChatValidationRules {
    return { ...this.rules };
  }

  // Cleanup old rate limit data
  cleanupRateTracker(): void {
    const oneMinuteAgo = Date.now() - 60000;
    
    for (const [senderId, timestamps] of this.messageRateTracker.entries()) {
      const recentTimestamps = timestamps.filter(timestamp => timestamp > oneMinuteAgo);
      
      if (recentTimestamps.length === 0) {
        this.messageRateTracker.delete(senderId);
      } else {
        this.messageRateTracker.set(senderId, recentTimestamps);
      }
    }
  }
}

// Utility functions
export const validateChatMessage = (
  message: Partial<ChatMessage>, 
  rules?: ChatValidationRules
): MessageValidationResult => {
  const validator = new ChatValidator(rules);
  return validator.validateMessage(message);
};

export const validateChatSession = (
  session: Partial<ChatSession>, 
  rules?: ChatValidationRules
): MessageValidationResult => {
  const validator = new ChatValidator(rules);
  return validator.validateSession(session);
};

export const validateVisitor = (
  visitor: Partial<Visitor>, 
  rules?: ChatValidationRules
): MessageValidationResult => {
  const validator = new ChatValidator(rules);
  return validator.validateVisitor(visitor);
};

// Safe message content sanitizer
export const sanitizeMessageContent = (content: string): string => {
  const validator = new ChatValidator();
  const result = validator.validateMessage({ content });
  return result.filtered_content || content;
};

// Check if content is safe for public display
export const isContentSafe = (content: string): boolean => {
  const validator = new ChatValidator();
  const result = validator.validateMessage({ content });
  return result.isValid && result.warnings.length === 0;
};

// Extract validation errors for API responses
export const formatValidationErrors = (result: MessageValidationResult): MessageValidationError => {
  return new MessageValidationError(
    result.errors.join(', '),
    result.errors
  );
};

// Export singleton validator instance
export const chatValidator = new ChatValidator();