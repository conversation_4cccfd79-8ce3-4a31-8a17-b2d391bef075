// Error Handling System
// Comprehensive error handling, logging, and recovery mechanisms

import { supabase } from '@/integrations/supabase/client';

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  NETWORK = 'network',
  DATABASE = 'database',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  BUSINESS_LOGIC = 'business_logic',
  EXTERNAL_SERVICE = 'external_service',
  UI = 'ui',
  PERFORMANCE = 'performance',
  SECURITY = 'security'
}

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, unknown>;
  userAgent?: string;
  url?: string;
  timestamp?: Date;
  stackTrace?: string;
  breadcrumbs?: ErrorBreadcrumb[];
}

export interface ErrorBreadcrumb {
  timestamp: Date;
  category: string;
  message: string;
  level: 'info' | 'warning' | 'error';
  data?: Record<string, unknown>;
}

export interface ErrorReport {
  id: string;
  message: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  context: ErrorContext;
  fingerprint: string;
  count: number;
  firstOccurred: Date;
  lastOccurred: Date;
  resolved: boolean;
  resolvedAt?: Date;
  resolution?: string;
}

export interface ErrorRecoveryStrategy {
  name: string;
  description: string;
  canRecover: (error: AppError) => boolean;
  recover: (error: AppError) => Promise<RecoveryResult>;
  maxAttempts: number;
  backoffMs: number;
}

export interface RecoveryResult {
  success: boolean;
  retryAfter?: number;
  fallbackData?: unknown;
  message?: string;
}

export class AppError extends Error {
  public readonly id: string;
  public readonly severity: ErrorSeverity;
  public readonly category: ErrorCategory;
  public readonly context: ErrorContext;
  public readonly timestamp: Date;
  public readonly recoverable: boolean;
  public retryCount: number = 0;
  public maxRetries: number = 3;

  constructor(
    message: string,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.BUSINESS_LOGIC,
    context: ErrorContext = {},
    recoverable: boolean = false
  ) {
    super(message);
    this.name = 'AppError';
    this.id = this.generateErrorId();
    this.severity = severity;
    this.category = category;
    this.context = {
      ...context,
      timestamp: new Date(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined
    };
    this.timestamp = new Date();
    this.recoverable = recoverable;
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
    this.context.stackTrace = this.stack;
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  toJSON(): Record<string, unknown> {
    return {
      id: this.id,
      name: this.name,
      message: this.message,
      severity: this.severity,
      category: this.category,
      context: this.context,
      timestamp: this.timestamp,
      recoverable: this.recoverable,
      retryCount: this.retryCount,
      stack: this.stack
    };
  }
}

export class NetworkError extends AppError {
  constructor(message: string, context: ErrorContext = {}, public statusCode?: number) {
    super(
      message,
      statusCode && statusCode >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM,
      ErrorCategory.NETWORK,
      { ...context, statusCode },
      true
    );
    this.name = 'NetworkError';
  }
}

export class DatabaseError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(message, ErrorSeverity.HIGH, ErrorCategory.DATABASE, context, false);
    this.name = 'DatabaseError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string, context: ErrorContext = {}, public field?: string) {
    super(message, ErrorSeverity.LOW, ErrorCategory.VALIDATION, { ...context, field }, false);
    this.name = 'ValidationError';
  }
}

export class SecurityError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(message, ErrorSeverity.CRITICAL, ErrorCategory.SECURITY, context, false);
    this.name = 'SecurityError';
  }
}

export class ErrorHandler {
  private breadcrumbs: ErrorBreadcrumb[] = [];
  private errorReports: Map<string, ErrorReport> = new Map();
  private recoveryStrategies: Map<string, ErrorRecoveryStrategy> = new Map();
  private maxBreadcrumbs: number = 50;
  private notificationCallbacks: Set<(error: AppError) => void> = new Set();
  private isOnline: boolean = navigator.onLine;

  constructor() {
    this.setupGlobalErrorHandling();
    this.setupRecoveryStrategies();
    this.setupNetworkMonitoring();
  }

  private setupGlobalErrorHandling(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = new AppError(
        `Unhandled Promise Rejection: ${event.reason}`,
        ErrorSeverity.HIGH,
        ErrorCategory.BUSINESS_LOGIC,
        {
          component: 'global',
          action: 'promise_rejection',
          metadata: { reason: event.reason }
        }
      );
      this.handleError(error);
    });

    // Handle uncaught errors
    window.addEventListener('error', (event) => {
      const error = new AppError(
        `Uncaught Error: ${event.message}`,
        ErrorSeverity.HIGH,
        ErrorCategory.UI,
        {
          component: 'global',
          action: 'uncaught_error',
          metadata: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
          }
        }
      );
      this.handleError(error);
    });

    // Handle React errors via window object
    (window as any).__errorHandler = this;
  }

  private setupNetworkMonitoring(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.addBreadcrumb('network', 'Connection restored', 'info');
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.addBreadcrumb('network', 'Connection lost', 'warning');
    });
  }

  private setupRecoveryStrategies(): void {
    // Network retry strategy
    this.addRecoveryStrategy({
      name: 'network_retry',
      description: 'Retry network requests with exponential backoff',
      canRecover: (error) => error instanceof NetworkError && error.recoverable,
      recover: async (error) => {
        const networkError = error as NetworkError;
        
        if (!this.isOnline) {
          return { success: false, retryAfter: 5000, message: 'No internet connection' };
        }

        if (networkError.statusCode && networkError.statusCode >= 500) {
          const backoffMs = Math.min(1000 * Math.pow(2, error.retryCount), 30000);
          return { success: false, retryAfter: backoffMs };
        }

        return { success: false, message: 'Network error cannot be recovered' };
      },
      maxAttempts: 3,
      backoffMs: 1000
    });

    // Cache fallback strategy
    this.addRecoveryStrategy({
      name: 'cache_fallback',
      description: 'Fallback to cached data when available',
      canRecover: (error) => error.category === ErrorCategory.NETWORK,
      recover: async (error) => {
        try {
          const cachedData = await this.getCachedData(error.context.action || 'unknown');
          if (cachedData) {
            return { success: true, fallbackData: cachedData };
          }
        } catch (cacheError) {
          // Cache error, continue with failure
        }
        return { success: false };
      },
      maxAttempts: 1,
      backoffMs: 0
    });

    // Auth refresh strategy
    this.addRecoveryStrategy({
      name: 'auth_refresh',
      description: 'Refresh authentication tokens',
      canRecover: (error) => error.category === ErrorCategory.AUTHENTICATION,
      recover: async (error) => {
        try {
          const { error: refreshError } = await supabase.auth.refreshSession();
          if (!refreshError) {
            return { success: true };
          }
          return { success: false, message: 'Token refresh failed' };
        } catch (refreshError) {
          return { success: false, message: 'Token refresh error' };
        }
      },
      maxAttempts: 1,
      backoffMs: 0
    });
  }

  // Error Handling Methods
  public async handleError(error: Error | AppError): Promise<void> {
    let appError: AppError;

    if (error instanceof AppError) {
      appError = error;
    } else {
      appError = new AppError(
        error.message,
        ErrorSeverity.MEDIUM,
        ErrorCategory.BUSINESS_LOGIC,
        {
          component: 'unknown',
          action: 'error_conversion',
          stackTrace: error.stack
        }
      );
    }

    // Add to breadcrumbs
    this.addBreadcrumb(
      appError.category,
      appError.message,
      'error',
      { errorId: appError.id, severity: appError.severity }
    );

    // Update or create error report
    await this.updateErrorReport(appError);

    // Attempt recovery if applicable
    if (appError.recoverable && appError.retryCount < appError.maxRetries) {
      const recoveryResult = await this.attemptRecovery(appError);
      if (recoveryResult.success) {
        this.addBreadcrumb('recovery', `Recovered from ${appError.category} error`, 'info');
        return;
      }
    }

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('AppError:', appError.toJSON());
    }

    // Send to logging service
    await this.logError(appError);

    // Notify listeners
    this.notifyErrorListeners(appError);

    // Send to monitoring service if critical
    if (appError.severity === ErrorSeverity.CRITICAL) {
      await this.sendToMonitoring(appError);
    }
  }

  private async attemptRecovery(error: AppError): Promise<RecoveryResult> {
    for (const strategy of this.recoveryStrategies.values()) {
      if (strategy.canRecover(error)) {
        try {
          error.retryCount++;
          const result = await strategy.recover(error);
          
          if (result.success) {
            this.addBreadcrumb(
              'recovery',
              `Recovery successful with strategy: ${strategy.name}`,
              'info'
            );
            return result;
          } else if (result.retryAfter) {
            // Schedule retry
            setTimeout(() => {
              this.handleError(error);
            }, result.retryAfter);
          }
          
          return result;
        } catch (recoveryError) {
          this.addBreadcrumb(
            'recovery',
            `Recovery failed with strategy: ${strategy.name}`,
            'warning',
            { error: recoveryError instanceof Error ? recoveryError.message : 'Unknown' }
          );
        }
      }
    }

    return { success: false };
  }

  private async updateErrorReport(error: AppError): Promise<void> {
    const fingerprint = this.generateErrorFingerprint(error);
    const existing = this.errorReports.get(fingerprint);

    if (existing) {
      existing.count++;
      existing.lastOccurred = new Date();
    } else {
      const report: ErrorReport = {
        id: error.id,
        message: error.message,
        severity: error.severity,
        category: error.category,
        context: error.context,
        fingerprint,
        count: 1,
        firstOccurred: new Date(),
        lastOccurred: new Date(),
        resolved: false
      };
      this.errorReports.set(fingerprint, report);
    }
  }

  private generateErrorFingerprint(error: AppError): string {
    const key = `${error.category}:${error.message}:${error.context.component}:${error.context.action}`;
    return btoa(key).substring(0, 16);
  }

  private async logError(error: AppError): Promise<void> {
    try {
      await supabase
        .from('error_logs')
        .insert({
          error_id: error.id,
          message: error.message,
          severity: error.severity,
          category: error.category,
          context: error.context,
          user_id: error.context.userId,
          session_id: error.context.sessionId,
          fingerprint: this.generateErrorFingerprint(error),
          created_at: new Date().toISOString()
        });
    } catch (logError) {
      console.error('Failed to log error to database:', logError);
      // Store in local storage as fallback
      this.storeErrorLocally(error);
    }
  }

  private storeErrorLocally(error: AppError): void {
    try {
      const stored = localStorage.getItem('stored_errors') || '[]';
      const errors = JSON.parse(stored);
      errors.push(error.toJSON());
      
      // Keep only last 100 errors
      if (errors.length > 100) {
        errors.splice(0, errors.length - 100);
      }
      
      localStorage.setItem('stored_errors', JSON.stringify(errors));
    } catch (storageError) {
      console.error('Failed to store error locally:', storageError);
    }
  }

  private async sendToMonitoring(error: AppError): Promise<void> {
    // This would integrate with external monitoring services like Sentry, DataDog, etc.
    try {
      // Example: Send to external monitoring service
      // await fetch('/api/monitoring/error', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(error.toJSON())
      // });
      
      console.warn('Critical error detected:', error.toJSON());
    } catch (monitoringError) {
      console.error('Failed to send error to monitoring service:', monitoringError);
    }
  }

  // Breadcrumb Management
  public addBreadcrumb(
    category: string,
    message: string,
    level: 'info' | 'warning' | 'error' = 'info',
    data?: Record<string, unknown>
  ): void {
    const breadcrumb: ErrorBreadcrumb = {
      timestamp: new Date(),
      category,
      message,
      level,
      data
    };

    this.breadcrumbs.push(breadcrumb);

    // Keep only the most recent breadcrumbs
    if (this.breadcrumbs.length > this.maxBreadcrumbs) {
      this.breadcrumbs.shift();
    }
  }

  public getBreadcrumbs(): ErrorBreadcrumb[] {
    return [...this.breadcrumbs];
  }

  public clearBreadcrumbs(): void {
    this.breadcrumbs = [];
  }

  // Recovery Strategy Management
  public addRecoveryStrategy(strategy: ErrorRecoveryStrategy): void {
    this.recoveryStrategies.set(strategy.name, strategy);
  }

  public removeRecoveryStrategy(name: string): void {
    this.recoveryStrategies.delete(name);
  }

  // Error Notification
  public onError(callback: (error: AppError) => void): () => void {
    this.notificationCallbacks.add(callback);
    return () => this.notificationCallbacks.delete(callback);
  }

  private notifyErrorListeners(error: AppError): void {
    this.notificationCallbacks.forEach(callback => {
      try {
        callback(error);
      } catch (callbackError) {
        console.error('Error in error notification callback:', callbackError);
      }
    });
  }

  // Error Reports
  public getErrorReports(): ErrorReport[] {
    return Array.from(this.errorReports.values());
  }

  public resolveError(fingerprint: string, resolution: string): void {
    const report = this.errorReports.get(fingerprint);
    if (report) {
      report.resolved = true;
      report.resolvedAt = new Date();
      report.resolution = resolution;
    }
  }

  // Cache Management
  private async getCachedData(key: string): Promise<unknown | null> {
    try {
      const cached = localStorage.getItem(`cache_${key}`);
      if (cached) {
        const data = JSON.parse(cached);
        const now = Date.now();
        
        // Check if cache is still valid (1 hour)
        if (now - data.timestamp < 3600000) {
          return data.value;
        }
      }
    } catch (cacheError) {
      console.warn('Cache retrieval failed:', cacheError);
    }
    return null;
  }

  public setCachedData(key: string, value: unknown): void {
    try {
      const data = {
        value,
        timestamp: Date.now()
      };
      localStorage.setItem(`cache_${key}`, JSON.stringify(data));
    } catch (cacheError) {
      console.warn('Cache storage failed:', cacheError);
    }
  }

  // Context Management
  public setContext(updates: Partial<ErrorContext>): void {
    // Store context for future errors
    const context = this.getStoredContext();
    Object.assign(context, updates);
    sessionStorage.setItem('error_context', JSON.stringify(context));
  }

  private getStoredContext(): ErrorContext {
    try {
      const stored = sessionStorage.getItem('error_context');
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  }

  // Performance Monitoring
  public trackPerformance(name: string, duration: number, metadata?: Record<string, unknown>): void {
    if (duration > 5000) { // Log slow operations (>5s)
      const error = new AppError(
        `Slow operation detected: ${name} took ${duration}ms`,
        ErrorSeverity.LOW,
        ErrorCategory.PERFORMANCE,
        {
          component: 'performance',
          action: name,
          metadata: { ...metadata, duration }
        }
      );
      this.handleError(error);
    }
  }

  // Utility Methods
  public createError(
    message: string,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.BUSINESS_LOGIC,
    context: ErrorContext = {}
  ): AppError {
    return new AppError(message, severity, category, {
      ...this.getStoredContext(),
      ...context
    });
  }

  public async syncOfflineErrors(): Promise<void> {
    try {
      const stored = localStorage.getItem('stored_errors');
      if (stored) {
        const errors = JSON.parse(stored);
        
        for (const errorData of errors) {
          await supabase
            .from('error_logs')
            .insert({
              error_id: errorData.id,
              message: errorData.message,
              severity: errorData.severity,
              category: errorData.category,
              context: errorData.context,
              user_id: errorData.context.userId,
              session_id: errorData.context.sessionId,
              fingerprint: this.generateErrorFingerprint(errorData),
              created_at: errorData.timestamp
            });
        }
        
        // Clear stored errors after sync
        localStorage.removeItem('stored_errors');
      }
    } catch (syncError) {
      console.error('Failed to sync offline errors:', syncError);
    }
  }
}

// Export singleton instance
export const errorHandler = new ErrorHandler();

// Convenience functions
export const handleError = (error: Error | AppError): Promise<void> => {
  return errorHandler.handleError(error);
};

export const createError = (
  message: string,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  category: ErrorCategory = ErrorCategory.BUSINESS_LOGIC,
  context: ErrorContext = {}
): AppError => {
  return errorHandler.createError(message, severity, category, context);
};

export const addBreadcrumb = (
  category: string,
  message: string,
  level: 'info' | 'warning' | 'error' = 'info',
  data?: Record<string, unknown>
): void => {
  errorHandler.addBreadcrumb(category, message, level, data);
};

export const trackPerformance = (
  name: string,
  duration: number,
  metadata?: Record<string, unknown>
): void => {
  errorHandler.trackPerformance(name, duration, metadata);
};

export const setErrorContext = (context: Partial<ErrorContext>): void => {
  errorHandler.setContext(context);
};