// Language Manager Tests
// Comprehensive tests for language management system

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { LanguageManager } from '../languageManager';

// Mock Supabase
const mockSupabase = {
  auth: {
    getUser: vi.fn().mockResolvedValue({ data: { user: null } })
  },
  from: vi.fn(() => ({
    select: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn().mockResolvedValue({ data: null, error: null }),
    order: vi.fn().mockReturnThis(),
    upsert: vi.fn().mockResolvedValue({ data: null, error: null }),
    update: vi.fn().mockReturnThis()
  }))
};

vi.mock('@/integrations/supabase/client', () => ({
  supabase: mockSupabase
}));

// Mock dynamic imports
vi.mock('@/i18n/locales/en.json', () => ({
  default: {
    common: {
      welcome: 'Welcome',
      goodbye: 'Goodbye'
    },
    auth: {
      login: 'Login',
      logout: 'Logout'
    },
    plural_test: {
      value: 'You have {{count}} item',
      one: 'You have {{count}} item',
      other: 'You have {{count}} items'
    }
  }
}));

vi.mock('@/i18n/locales/es.json', () => ({
  default: {
    common: {
      welcome: 'Bienvenido',
      goodbye: 'Adiós'
    },
    auth: {
      login: 'Iniciar sesión',
      logout: 'Cerrar sesión'
    },
    plural_test: {
      value: 'Tienes {{count}} elemento',
      one: 'Tienes {{count}} elemento',
      other: 'Tienes {{count}} elementos'
    }
  }
}));

describe('LanguageManager', () => {
  let languageManager: LanguageManager;
  let mockLocalStorage: { [key: string]: string };
  let mockSessionStorage: { [key: string]: string };

  beforeEach(() => {
    languageManager = new LanguageManager();
    
    // Mock localStorage
    mockLocalStorage = {};
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn((key: string) => mockLocalStorage[key] || null),
        setItem: vi.fn((key: string, value: string) => {
          mockLocalStorage[key] = value;
        }),
        removeItem: vi.fn((key: string) => {
          delete mockLocalStorage[key];
        })
      },
      writable: true
    });

    // Mock sessionStorage
    mockSessionStorage = {};
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: vi.fn((key: string) => mockSessionStorage[key] || null),
        setItem: vi.fn((key: string, value: string) => {
          mockSessionStorage[key] = value;
        })
      },
      writable: true
    });

    // Mock navigator
    Object.defineProperty(navigator, 'languages', {
      value: ['en-US', 'en'],
      writable: true
    });

    Object.defineProperty(navigator, 'language', {
      value: 'en-US',
      writable: true
    });

    // Mock document
    Object.defineProperty(document, 'documentElement', {
      value: {
        dir: '',
        lang: ''
      },
      writable: true
    });

    // Mock geolocation
    Object.defineProperty(navigator, 'geolocation', {
      value: {
        getCurrentPosition: vi.fn()
      },
      writable: true
    });

    // Mock fetch for geolocation API
    global.fetch = vi.fn();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with default language configs', async () => {
      await languageManager.initialize();
      
      const configs = languageManager.getAvailableLanguages();
      expect(configs.length).toBeGreaterThan(0);
      
      const englishConfig = configs.find(c => c.code === 'en');
      expect(englishConfig).toBeDefined();
      expect(englishConfig?.name).toBe('English');
      expect(englishConfig?.isActive).toBe(true);
    });

    it('should load language configs from database when available', async () => {
      const mockConfigs = [
        {
          code: 'en',
          name: 'English',
          native_name: 'English',
          flag: '🇺🇸',
          direction: 'ltr',
          completeness: 100,
          is_active: true,
          updated_at: new Date().toISOString()
        },
        {
          code: 'fr',
          name: 'French',
          native_name: 'Français',
          flag: '🇫🇷',
          direction: 'ltr',
          completeness: 85,
          is_active: true,
          updated_at: new Date().toISOString()
        }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({ data: mockConfigs, error: null })
      });

      await languageManager.initialize();
      
      const configs = languageManager.getAvailableLanguages();
      expect(configs.some(c => c.code === 'fr')).toBe(true);
    });

    it('should fall back to default configs when database fails', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({ data: null, error: new Error('DB Error') })
      });

      await languageManager.initialize();
      
      const configs = languageManager.getAvailableLanguages();
      expect(configs.length).toBeGreaterThan(0);
      expect(configs.some(c => c.code === 'en')).toBe(true);
    });
  });

  describe('Language Detection', () => {
    it('should detect browser language', async () => {
      Object.defineProperty(navigator, 'languages', {
        value: ['es-ES', 'es'],
        writable: true
      });

      const result = await languageManager.detectUserLanguage();
      
      expect(result.language).toBe('es');
      expect(result.source).toBe('browser');
      expect(result.confidence).toBeGreaterThan(0);
    });

    it('should use fallback when no supported language found', async () => {
      Object.defineProperty(navigator, 'languages', {
        value: ['xx-XX', 'yy'],
        writable: true
      });

      const result = await languageManager.detectUserLanguage();
      
      expect(result.language).toBe('en'); // fallback
      expect(result.source).toBe('fallback');
    });

    it('should detect user preference from database', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } }
      });

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { preferred_language: 'es' },
          error: null
        })
      });

      const result = await languageManager.detectUserLanguage();
      
      expect(result.language).toBe('es');
      expect(result.source).toBe('user_preference');
      expect(result.confidence).toBe(1.0);
    });

    it('should handle geolocation-based detection', async () => {
      Object.defineProperty(navigator, 'languages', {
        value: ['xx-XX'],
        writable: true
      });

      // Mock geolocation
      const mockGetCurrentPosition = vi.fn((success) => {
        success({
          coords: {
            latitude: 40.7128,
            longitude: -74.0060
          }
        });
      });

      Object.defineProperty(navigator, 'geolocation', {
        value: {
          getCurrentPosition: mockGetCurrentPosition
        },
        writable: true
      });

      // Mock fetch for geolocation API
      (global.fetch as any).mockResolvedValue({
        json: () => Promise.resolve({
          countryCode: 'ES'
        })
      });

      const result = await languageManager.detectUserLanguage();
      
      expect(result.language).toBe('es');
      expect(result.source).toBe('geolocation');
    });
  });

  describe('Language Setting', () => {
    it('should set language successfully', async () => {
      await languageManager.setLanguage('es');
      
      expect(languageManager.getCurrentLanguage()).toBe('es');
      expect(document.documentElement.lang).toBe('es');
    });

    it('should fall back to default for unsupported language', async () => {
      await languageManager.setLanguage('xx');
      
      expect(languageManager.getCurrentLanguage()).toBe('en');
    });

    it('should update document direction for RTL languages', async () => {
      await languageManager.setLanguage('ar');
      
      expect(document.documentElement.dir).toBe('rtl');
    });

    it('should save user preference when authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } }
      });

      const updateMock = vi.fn().mockResolvedValue({ error: null });
      mockSupabase.from.mockReturnValue({
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnValue({ 
          then: (cb: any) => cb({ error: null })
        })
      });

      await languageManager.setLanguage('es');
      
      expect(mockSupabase.from).toHaveBeenCalledWith('profiles');
    });
  });

  describe('Translation Loading', () => {
    it('should load translations from database', async () => {
      const mockTranslations = [
        {
          key: 'common.welcome',
          value: 'Bienvenido',
          context: 'greeting',
          interpolations: null,
          pluralization: null
        },
        {
          key: 'common.goodbye',
          value: 'Adiós',
          context: 'farewell',
          interpolations: null,
          pluralization: null
        }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({ data: mockTranslations, error: null })
      });

      await languageManager.loadTranslations('es');
      
      const translations = (languageManager as any).translations.get('es');
      expect(translations.has('common.welcome')).toBe(true);
      expect(translations.get('common.welcome').value).toBe('Bienvenido');
    });

    it('should fall back to file loading when database fails', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({ data: null, error: new Error('DB Error') })
      });

      await languageManager.loadTranslations('es');
      
      const translations = (languageManager as any).translations.get('es');
      expect(translations.has('common.welcome')).toBe(true);
      expect(translations.get('common.welcome').value).toBe('Bienvenido');
    });

    it('should prevent duplicate loading requests', async () => {
      const loadPromise1 = languageManager.loadTranslations('es');
      const loadPromise2 = languageManager.loadTranslations('es');
      
      await Promise.all([loadPromise1, loadPromise2]);
      
      // Should only load once
      expect(mockSupabase.from).toHaveBeenCalledTimes(1);
    });
  });

  describe('Translation Functions', () => {
    beforeEach(async () => {
      await languageManager.setLanguage('en');
      await languageManager.loadTranslations('en');
    });

    it('should translate simple keys', () => {
      const result = languageManager.translate('common.welcome');
      expect(result).toBe('Welcome');
    });

    it('should handle missing translations', () => {
      const result = languageManager.translate('missing.key');
      expect(result).toBe('missing.key'); // Returns key as fallback
    });

    it('should interpolate variables', () => {
      const result = languageManager.translate('plural_test.value', { count: 5 });
      expect(result).toBe('You have 5 item');
    });

    it('should handle pluralization', () => {
      const resultOne = languageManager.translate('plural_test', {}, 1);
      const resultMany = languageManager.translate('plural_test', {}, 5);
      
      expect(resultOne).toBe('You have 1 item');
      expect(resultMany).toBe('You have 5 items');
    });

    it('should use cache for repeated translations', () => {
      const result1 = languageManager.translate('common.welcome');
      const result2 = languageManager.translate('common.welcome');
      
      expect(result1).toBe(result2);
      
      // Check cache
      const cache = (languageManager as any).translationCache;
      expect(cache.size).toBeGreaterThan(0);
    });

    it('should fall back to fallback language', async () => {
      await languageManager.setLanguage('es');
      await languageManager.loadTranslations('es');
      
      // Try to translate key that doesn't exist in Spanish
      const result = languageManager.translate('nonexistent.key');
      
      // Should fall back to English, then to key
      expect(result).toBe('nonexistent.key');
    });
  });

  describe('Translation Management', () => {
    it('should update translations', async () => {
      const upsertMock = vi.fn().mockResolvedValue({ error: null });
      mockSupabase.from.mockReturnValue({
        upsert: upsertMock
      });

      await languageManager.updateTranslation('es', 'test.key', 'Test Value', 'Test context');
      
      expect(upsertMock).toHaveBeenCalledWith({
        language_code: 'es',
        key: 'test.key',
        value: 'Test Value',
        context: 'Test context',
        updated_at: expect.any(String)
      });
    });

    it('should get translation statistics', async () => {
      await languageManager.loadTranslations('en');
      await languageManager.loadTranslations('es');
      
      const stats = await languageManager.getTranslationStats('es');
      
      expect(stats.totalKeys).toBeGreaterThan(0);
      expect(stats.translatedKeys).toBeGreaterThan(0);
      expect(stats.completeness).toBeGreaterThanOrEqual(0);
      expect(stats.completeness).toBeLessThanOrEqual(100);
    });
  });

  describe('Utility Methods', () => {
    it('should identify RTL languages correctly', () => {
      expect(languageManager.isRTL('ar')).toBe(true);
      expect(languageManager.isRTL('he')).toBe(true);
      expect(languageManager.isRTL('en')).toBe(false);
      expect(languageManager.isRTL('es')).toBe(false);
    });

    it('should get current language config', async () => {
      await languageManager.setLanguage('en');
      
      const config = languageManager.getCurrentLanguageConfig();
      expect(config).toBeDefined();
      expect(config?.code).toBe('en');
      expect(config?.name).toBe('English');
    });

    it('should normalize language codes', () => {
      const normalizeMethod = (languageManager as any).normalizeLanguageCode;
      
      expect(normalizeMethod('en-US')).toBe('en');
      expect(normalizeMethod('ES-ES')).toBe('es');
      expect(normalizeMethod('zh-CN')).toBe('zh');
    });
  });

  describe('Observer Pattern', () => {
    it('should notify observers on language change', async () => {
      const mockCallback = vi.fn();
      const unsubscribe = languageManager.subscribe(mockCallback);
      
      await languageManager.setLanguage('es');
      
      expect(mockCallback).toHaveBeenCalledWith('es');
      
      unsubscribe();
      
      await languageManager.setLanguage('en');
      expect(mockCallback).toHaveBeenCalledTimes(1); // Should not be called after unsubscribe
    });

    it('should handle observer errors gracefully', async () => {
      const errorCallback = vi.fn(() => {
        throw new Error('Observer error');
      });
      
      languageManager.subscribe(errorCallback);
      
      // Should not throw
      await expect(languageManager.setLanguage('es')).resolves.toBeUndefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockRejectedValue(new Error('Database connection failed'))
      });

      // Should not throw
      await expect(languageManager.loadTranslations('es')).resolves.toBeUndefined();
    });

    it('should handle file loading errors gracefully', async () => {
      // Mock import to fail
      vi.doMock('@/i18n/locales/xx.json', () => {
        throw new Error('File not found');
      });

      await languageManager.loadTranslations('xx');
      
      // Should create empty translation map
      const translations = (languageManager as any).translations.get('xx');
      expect(translations).toBeDefined();
    });
  });

  describe('Device Fingerprinting', () => {
    it('should generate device fingerprint', async () => {
      // Mock canvas
      const mockCanvas = {
        getContext: vi.fn(() => ({
          fillText: vi.fn()
        })),
        toDataURL: vi.fn(() => 'mock-canvas-data')
      };

      Object.defineProperty(document, 'createElement', {
        value: vi.fn(() => mockCanvas),
        writable: true
      });

      const fingerprint = await (languageManager as any).generateDeviceFingerprint();
      
      expect(fingerprint).toBeDefined();
      expect(typeof fingerprint).toBe('string');
      expect(fingerprint.length).toBe(32);
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources on destroy', () => {
      const clearMock = vi.fn();
      (languageManager as any).translationCache.clear = clearMock;
      
      languageManager.destroy();
      
      expect(clearMock).toHaveBeenCalled();
    });
  });
});