// Chat Flow Integration Tests
// End-to-end tests for complete chat workflows

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ChatService } from '../../chatService';
import { AiService } from '../../aiService';

// Mock dependencies
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn().mockResolvedValue({ data: { id: 'chat123' }, error: null }),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
      update: vi.fn().mockResolvedValue({ data: null, error: null })
    })),
    auth: {
      getUser: vi.fn().mockResolvedValue({ data: { user: { id: 'user123' } } })
    }
  }
}));

vi.mock('../../aiService', () => ({
  AiService: vi.fn().mockImplementation(() => ({
    generateResponse: vi.fn().mockResolvedValue({
      response: 'AI response',
      confidence: 0.95,
      suggestedActions: [],
      metadata: {}
    }),
    extractTopics: vi.fn().mockResolvedValue(['topic1', 'topic2']),
    analyzeSentiment: vi.fn().mockResolvedValue({
      sentiment: 'positive',
      confidence: 0.8,
      emotions: ['joy']
    })
  }))
}));

describe('Chat Flow Integration', () => {
  let chatService: ChatService;
  let aiService: any;

  beforeEach(() => {
    chatService = new ChatService();
    aiService = new AiService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete Chat Session', () => {
    it('should handle complete visitor chat flow', async () => {
      // 1. Visitor arrives and chat session is created
      const createResult = await chatService.createChatSession(
        'user123',
        'visitor456',
        { 
          name: 'John Doe', 
          email: '<EMAIL>',
          message: 'Hello, I need help with my order'
        }
      );

      expect(createResult.success).toBe(true);
      expect(createResult.data).toHaveProperty('id');

      const chatId = createResult.data!.id;

      // 2. Visitor sends initial message
      const messageResult = await chatService.sendMessage(
        chatId,
        'visitor456',
        'visitor',
        'Hello, I need help with my order #12345'
      );

      expect(messageResult.success).toBe(true);
      expect(messageResult.data).toHaveProperty('id');

      // 3. AI generates automatic response
      const aiResponse = await aiService.generateResponse(
        'Hello, I need help with my order #12345',
        {
          chatHistory: [],
          userContext: { name: 'John Doe', email: '<EMAIL>' }
        }
      );

      expect(aiResponse.response).toBe('AI response');
      expect(aiResponse.confidence).toBeGreaterThan(0.9);

      // 4. AI response is sent to visitor
      const aiMessageResult = await chatService.sendMessage(
        chatId,
        'ai',
        'ai',
        aiResponse.response,
        { 
          confidence: aiResponse.confidence,
          type: 'auto_response'
        }
      );

      expect(aiMessageResult.success).toBe(true);

      // 5. Visitor continues conversation
      const followUpResult = await chatService.sendMessage(
        chatId,
        'visitor456',
        'visitor',
        'Yes, my order seems to be delayed'
      );

      expect(followUpResult.success).toBe(true);

      // 6. Human agent takes over (simulated by updating chat status)
      // This would typically involve routing logic
      expect(chatService).toBeDefined();
    });

    it('should handle escalation to human agent', async () => {
      const chatId = 'chat123';

      // 1. Visitor asks complex question
      const complexQuestion = 'I need to modify my subscription and change billing address';
      
      const messageResult = await chatService.sendMessage(
        chatId,
        'visitor456',
        'visitor',
        complexQuestion
      );

      expect(messageResult.success).toBe(true);

      // 2. AI detects need for human intervention
      const aiResponse = await aiService.generateResponse(complexQuestion, {
        chatHistory: [],
        userContext: {}
      });

      // In real implementation, AI would detect complexity and suggest escalation
      expect(aiResponse).toBeDefined();

      // 3. System routes to human agent
      // This would involve updating chat routing and notifying agents
      const escalationResult = await chatService.sendMessage(
        chatId,
        'system',
        'system',
        'Connecting you with a human agent...',
        { type: 'escalation_notice' }
      );

      expect(escalationResult.success).toBe(true);
    });
  });

  describe('Multi-language Chat Support', () => {
    it('should handle chat in different languages', async () => {
      // 1. Spanish-speaking visitor starts chat
      const createResult = await chatService.createChatSession(
        'user123',
        'visitor789',
        {
          name: 'María García',
          email: '<EMAIL>',
          message: 'Hola, necesito ayuda con mi pedido',
          language: 'es'
        }
      );

      expect(createResult.success).toBe(true);

      const chatId = createResult.data!.id;

      // 2. Send message in Spanish
      const messageResult = await chatService.sendMessage(
        chatId,
        'visitor789',
        'visitor',
        'Mi pedido no ha llegado aún'
      );

      expect(messageResult.success).toBe(true);

      // 3. AI should respond in Spanish (mocked)
      const aiResponse = await aiService.generateResponse(
        'Mi pedido no ha llegado aún',
        {
          chatHistory: [],
          userContext: { language: 'es' }
        }
      );

      expect(aiResponse.response).toBeDefined();
    });
  });

  describe('Chat Analytics and Insights', () => {
    it('should track and analyze chat metrics', async () => {
      const chatId = 'chat123';
      
      // Simulate a complete chat session with multiple messages
      const messages = [
        'Hello, I need help',
        'My order is delayed',
        'When will it arrive?',
        'Thank you for your help'
      ];

      for (const message of messages) {
        await chatService.sendMessage(
          chatId,
          'visitor456',
          'visitor',
          message
        );

        // AI responds to each message
        const aiResponse = await aiService.generateResponse(message, {
          chatHistory: [],
          userContext: {}
        });

        await chatService.sendMessage(
          chatId,
          'ai',
          'ai',
          aiResponse.response,
          { confidence: aiResponse.confidence }
        );
      }

      // Extract topics from the conversation
      const conversationText = messages.join(' ');
      const topics = await aiService.extractTopics(conversationText);
      
      expect(topics).toContain('topic1');
      expect(topics).toContain('topic2');

      // Analyze sentiment
      const sentiment = await aiService.analyzeSentiment(conversationText);
      
      expect(sentiment.sentiment).toBe('positive');
      expect(sentiment.confidence).toBeGreaterThan(0.5);
      expect(sentiment.emotions).toContain('joy');
    });

    it('should generate chat analytics report', async () => {
      // This would typically aggregate data from multiple chats
      const analyticsData = await chatService.getChatAnalytics('user123', {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31')
      });

      expect(analyticsData.success).toBe(true);
      expect(analyticsData.data).toHaveProperty('totalChats');
      expect(analyticsData.data).toHaveProperty('averageResponseTime');
      expect(analyticsData.data).toHaveProperty('satisfactionScore');
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle AI service failures gracefully', async () => {
      // Mock AI service to fail
      aiService.generateResponse.mockRejectedValue(new Error('AI service unavailable'));

      const chatId = 'chat123';
      
      // Visitor sends message
      const messageResult = await chatService.sendMessage(
        chatId,
        'visitor456',
        'visitor',
        'Hello, I need help'
      );

      expect(messageResult.success).toBe(true);

      // Try to get AI response (should fail gracefully)
      try {
        await aiService.generateResponse('Hello, I need help', {});
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }

      // System should fall back to default response or queue for human agent
      const fallbackResult = await chatService.sendMessage(
        chatId,
        'system',
        'system',
        'I apologize, but our AI assistant is temporarily unavailable. A human agent will assist you shortly.',
        { type: 'fallback_response' }
      );

      expect(fallbackResult.success).toBe(true);
    });

    it('should handle database connection issues', async () => {
      // Mock database to fail
      const { supabase } = await import('@/integrations/supabase/client');
      (supabase.from as any).mockReturnValue({
        insert: vi.fn().mockRejectedValue(new Error('Database connection failed'))
      });

      // Try to create chat session
      const result = await chatService.createChatSession(
        'user123',
        'visitor456',
        { name: 'John Doe', email: '<EMAIL>' }
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Real-time Features', () => {
    it('should handle typing indicators', async () => {
      const chatId = 'chat123';
      
      // Simulate typing indicator
      const typingResult = await chatService.setTypingStatus(
        chatId,
        'visitor456',
        true
      );

      expect(typingResult.success).toBe(true);

      // Stop typing
      const stopTypingResult = await chatService.setTypingStatus(
        chatId,
        'visitor456',
        false
      );

      expect(stopTypingResult.success).toBe(true);
    });

    it('should handle presence tracking', async () => {
      const chatId = 'chat123';
      
      // Visitor goes online
      const onlineResult = await chatService.updatePresence(
        chatId,
        'visitor456',
        'online'
      );

      expect(onlineResult.success).toBe(true);

      // Visitor goes offline
      const offlineResult = await chatService.updatePresence(
        chatId,
        'visitor456',
        'offline'
      );

      expect(offlineResult.success).toBe(true);
    });
  });

  describe('Chat Persistence and History', () => {
    it('should persist chat history correctly', async () => {
      const chatId = 'chat123';
      
      // Send multiple messages
      const messages = [
        { sender: 'visitor456', content: 'Hello' },
        { sender: 'ai', content: 'Hi there! How can I help?' },
        { sender: 'visitor456', content: 'I need help with my account' },
        { sender: 'ai', content: 'I can help you with that.' }
      ];

      for (const msg of messages) {
        await chatService.sendMessage(
          chatId,
          msg.sender,
          msg.sender === 'visitor456' ? 'visitor' : 'ai',
          msg.content
        );
      }

      // Retrieve chat history
      const historyResult = await chatService.getChatHistory(chatId);
      
      expect(historyResult.success).toBe(true);
      expect(historyResult.data).toHaveLength(messages.length);
    });

    it('should handle chat archival', async () => {
      const chatId = 'chat123';
      
      // Archive chat
      const archiveResult = await chatService.archiveChat(chatId);
      
      expect(archiveResult.success).toBe(true);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle high message volume', async () => {
      const chatId = 'chat123';
      const messageCount = 100;
      
      const startTime = Date.now();
      
      // Send many messages rapidly
      const promises = Array.from({ length: messageCount }, (_, i) => 
        chatService.sendMessage(
          chatId,
          'visitor456',
          'visitor',
          `Message ${i + 1}`
        )
      );

      const results = await Promise.all(promises);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // All messages should succeed
      expect(results.every(r => r.success)).toBe(true);
      
      // Performance check (should complete within reasonable time)
      expect(duration).toBeLessThan(10000); // 10 seconds
    });

    it('should handle concurrent chat sessions', async () => {
      const sessionCount = 10;
      
      // Create multiple chat sessions concurrently
      const promises = Array.from({ length: sessionCount }, (_, i) =>
        chatService.createChatSession(
          'user123',
          `visitor${i}`,
          { 
            name: `Visitor ${i}`, 
            email: `visitor${i}@example.com`,
            message: `Hello from visitor ${i}`
          }
        )
      );

      const results = await Promise.all(promises);
      
      // All sessions should be created successfully
      expect(results.every(r => r.success)).toBe(true);
      
      // Each session should have unique ID
      const ids = results.map(r => r.data?.id);
      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(sessionCount);
    });
  });
});