// Error Handling Tests
// Comprehensive tests for error handling system

import { describe, it, expect, beforeEach, afterEach, vi, MockedFunction } from 'vitest';
import {
  <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  AppError,
  NetworkError,
  DatabaseError,
  ValidationError,
  SecurityError,
  ErrorSeverity,
  ErrorCategory,
  handleError,
  createError,
  addBreadcrumb,
  trackPerformance,
  setErrorContext
} from '../errorHandling';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn().mockResolvedValue({ data: null, error: null })
    })),
    auth: {
      refreshSession: vi.fn().mockResolvedValue({ error: null })
    }
  }
}));

// Mock console methods
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

describe('AppError', () => {
  beforeEach(() => {
    console.error = vi.fn();
    console.warn = vi.fn();
  });

  afterEach(() => {
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
  });

  it('should create a basic app error', () => {
    const error = new AppError('Test error');
    
    expect(error.message).toBe('Test error');
    expect(error.name).toBe('AppError');
    expect(error.severity).toBe(ErrorSeverity.MEDIUM);
    expect(error.category).toBe(ErrorCategory.BUSINESS_LOGIC);
    expect(error.recoverable).toBe(false);
    expect(error.retryCount).toBe(0);
    expect(error.maxRetries).toBe(3);
    expect(error.id).toMatch(/^err_\d+_[a-z0-9]+$/);
  });

  it('should create an error with custom properties', () => {
    const context = {
      userId: 'user123',
      component: 'test-component',
      action: 'test-action'
    };

    const error = new AppError(
      'Custom error',
      ErrorSeverity.HIGH,
      ErrorCategory.NETWORK,
      context,
      true
    );

    expect(error.message).toBe('Custom error');
    expect(error.severity).toBe(ErrorSeverity.HIGH);
    expect(error.category).toBe(ErrorCategory.NETWORK);
    expect(error.recoverable).toBe(true);
    expect(error.context.userId).toBe('user123');
    expect(error.context.component).toBe('test-component');
    expect(error.context.action).toBe('test-action');
  });

  it('should serialize to JSON correctly', () => {
    const error = new AppError('Test error');
    const json = error.toJSON();

    expect(json).toHaveProperty('id');
    expect(json).toHaveProperty('name', 'AppError');
    expect(json).toHaveProperty('message', 'Test error');
    expect(json).toHaveProperty('severity', ErrorSeverity.MEDIUM);
    expect(json).toHaveProperty('category', ErrorCategory.BUSINESS_LOGIC);
    expect(json).toHaveProperty('timestamp');
    expect(json).toHaveProperty('context');
  });
});

describe('Specialized Error Classes', () => {
  it('should create NetworkError with correct properties', () => {
    const error = new NetworkError('Network failed', { action: 'fetch' }, 500);
    
    expect(error.name).toBe('NetworkError');
    expect(error.category).toBe(ErrorCategory.NETWORK);
    expect(error.severity).toBe(ErrorSeverity.HIGH); // 500 status code
    expect(error.recoverable).toBe(true);
    expect(error.statusCode).toBe(500);
  });

  it('should create NetworkError with medium severity for 4xx errors', () => {
    const error = new NetworkError('Client error', {}, 404);
    
    expect(error.severity).toBe(ErrorSeverity.MEDIUM);
    expect(error.statusCode).toBe(404);
  });

  it('should create DatabaseError with correct properties', () => {
    const error = new DatabaseError('DB connection failed');
    
    expect(error.name).toBe('DatabaseError');
    expect(error.category).toBe(ErrorCategory.DATABASE);
    expect(error.severity).toBe(ErrorSeverity.HIGH);
    expect(error.recoverable).toBe(false);
  });

  it('should create ValidationError with correct properties', () => {
    const error = new ValidationError('Invalid input', { action: 'validate' }, 'email');
    
    expect(error.name).toBe('ValidationError');
    expect(error.category).toBe(ErrorCategory.VALIDATION);
    expect(error.severity).toBe(ErrorSeverity.LOW);
    expect(error.recoverable).toBe(false);
    expect(error.field).toBe('email');
  });

  it('should create SecurityError with correct properties', () => {
    const error = new SecurityError('Unauthorized access');
    
    expect(error.name).toBe('SecurityError');
    expect(error.category).toBe(ErrorCategory.SECURITY);
    expect(error.severity).toBe(ErrorSeverity.CRITICAL);
    expect(error.recoverable).toBe(false);
  });
});

describe('ErrorHandler', () => {
  let errorHandler: ErrorHandler;
  let mockLocalStorage: { [key: string]: string };
  let mockSessionStorage: { [key: string]: string };

  beforeEach(() => {
    errorHandler = new ErrorHandler();
    
    // Mock localStorage
    mockLocalStorage = {};
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn((key: string) => mockLocalStorage[key] || null),
        setItem: vi.fn((key: string, value: string) => {
          mockLocalStorage[key] = value;
        }),
        removeItem: vi.fn((key: string) => {
          delete mockLocalStorage[key];
        })
      }
    });

    // Mock sessionStorage
    mockSessionStorage = {};
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: vi.fn((key: string) => mockSessionStorage[key] || null),
        setItem: vi.fn((key: string, value: string) => {
          mockSessionStorage[key] = value;
        })
      }
    });

    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      value: true,
      writable: true
    });

    console.error = vi.fn();
    console.warn = vi.fn();
  });

  afterEach(() => {
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
  });

  describe('Breadcrumb Management', () => {
    it('should add breadcrumbs correctly', () => {
      errorHandler.addBreadcrumb('test', 'Test breadcrumb', 'info', { data: 'test' });
      
      const breadcrumbs = errorHandler.getBreadcrumbs();
      expect(breadcrumbs).toHaveLength(1);
      expect(breadcrumbs[0].category).toBe('test');
      expect(breadcrumbs[0].message).toBe('Test breadcrumb');
      expect(breadcrumbs[0].level).toBe('info');
      expect(breadcrumbs[0].data).toEqual({ data: 'test' });
    });

    it('should limit breadcrumbs to max count', () => {
      // Add more than max breadcrumbs
      for (let i = 0; i < 55; i++) {
        errorHandler.addBreadcrumb('test', `Breadcrumb ${i}`);
      }

      const breadcrumbs = errorHandler.getBreadcrumbs();
      expect(breadcrumbs).toHaveLength(50); // Max breadcrumbs
    });

    it('should clear breadcrumbs', () => {
      errorHandler.addBreadcrumb('test', 'Test breadcrumb');
      expect(errorHandler.getBreadcrumbs()).toHaveLength(1);
      
      errorHandler.clearBreadcrumbs();
      expect(errorHandler.getBreadcrumbs()).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle AppError correctly', async () => {
      const error = new AppError('Test error', ErrorSeverity.HIGH);
      
      await errorHandler.handleError(error);
      
      const breadcrumbs = errorHandler.getBreadcrumbs();
      expect(breadcrumbs.some(b => b.level === 'error')).toBe(true);
    });

    it('should convert regular Error to AppError', async () => {
      const regularError = new Error('Regular error');
      
      await errorHandler.handleError(regularError);
      
      const breadcrumbs = errorHandler.getBreadcrumbs();
      expect(breadcrumbs.some(b => 
        b.level === 'error' && b.message === 'Regular error'
      )).toBe(true);
    });

    it('should store errors locally when database fails', async () => {
      // Mock supabase to fail
      const { supabase } = await import('@/integrations/supabase/client');
      (supabase.from as any).mockReturnValue({
        insert: vi.fn().mockRejectedValue(new Error('DB Error'))
      });

      const error = new AppError('Test error');
      await errorHandler.handleError(error);

      // Check localStorage
      const stored = mockLocalStorage['stored_errors'];
      expect(stored).toBeDefined();
      const errors = JSON.parse(stored);
      expect(errors).toHaveLength(1);
      expect(errors[0].message).toBe('Test error');
    });
  });

  describe('Recovery Strategies', () => {
    it('should attempt network recovery for NetworkError', async () => {
      const networkError = new NetworkError('Network failed', {}, 500);
      networkError.recoverable = true;
      
      const recoverySpy = vi.spyOn(errorHandler as any, 'attemptRecovery');
      
      await errorHandler.handleError(networkError);
      
      expect(recoverySpy).toHaveBeenCalled();
    });

    it('should not attempt recovery for non-recoverable errors', async () => {
      const validationError = new ValidationError('Invalid input');
      
      const recoverySpy = vi.spyOn(errorHandler as any, 'attemptRecovery');
      
      await errorHandler.handleError(validationError);
      
      expect(recoverySpy).not.toHaveBeenCalled();
    });

    it('should add custom recovery strategy', () => {
      const customStrategy = {
        name: 'custom_strategy',
        description: 'Custom recovery',
        canRecover: () => true,
        recover: async () => ({ success: true }),
        maxAttempts: 1,
        backoffMs: 0
      };

      errorHandler.addRecoveryStrategy(customStrategy);
      
      // Verify strategy was added
      expect((errorHandler as any).recoveryStrategies.has('custom_strategy')).toBe(true);
    });

    it('should remove recovery strategy', () => {
      const customStrategy = {
        name: 'custom_strategy',
        description: 'Custom recovery',
        canRecover: () => true,
        recover: async () => ({ success: true }),
        maxAttempts: 1,
        backoffMs: 0
      };

      errorHandler.addRecoveryStrategy(customStrategy);
      errorHandler.removeRecoveryStrategy('custom_strategy');
      
      expect((errorHandler as any).recoveryStrategies.has('custom_strategy')).toBe(false);
    });
  });

  describe('Error Reports', () => {
    it('should generate error reports', async () => {
      const error1 = new AppError('Test error 1');
      const error2 = new AppError('Test error 1'); // Same message
      const error3 = new AppError('Test error 2'); // Different message

      await errorHandler.handleError(error1);
      await errorHandler.handleError(error2);
      await errorHandler.handleError(error3);

      const reports = errorHandler.getErrorReports();
      expect(reports).toHaveLength(2); // Two unique error types
      
      const report1 = reports.find(r => r.message === 'Test error 1');
      expect(report1?.count).toBe(2); // Two occurrences
      
      const report2 = reports.find(r => r.message === 'Test error 2');
      expect(report2?.count).toBe(1); // One occurrence
    });

    it('should resolve errors', async () => {
      const error = new AppError('Test error');
      await errorHandler.handleError(error);

      const reports = errorHandler.getErrorReports();
      const report = reports[0];
      
      expect(report.resolved).toBe(false);
      
      errorHandler.resolveError(report.fingerprint, 'Fixed by updating code');
      
      expect(report.resolved).toBe(true);
      expect(report.resolution).toBe('Fixed by updating code');
      expect(report.resolvedAt).toBeInstanceOf(Date);
    });
  });

  describe('Context Management', () => {
    it('should set and use error context', () => {
      const context = {
        userId: 'user123',
        component: 'test-component'
      };

      errorHandler.setContext(context);
      
      const error = errorHandler.createError('Test error');
      
      expect(error.context.userId).toBe('user123');
      expect(error.context.component).toBe('test-component');
    });
  });

  describe('Performance Monitoring', () => {
    it('should track slow operations', async () => {
      const handleErrorSpy = vi.spyOn(errorHandler, 'handleError');
      
      errorHandler.trackPerformance('slow_operation', 6000); // 6 seconds
      
      expect(handleErrorSpy).toHaveBeenCalled();
      const call = handleErrorSpy.mock.calls[0][0] as AppError;
      expect(call.category).toBe(ErrorCategory.PERFORMANCE);
      expect(call.message).toContain('slow_operation');
      expect(call.context.metadata?.duration).toBe(6000);
    });

    it('should not track fast operations', () => {
      const handleErrorSpy = vi.spyOn(errorHandler, 'handleError');
      
      errorHandler.trackPerformance('fast_operation', 1000); // 1 second
      
      expect(handleErrorSpy).not.toHaveBeenCalled();
    });
  });

  describe('Cache Management', () => {
    it('should cache and retrieve data', async () => {
      const testData = { test: 'data' };
      
      errorHandler.setCachedData('test_key', testData);
      
      const retrieved = await (errorHandler as any).getCachedData('test_key');
      expect(retrieved).toEqual(testData);
    });

    it('should return null for expired cache', async () => {
      const testData = { test: 'data' };
      
      // Mock timestamp to be old
      const oldTimestamp = Date.now() - 7200000; // 2 hours ago
      mockLocalStorage['cache_test_key'] = JSON.stringify({
        value: testData,
        timestamp: oldTimestamp
      });
      
      const retrieved = await (errorHandler as any).getCachedData('test_key');
      expect(retrieved).toBeNull();
    });
  });

  describe('Error Notifications', () => {
    it('should notify error listeners', async () => {
      const mockCallback = vi.fn();
      
      const unsubscribe = errorHandler.onError(mockCallback);
      
      const error = new AppError('Test error');
      await errorHandler.handleError(error);
      
      expect(mockCallback).toHaveBeenCalledWith(error);
      
      unsubscribe();
      
      // After unsubscribe, callback should not be called
      await errorHandler.handleError(new AppError('Another error'));
      expect(mockCallback).toHaveBeenCalledTimes(1);
    });
  });
});

describe('Convenience Functions', () => {
  beforeEach(() => {
    console.error = vi.fn();
    console.warn = vi.fn();
  });

  afterEach(() => {
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
  });

  it('should handle error using convenience function', async () => {
    const error = new Error('Test error');
    
    await expect(handleError(error)).resolves.toBeUndefined();
  });

  it('should create error using convenience function', () => {
    const error = createError('Test error', ErrorSeverity.HIGH, ErrorCategory.NETWORK);
    
    expect(error).toBeInstanceOf(AppError);
    expect(error.message).toBe('Test error');
    expect(error.severity).toBe(ErrorSeverity.HIGH);
    expect(error.category).toBe(ErrorCategory.NETWORK);
  });

  it('should add breadcrumb using convenience function', () => {
    addBreadcrumb('test', 'Test breadcrumb', 'warning');
    // This function delegates to the singleton, so we can't easily test it
    // without accessing the singleton instance
  });

  it('should track performance using convenience function', () => {
    trackPerformance('test_operation', 6000);
    // This function delegates to the singleton
  });

  it('should set error context using convenience function', () => {
    setErrorContext({ userId: 'user123' });
    // This function delegates to the singleton
  });
});

describe('Global Error Handling', () => {
  let errorHandler: ErrorHandler;

  beforeEach(() => {
    errorHandler = new ErrorHandler();
    console.error = vi.fn();
    console.warn = vi.fn();
  });

  afterEach(() => {
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
  });

  it('should handle unhandled promise rejections', () => {
    const handleErrorSpy = vi.spyOn(errorHandler, 'handleError');
    
    const event = new Event('unhandledrejection') as any;
    event.reason = 'Test rejection';
    
    window.dispatchEvent(event);
    
    expect(handleErrorSpy).toHaveBeenCalled();
  });

  it('should handle uncaught errors', () => {
    const handleErrorSpy = vi.spyOn(errorHandler, 'handleError');
    
    const event = new ErrorEvent('error', {
      message: 'Test uncaught error',
      filename: 'test.js',
      lineno: 1,
      colno: 1
    });
    
    window.dispatchEvent(event);
    
    expect(handleErrorSpy).toHaveBeenCalled();
  });
});