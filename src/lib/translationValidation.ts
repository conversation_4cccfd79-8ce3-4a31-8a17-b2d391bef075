// Translation Validation
// Validation system for translations, language content, and internationalization quality

interface TranslationValidationRule {
  name: string;
  description: string;
  severity: 'error' | 'warning' | 'info';
  validate: (value: string, context?: TranslationContext) => ValidationResult;
}

interface TranslationContext {
  key: string;
  languageCode: string;
  interpolations?: string[];
  maxLength?: number;
  allowHtml?: boolean;
  isPlural?: boolean;
  originalValue?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

interface LanguageQualityReport {
  languageCode: string;
  totalTranslations: number;
  validTranslations: number;
  errorCount: number;
  warningCount: number;
  completeness: number;
  qualityScore: number;
  issues: TranslationIssue[];
  recommendations: string[];
}

interface TranslationIssue {
  key: string;
  type: 'error' | 'warning' | 'info';
  rule: string;
  message: string;
  originalValue?: string;
  suggestedValue?: string;
  context?: TranslationContext;
}

export class TranslationValidator {
  private rules: Map<string, TranslationValidationRule> = new Map();
  private languageSpecificRules: Map<string, TranslationValidationRule[]> = new Map();

  constructor() {
    this.initializeDefaultRules();
    this.initializeLanguageSpecificRules();
  }

  private initializeDefaultRules(): void {
    // Basic validation rules
    this.addRule({
      name: 'not_empty',
      description: 'Translation should not be empty',
      severity: 'error',
      validate: (value: string): ValidationResult => {
        const isValid = value.trim().length > 0;
        return {
          isValid,
          errors: isValid ? [] : ['Translation cannot be empty'],
          warnings: [],
          suggestions: isValid ? [] : ['Provide a translation for this key']
        };
      }
    });

    this.addRule({
      name: 'length_reasonable',
      description: 'Translation length should be reasonable',
      severity: 'warning',
      validate: (value: string, context?: TranslationContext): ValidationResult => {
        const errors: string[] = [];
        const warnings: string[] = [];
        const suggestions: string[] = [];

        if (value.length > 1000) {
          warnings.push('Translation is very long (>1000 characters)');
          suggestions.push('Consider breaking this into multiple shorter translations');
        }

        if (context?.maxLength && value.length > context.maxLength) {
          errors.push(`Translation exceeds maximum length of ${context.maxLength} characters`);
        }

        // Check for suspiciously short translations for complex keys
        if (context?.key && context.key.includes('.') && value.length < 3) {
          warnings.push('Translation seems too short for this key');
        }

        return {
          isValid: errors.length === 0,
          errors,
          warnings,
          suggestions
        };
      }
    });

    this.addRule({
      name: 'interpolation_consistency',
      description: 'Interpolation variables should be preserved',
      severity: 'error',
      validate: (value: string, context?: TranslationContext): ValidationResult => {
        const errors: string[] = [];
        const warnings: string[] = [];
        const suggestions: string[] = [];

        if (context?.interpolations) {
          // Find interpolation variables in the translation
          const interpolationRegex = /\{\{(\w+)\}\}/g;
          const foundInterpolations: string[] = [];
          let match;

          while ((match = interpolationRegex.exec(value)) !== null) {
            foundInterpolations.push(match[1]);
          }

          // Check for missing interpolations
          const missingInterpolations = context.interpolations.filter(
            interp => !foundInterpolations.includes(interp)
          );

          // Check for extra interpolations
          const extraInterpolations = foundInterpolations.filter(
            interp => !context.interpolations!.includes(interp)
          );

          if (missingInterpolations.length > 0) {
            errors.push(`Missing interpolation variables: ${missingInterpolations.join(', ')}`);
            suggestions.push(`Include these variables: ${missingInterpolations.map(v => `{{${v}}}`).join(', ')}`);
          }

          if (extraInterpolations.length > 0) {
            warnings.push(`Unknown interpolation variables: ${extraInterpolations.join(', ')}`);
            suggestions.push('Remove or verify these unknown variables');
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
          warnings,
          suggestions
        };
      }
    });

    this.addRule({
      name: 'html_safety',
      description: 'Check for potentially unsafe HTML content',
      severity: 'warning',
      validate: (value: string, context?: TranslationContext): ValidationResult => {
        const errors: string[] = [];
        const warnings: string[] = [];
        const suggestions: string[] = [];

        // Check for HTML tags
        const htmlTagRegex = /<[^>]+>/g;
        const htmlTags = value.match(htmlTagRegex);

        if (htmlTags && !context?.allowHtml) {
          warnings.push('Translation contains HTML tags');
          suggestions.push('Remove HTML tags or mark this translation as HTML-safe');
        }

        // Check for potentially dangerous content
        const dangerousPatterns = [
          /<script/i,
          /javascript:/i,
          /on\w+=/i,
          /<iframe/i,
          /<object/i,
          /<embed/i
        ];

        for (const pattern of dangerousPatterns) {
          if (pattern.test(value)) {
            errors.push('Translation contains potentially dangerous HTML content');
            suggestions.push('Remove scripts and dangerous HTML attributes');
            break;
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
          warnings,
          suggestions
        };
      }
    });

    this.addRule({
      name: 'special_characters',
      description: 'Check for problematic special characters',
      severity: 'info',
      validate: (value: string): ValidationResult => {
        const warnings: string[] = [];
        const suggestions: string[] = [];

        // Check for multiple consecutive spaces
        if (/\s{2,}/.test(value)) {
          warnings.push('Translation contains multiple consecutive spaces');
          suggestions.push('Use single spaces between words');
        }

        // Check for leading/trailing whitespace
        if (value !== value.trim()) {
          warnings.push('Translation has leading or trailing whitespace');
          suggestions.push('Remove unnecessary whitespace at the beginning or end');
        }

        // Check for smart quotes that might cause issues
        if (/[""'']/.test(value)) {
          warnings.push('Translation contains smart quotes');
          suggestions.push('Consider using straight quotes for consistency');
        }

        return {
          isValid: true,
          errors: [],
          warnings,
          suggestions
        };
      }
    });

    this.addRule({
      name: 'consistency_with_original',
      description: 'Check consistency with original language',
      severity: 'info',
      validate: (value: string, context?: TranslationContext): ValidationResult => {
        const warnings: string[] = [];
        const suggestions: string[] = [];

        if (context?.originalValue) {
          // Check for significant length differences
          const lengthRatio = value.length / context.originalValue.length;
          if (lengthRatio > 2.5) {
            warnings.push('Translation is significantly longer than original');
            suggestions.push('Verify if this length difference is necessary');
          } else if (lengthRatio < 0.4) {
            warnings.push('Translation is significantly shorter than original');
            suggestions.push('Ensure no important information is lost');
          }

          // Check for preserved formatting
          const originalHasNumbers = /\d/.test(context.originalValue);
          const translationHasNumbers = /\d/.test(value);
          
          if (originalHasNumbers && !translationHasNumbers) {
            warnings.push('Original contains numbers but translation does not');
          }
        }

        return {
          isValid: true,
          errors: [],
          warnings,
          suggestions
        };
      }
    });

    this.addRule({
      name: 'plural_forms',
      description: 'Validate plural form translations',
      severity: 'error',
      validate: (value: string, context?: TranslationContext): ValidationResult => {
        const errors: string[] = [];
        const warnings: string[] = [];

        if (context?.isPlural) {
          // For plural forms, ensure the translation makes sense
          if (!value.includes('{{count}}') && /\d/.test(context.originalValue || '')) {
            warnings.push('Plural translation might need {{count}} variable');
          }

          // Check if plural form seems appropriate
          if (value === context.originalValue) {
            warnings.push('Plural translation is identical to original');
            warnings.push('Verify if this language has distinct plural forms');
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
          warnings,
          suggestions: []
        };
      }
    });
  }

  private initializeLanguageSpecificRules(): void {
    // Arabic-specific rules
    this.addLanguageSpecificRule('ar', {
      name: 'arabic_text_direction',
      description: 'Check Arabic text direction and formatting',
      severity: 'warning',
      validate: (value: string): ValidationResult => {
        const warnings: string[] = [];
        const suggestions: string[] = [];

        // Check for mixed LTR/RTL content
        const hasArabic = /[\u0600-\u06FF]/.test(value);
        const hasLatin = /[a-zA-Z]/.test(value);

        if (hasArabic && hasLatin) {
          warnings.push('Translation mixes Arabic and Latin characters');
          suggestions.push('Consider using RTL/LTR marks for proper display');
        }

        // Check for Arabic numerals vs. Arabic-Indic numerals
        const hasWesternDigits = /[0-9]/.test(value);
        const hasArabicDigits = /[٠-٩]/.test(value);

        if (hasWesternDigits && hasArabicDigits) {
          warnings.push('Translation mixes Western and Arabic-Indic numerals');
          suggestions.push('Use consistent numeral system');
        }

        return {
          isValid: true,
          errors: [],
          warnings,
          suggestions
        };
      }
    });

    // Chinese-specific rules
    this.addLanguageSpecificRule('zh', {
      name: 'chinese_characters',
      description: 'Check Chinese character consistency',
      severity: 'info',
      validate: (value: string): ValidationResult => {
        const warnings: string[] = [];
        const suggestions: string[] = [];

        // Check for mixed traditional/simplified Chinese
        const hasTraditional = /[\u4E00-\u9FFF]/.test(value);
        const hasSimplified = /[\u3400-\u4DBF]/.test(value);

        if (hasTraditional && hasSimplified) {
          warnings.push('Translation might mix traditional and simplified Chinese');
          suggestions.push('Use consistent character set');
        }

        return {
          isValid: true,
          errors: [],
          warnings,
          suggestions
        };
      }
    });

    // Spanish-specific rules
    this.addLanguageSpecificRule('es', {
      name: 'spanish_accents',
      description: 'Check for proper Spanish accents and characters',
      severity: 'warning',
      validate: (value: string): ValidationResult => {
        const warnings: string[] = [];
        const suggestions: string[] = [];

        // Check for missing accents in common words
        const commonAccentedWords = [
          { without: 'mas', with: 'más' },
          { without: 'si', with: 'sí' },
          { without: 'tu', with: 'tú' },
          { without: 'el', with: 'él' }
        ];

        for (const word of commonAccentedWords) {
          const regex = new RegExp(`\\b${word.without}\\b`, 'gi');
          if (regex.test(value)) {
            warnings.push(`Consider using "${word.with}" instead of "${word.without}"`);
          }
        }

        return {
          isValid: true,
          errors: [],
          warnings,
          suggestions
        };
      }
    });
  }

  // Rule Management
  addRule(rule: TranslationValidationRule): void {
    this.rules.set(rule.name, rule);
  }

  addLanguageSpecificRule(languageCode: string, rule: TranslationValidationRule): void {
    if (!this.languageSpecificRules.has(languageCode)) {
      this.languageSpecificRules.set(languageCode, []);
    }
    this.languageSpecificRules.get(languageCode)!.push(rule);
  }

  // Validation Methods
  validateTranslation(
    value: string,
    context: TranslationContext
  ): ValidationResult {
    const allErrors: string[] = [];
    const allWarnings: string[] = [];
    const allSuggestions: string[] = [];

    // Run general rules
    for (const rule of this.rules.values()) {
      const result = rule.validate(value, context);
      allErrors.push(...result.errors);
      allWarnings.push(...result.warnings);
      allSuggestions.push(...result.suggestions);
    }

    // Run language-specific rules
    const languageRules = this.languageSpecificRules.get(context.languageCode) || [];
    for (const rule of languageRules) {
      const result = rule.validate(value, context);
      allErrors.push(...result.errors);
      allWarnings.push(...result.warnings);
      allSuggestions.push(...result.suggestions);
    }

    return {
      isValid: allErrors.length === 0,
      errors: [...new Set(allErrors)],
      warnings: [...new Set(allWarnings)],
      suggestions: [...new Set(allSuggestions)]
    };
  }

  validateLanguageQuality(
    translations: Map<string, string>,
    languageCode: string,
    originalTranslations?: Map<string, string>
  ): LanguageQualityReport {
    const issues: TranslationIssue[] = [];
    let errorCount = 0;
    let warningCount = 0;
    let validTranslations = 0;

    for (const [key, value] of translations.entries()) {
      const context: TranslationContext = {
        key,
        languageCode,
        originalValue: originalTranslations?.get(key)
      };

      const result = this.validateTranslation(value, context);

      if (result.isValid) {
        validTranslations++;
      }

      // Process errors
      for (const error of result.errors) {
        errorCount++;
        issues.push({
          key,
          type: 'error',
          rule: 'validation_error',
          message: error,
          originalValue: value,
          context
        });
      }

      // Process warnings
      for (const warning of result.warnings) {
        warningCount++;
        issues.push({
          key,
          type: 'warning',
          rule: 'validation_warning',
          message: warning,
          originalValue: value,
          context
        });
      }
    }

    const totalTranslations = translations.size;
    const completeness = totalTranslations > 0 ? (validTranslations / totalTranslations) * 100 : 0;
    const qualityScore = this.calculateQualityScore(totalTranslations, validTranslations, errorCount, warningCount);

    return {
      languageCode,
      totalTranslations,
      validTranslations,
      errorCount,
      warningCount,
      completeness,
      qualityScore,
      issues,
      recommendations: this.generateRecommendations(issues, languageCode)
    };
  }

  private calculateQualityScore(
    total: number,
    valid: number,
    errors: number,
    warnings: number
  ): number {
    if (total === 0) return 0;

    const validityScore = (valid / total) * 70; // 70% weight for validity
    const errorPenalty = Math.min((errors / total) * 20, 20); // Up to 20% penalty for errors
    const warningPenalty = Math.min((warnings / total) * 10, 10); // Up to 10% penalty for warnings

    return Math.max(0, validityScore - errorPenalty - warningPenalty);
  }

  private generateRecommendations(issues: TranslationIssue[], languageCode: string): string[] {
    const recommendations: string[] = [];
    const errorTypes = new Set(issues.filter(i => i.type === 'error').map(i => i.rule));
    const warningTypes = new Set(issues.filter(i => i.type === 'warning').map(i => i.rule));

    if (errorTypes.has('not_empty')) {
      recommendations.push('Complete all missing translations');
    }

    if (errorTypes.has('interpolation_consistency')) {
      recommendations.push('Review and fix interpolation variable usage');
    }

    if (warningTypes.has('html_safety')) {
      recommendations.push('Review HTML content for safety and necessity');
    }

    if (warningTypes.has('length_reasonable')) {
      recommendations.push('Review translation lengths for reasonableness');
    }

    // Language-specific recommendations
    if (languageCode === 'ar' && warningTypes.has('arabic_text_direction')) {
      recommendations.push('Review Arabic text for proper RTL formatting');
    }

    if (languageCode === 'es' && warningTypes.has('spanish_accents')) {
      recommendations.push('Add proper Spanish accents and diacritical marks');
    }

    if (languageCode === 'zh' && warningTypes.has('chinese_characters')) {
      recommendations.push('Ensure consistent use of Chinese character set');
    }

    return recommendations;
  }

  // Utility Methods
  getAvailableRules(): TranslationValidationRule[] {
    return Array.from(this.rules.values());
  }

  getLanguageSpecificRules(languageCode: string): TranslationValidationRule[] {
    return this.languageSpecificRules.get(languageCode) || [];
  }

  // Auto-correction suggestions
  suggestCorrections(value: string, context: TranslationContext): string[] {
    const suggestions: string[] = [];

    // Auto-fix common issues
    let correctedValue = value;

    // Fix multiple spaces
    if (/\s{2,}/.test(correctedValue)) {
      correctedValue = correctedValue.replace(/\s{2,}/g, ' ');
      suggestions.push(`Fix multiple spaces: "${correctedValue}"`);
    }

    // Fix leading/trailing whitespace
    if (correctedValue !== correctedValue.trim()) {
      correctedValue = correctedValue.trim();
      suggestions.push(`Remove whitespace: "${correctedValue}"`);
    }

    // Language-specific corrections
    if (context.languageCode === 'es') {
      // Suggest accented versions for Spanish
      const accentSuggestions = this.suggestSpanishAccents(correctedValue);
      suggestions.push(...accentSuggestions);
    }

    return suggestions;
  }

  private suggestSpanishAccents(value: string): string[] {
    const suggestions: string[] = [];
    const accentMap: Record<string, string> = {
      'mas': 'más',
      'si': 'sí',
      'tu': 'tú',
      'el': 'él',
      'como': 'cómo',
      'que': 'qué',
      'donde': 'dónde',
      'cuando': 'cuándo'
    };

    let hasChanges = false;
    let corrected = value;

    for (const [without, with_] of Object.entries(accentMap)) {
      const regex = new RegExp(`\\b${without}\\b`, 'gi');
      if (regex.test(corrected)) {
        corrected = corrected.replace(regex, with_);
        hasChanges = true;
      }
    }

    if (hasChanges) {
      suggestions.push(`Add accents: "${corrected}"`);
    }

    return suggestions;
  }
}

// Export singleton instance
export const translationValidator = new TranslationValidator();

// Convenience functions
export const validateTranslation = (
  value: string,
  context: TranslationContext
): ValidationResult => {
  return translationValidator.validateTranslation(value, context);
};

export const validateLanguageQuality = (
  translations: Map<string, string>,
  languageCode: string,
  originalTranslations?: Map<string, string>
): LanguageQualityReport => {
  return translationValidator.validateLanguageQuality(translations, languageCode, originalTranslations);
};

export type {
  TranslationValidationRule,
  TranslationContext,
  ValidationResult,
  LanguageQualityReport,
  TranslationIssue
};