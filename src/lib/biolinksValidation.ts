// Biolinks Validation\n// Validation utilities for biolinks pages, links, and content\n\nimport type {\n  BioPage,\n  BioLink,\n  SocialLink,\n  BioLinksValidationRules,\n  BioLinksValidationResult,\n  PageTheme,\n  CustomTheme\n} from '@/types/biolinks';\nimport { LinkValidationError } from '@/types/biolinks';\n\n// Default validation rules\nexport const defaultBioLinksValidationRules: BioLinksValidationRules = {\n  page: {\n    username: {\n      min_length: 3,\n      max_length: 30,\n      allowed_chars: 'abcdefghijklmnopqrstuvwxyz0123456789_-',\n      reserved_words: [\n        'admin', 'api', 'app', 'blog', 'contact', 'dashboard', 'docs', 'help',\n        'home', 'info', 'login', 'mail', 'news', 'root', 'support', 'test',\n        'user', 'www', 'about', 'terms', 'privacy', 'settings', 'profile'\n      ]\n    },\n    display_name: {\n      min_length: 1,\n      max_length: 50\n    },\n    bio: {\n      max_length: 500\n    }\n  },\n  link: {\n    title: {\n      min_length: 1,\n      max_length: 100\n    },\n    url: {\n      allowed_protocols: ['http', 'https', 'mailto', 'tel'],\n      max_length: 2000,\n      blocked_domains: [\n        'bit.ly/malicious',\n        'tinyurl.com/spam',\n        'suspicious-domain.com'\n        // Add more blocked domains as needed\n      ]\n    },\n    description: {\n      max_length: 200\n    }\n  },\n  limits: {\n    max_links_per_page: 50,\n    max_social_links: 10,\n    max_widgets: 20,\n    max_custom_css_length: 10000\n  }\n};\n\n// Malicious content patterns\nconst MALICIOUS_PATTERNS = [\n  /javascript:/i,\n  /data:/i,\n  /vbscript:/i,\n  /onload=/i,\n  /onerror=/i,\n  /onclick=/i,\n  /<script[\\s\\S]*?>/i,\n  /<iframe[\\s\\S]*?>/i,\n  /<object[\\s\\S]*?>/i,\n  /<embed[\\s\\S]*?>/i\n];\n\n// Spam patterns\nconst SPAM_PATTERNS = [\n  /free\\s+money/i,\n  /guaranteed\\s+income/i,\n  /make\\s+money\\s+fast/i,\n  /get\\s+rich\\s+quick/i,\n  /click\\s+here\\s+now/i,\n  /limited\\s+time\\s+offer/i,\n  /act\\s+now/i\n];\n\nexport class BioLinksValidator {\n  private rules: BioLinksValidationRules;\n\n  constructor(rules: BioLinksValidationRules = defaultBioLinksValidationRules) {\n    this.rules = rules;\n  }\n\n  // Page Validation\n  validatePage(page: Partial<BioPage>): BioLinksValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n    const suggestions: string[] = [];\n\n    // Username validation\n    if (page.username) {\n      const usernameResult = this.validateUsername(page.username);\n      if (!usernameResult.isValid) {\n        errors.push(...usernameResult.errors);\n      }\n      warnings.push(...usernameResult.warnings);\n      if (usernameResult.suggestions) {\n        suggestions.push(...usernameResult.suggestions);\n      }\n    } else {\n      errors.push('Username is required');\n    }\n\n    // Display name validation\n    if (page.display_name) {\n      if (page.display_name.length < this.rules.page.display_name.min_length) {\n        errors.push(`Display name must be at least ${this.rules.page.display_name.min_length} characters`);\n      }\n      if (page.display_name.length > this.rules.page.display_name.max_length) {\n        errors.push(`Display name cannot exceed ${this.rules.page.display_name.max_length} characters`);\n      }\n      if (this.containsMaliciousContent(page.display_name)) {\n        errors.push('Display name contains prohibited content');\n      }\n    } else {\n      errors.push('Display name is required');\n    }\n\n    // Bio validation\n    if (page.bio) {\n      if (page.bio.length > this.rules.page.bio.max_length) {\n        errors.push(`Bio cannot exceed ${this.rules.page.bio.max_length} characters`);\n      }\n      if (this.containsMaliciousContent(page.bio)) {\n        errors.push('Bio contains prohibited content');\n      }\n      if (this.containsSpam(page.bio)) {\n        warnings.push('Bio may contain spam-like content');\n      }\n    }\n\n    // Theme validation\n    if (page.theme) {\n      const themeResult = this.validateTheme(page.theme);\n      if (!themeResult.isValid) {\n        errors.push(...themeResult.errors);\n      }\n      warnings.push(...themeResult.warnings);\n    }\n\n    // Custom theme validation\n    if (page.custom_theme) {\n      const customThemeResult = this.validateCustomTheme(page.custom_theme);\n      if (!customThemeResult.isValid) {\n        errors.push(...customThemeResult.errors);\n      }\n      warnings.push(...customThemeResult.warnings);\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings,\n      suggestions\n    };\n  }\n\n  // Username validation\n  validateUsername(username: string): BioLinksValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n    const suggestions: string[] = [];\n\n    const cleanUsername = username.toLowerCase().trim();\n\n    // Length validation\n    if (cleanUsername.length < this.rules.page.username.min_length) {\n      errors.push(`Username must be at least ${this.rules.page.username.min_length} characters`);\n    }\n    if (cleanUsername.length > this.rules.page.username.max_length) {\n      errors.push(`Username cannot exceed ${this.rules.page.username.max_length} characters`);\n    }\n\n    // Character validation\n    const allowedChars = this.rules.page.username.allowed_chars;\n    const invalidChars = cleanUsername.split('').filter(char => !allowedChars.includes(char));\n    if (invalidChars.length > 0) {\n      errors.push(`Username contains invalid characters: ${[...new Set(invalidChars)].join(', ')}`);\n      suggestions.push('Use only letters, numbers, underscores, and hyphens');\n    }\n\n    // Reserved words validation\n    if (this.rules.page.username.reserved_words.includes(cleanUsername)) {\n      errors.push('Username is reserved and cannot be used');\n      suggestions.push('Try adding numbers or your name to make it unique');\n    }\n\n    // Pattern validation\n    if (cleanUsername.startsWith('-') || cleanUsername.endsWith('-')) {\n      errors.push('Username cannot start or end with a hyphen');\n    }\n    if (cleanUsername.includes('--')) {\n      errors.push('Username cannot contain consecutive hyphens');\n    }\n    if (cleanUsername.startsWith('_') || cleanUsername.endsWith('_')) {\n      warnings.push('Usernames starting or ending with underscore are discouraged');\n    }\n\n    // SEO and readability suggestions\n    if (cleanUsername.length < 5) {\n      suggestions.push('Consider a longer username for better branding');\n    }\n    if (!/[a-z]/.test(cleanUsername)) {\n      suggestions.push('Consider including letters for better readability');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings,\n      suggestions\n    };\n  }\n\n  // Link validation\n  validateLink(link: Partial<BioLink>): BioLinksValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n    const suggestions: string[] = [];\n\n    // Title validation\n    if (!link.title || link.title.trim().length === 0) {\n      errors.push('Link title is required');\n    } else {\n      if (link.title.length < this.rules.link.title.min_length) {\n        errors.push(`Link title must be at least ${this.rules.link.title.min_length} characters`);\n      }\n      if (link.title.length > this.rules.link.title.max_length) {\n        errors.push(`Link title cannot exceed ${this.rules.link.title.max_length} characters`);\n      }\n      if (this.containsMaliciousContent(link.title)) {\n        errors.push('Link title contains prohibited content');\n      }\n    }\n\n    // URL validation\n    if (!link.url || link.url.trim().length === 0) {\n      errors.push('Link URL is required');\n    } else {\n      const urlResult = this.validateUrl(link.url);\n      if (!urlResult.isValid) {\n        errors.push(...urlResult.errors);\n      }\n      warnings.push(...urlResult.warnings);\n      if (urlResult.suggestions) {\n        suggestions.push(...urlResult.suggestions);\n      }\n    }\n\n    // Description validation\n    if (link.description) {\n      if (link.description.length > this.rules.link.description.max_length) {\n        errors.push(`Link description cannot exceed ${this.rules.link.description.max_length} characters`);\n      }\n      if (this.containsMaliciousContent(link.description)) {\n        errors.push('Link description contains prohibited content');\n      }\n    }\n\n    // Position validation\n    if (link.position !== undefined && link.position < 1) {\n      errors.push('Link position must be a positive number');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings,\n      suggestions\n    };\n  }\n\n  // URL validation\n  validateUrl(url: string): BioLinksValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n    const suggestions: string[] = [];\n\n    const trimmedUrl = url.trim();\n\n    // Length validation\n    if (trimmedUrl.length > this.rules.link.url.max_length) {\n      errors.push(`URL cannot exceed ${this.rules.link.url.max_length} characters`);\n    }\n\n    // Basic URL format validation\n    try {\n      const urlObj = new URL(trimmedUrl);\n      \n      // Protocol validation\n      if (!this.rules.link.url.allowed_protocols.includes(urlObj.protocol.slice(0, -1))) {\n        errors.push(`Protocol '${urlObj.protocol}' is not allowed`);\n        suggestions.push('Use http://, https://, mailto:, or tel: protocols');\n      }\n\n      // Domain validation\n      if (urlObj.hostname) {\n        const domain = urlObj.hostname.toLowerCase();\n        \n        // Check blocked domains\n        const isBlocked = this.rules.link.url.blocked_domains.some(blockedDomain => \n          domain.includes(blockedDomain.toLowerCase())\n        );\n        if (isBlocked) {\n          errors.push('This domain is not allowed');\n        }\n\n        // Security warnings\n        if (domain.includes('bit.ly') || domain.includes('tinyurl') || domain.includes('t.co')) {\n          warnings.push('Shortened URLs may not show the actual destination');\n          suggestions.push('Consider using the full URL for transparency');\n        }\n\n        // Local/private IP warnings\n        if (domain === 'localhost' || domain.startsWith('192.168.') || domain.startsWith('10.')) {\n          warnings.push('Local URLs may not be accessible to visitors');\n        }\n      }\n\n      // Malicious content check\n      if (this.containsMaliciousContent(trimmedUrl)) {\n        errors.push('URL contains prohibited content');\n      }\n\n    } catch (error) {\n      // If URL constructor fails, try to provide helpful error messages\n      if (!trimmedUrl.includes('://')) {\n        errors.push('URL must include a protocol (http:// or https://)');\n        suggestions.push(`Try: https://${trimmedUrl}`);\n      } else {\n        errors.push('Invalid URL format');\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings,\n      suggestions\n    };\n  }\n\n  // Social link validation\n  validateSocialLink(socialLink: Partial<SocialLink>): BioLinksValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n    const suggestions: string[] = [];\n\n    // Platform validation\n    if (!socialLink.platform) {\n      errors.push('Social platform is required');\n    }\n\n    // Username validation\n    if (!socialLink.username || socialLink.username.trim().length === 0) {\n      errors.push('Social username is required');\n    } else {\n      // Platform-specific username validation\n      const username = socialLink.username.trim();\n      \n      switch (socialLink.platform) {\n        case 'instagram':\n        case 'twitter':\n          if (username.length > 15) {\n            errors.push(`${socialLink.platform} username cannot exceed 15 characters`);\n          }\n          if (!/^[a-zA-Z0-9_]+$/.test(username)) {\n            errors.push(`${socialLink.platform} username can only contain letters, numbers, and underscores`);\n          }\n          break;\n        case 'youtube':\n          if (username.length > 100) {\n            errors.push('YouTube channel name cannot exceed 100 characters');\n          }\n          break;\n        case 'email':\n          const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n          if (!emailRegex.test(username)) {\n            errors.push('Invalid email format');\n          }\n          break;\n      }\n    }\n\n    // URL validation\n    if (socialLink.url) {\n      const urlResult = this.validateUrl(socialLink.url);\n      if (!urlResult.isValid) {\n        errors.push(...urlResult.errors.map(error => `Social link URL: ${error}`));\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings,\n      suggestions\n    };\n  }\n\n  // Theme validation\n  validateTheme(theme: PageTheme): BioLinksValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // Background validation\n    if (theme.background_type === 'image' && theme.background_value) {\n      const urlResult = this.validateUrl(theme.background_value);\n      if (!urlResult.isValid) {\n        errors.push('Invalid background image URL');\n      }\n    }\n\n    // Color validation\n    if (theme.text_color && !this.isValidColor(theme.text_color)) {\n      errors.push('Invalid text color format');\n    }\n\n    // Custom CSS validation\n    if (theme.custom_css) {\n      if (theme.custom_css.length > this.rules.limits.max_custom_css_length) {\n        errors.push(`Custom CSS cannot exceed ${this.rules.limits.max_custom_css_length} characters`);\n      }\n      if (this.containsMaliciousContent(theme.custom_css)) {\n        errors.push('Custom CSS contains prohibited content');\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  // Custom theme validation\n  validateCustomTheme(customTheme: CustomTheme): BioLinksValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // Color validation\n    const colors = [\n      { name: 'primary', value: customTheme.primary_color },\n      { name: 'secondary', value: customTheme.secondary_color },\n      { name: 'accent', value: customTheme.accent_color },\n      { name: 'background', value: customTheme.background_color },\n      { name: 'text', value: customTheme.text_color }\n    ];\n\n    for (const color of colors) {\n      if (color.value && !this.isValidColor(color.value)) {\n        errors.push(`Invalid ${color.name} color format`);\n      }\n    }\n\n    // Border radius validation\n    if (customTheme.border_radius < 0 || customTheme.border_radius > 50) {\n      errors.push('Border radius must be between 0 and 50 pixels');\n    }\n\n    // Gradient direction validation\n    if (customTheme.gradient_direction !== undefined) {\n      if (customTheme.gradient_direction < 0 || customTheme.gradient_direction > 360) {\n        errors.push('Gradient direction must be between 0 and 360 degrees');\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  // Utility validation methods\n  private containsMaliciousContent(content: string): boolean {\n    return MALICIOUS_PATTERNS.some(pattern => pattern.test(content));\n  }\n\n  private containsSpam(content: string): boolean {\n    return SPAM_PATTERNS.some(pattern => pattern.test(content));\n  }\n\n  private isValidColor(color: string): boolean {\n    // Check hex colors\n    if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color)) {\n      return true;\n    }\n    \n    // Check rgb/rgba colors\n    if (/^rgba?\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*(,\\s*[0-9.]+)?\\s*\\)$/.test(color)) {\n      return true;\n    }\n    \n    // Check hsl/hsla colors\n    if (/^hsla?\\(\\s*\\d+\\s*,\\s*\\d+%\\s*,\\s*\\d+%\\s*(,\\s*[0-9.]+)?\\s*\\)$/.test(color)) {\n      return true;\n    }\n    \n    // Check CSS color names (basic list)\n    const cssColors = [\n      'black', 'white', 'red', 'green', 'blue', 'yellow', 'cyan', 'magenta',\n      'gray', 'grey', 'orange', 'purple', 'brown', 'pink', 'transparent'\n    ];\n    \n    return cssColors.includes(color.toLowerCase());\n  }\n\n  // Validation rule management\n  updateRules(newRules: Partial<BioLinksValidationRules>): void {\n    this.rules = { ...this.rules, ...newRules };\n  }\n\n  getRules(): BioLinksValidationRules {\n    return { ...this.rules };\n  }\n}\n\n// Utility functions\nexport const validateBioPage = (\n  page: Partial<BioPage>,\n  rules?: BioLinksValidationRules\n): BioLinksValidationResult => {\n  const validator = new BioLinksValidator(rules);\n  return validator.validatePage(page);\n};\n\nexport const validateBioLink = (\n  link: Partial<BioLink>,\n  rules?: BioLinksValidationRules\n): BioLinksValidationResult => {\n  const validator = new BioLinksValidator(rules);\n  return validator.validateLink(link);\n};\n\nexport const validateUsername = (\n  username: string,\n  rules?: BioLinksValidationRules\n): BioLinksValidationResult => {\n  const validator = new BioLinksValidator(rules);\n  return validator.validateUsername(username);\n};\n\nexport const validateUrl = (\n  url: string,\n  rules?: BioLinksValidationRules\n): BioLinksValidationResult => {\n  const validator = new BioLinksValidator(rules);\n  return validator.validateUrl(url);\n};\n\n// Safe content sanitizer\nexport const sanitizeContent = (content: string): string => {\n  // Remove potentially dangerous content\n  let sanitized = content\n    .replace(/<script[\\s\\S]*?<\\/script>/gi, '')\n    .replace(/<iframe[\\s\\S]*?<\\/iframe>/gi, '')\n    .replace(/javascript:/gi, '')\n    .replace(/data:/gi, '')\n    .replace(/vbscript:/gi, '');\n  \n  return sanitized;\n};\n\n// Check if content is safe for public display\nexport const isContentSafe = (content: string): boolean => {\n  const validator = new BioLinksValidator();\n  return !validator['containsMaliciousContent'](content) && !validator['containsSpam'](content);\n};\n\n// Extract validation errors for API responses\nexport const formatValidationErrors = (result: BioLinksValidationResult): LinkValidationError => {\n  return new LinkValidationError(\n    result.errors.join(', '),\n    result.errors\n  );\n};\n\n// Generate username suggestions\nexport const generateUsernameSuggestions = (baseName: string, count = 5): string[] => {\n  const suggestions: string[] = [];\n  const cleanBase = baseName.toLowerCase().replace(/[^a-z0-9]/g, '');\n  \n  if (cleanBase.length >= 3) {\n    // Add numbers\n    for (let i = 1; i <= count; i++) {\n      suggestions.push(`${cleanBase}${i}`);\n    }\n    \n    // Add random suffixes\n    const suffixes = ['_official', '_real', '_page', '_profile', '_link'];\n    suffixes.slice(0, count - suggestions.length).forEach(suffix => {\n      if (cleanBase.length + suffix.length <= 30) {\n        suggestions.push(`${cleanBase}${suffix}`);\n      }\n    });\n  }\n  \n  return suggestions.slice(0, count);\n};\n\n// Export singleton validator instance\nexport const bioLinksValidator = new BioLinksValidator();