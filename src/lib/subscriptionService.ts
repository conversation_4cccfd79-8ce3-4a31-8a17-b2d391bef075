// Subscription Service
// Manages subscriptions, payments, and billing

import { supabase } from '@/integrations/supabase/client';
import type {
  Subscription,
  SubscriptionPlan,
  PaymentMethod,
  Invoice,
  PaymentIntent,
  Discount,
  SubscriptionUsage,
  BillingEvent,
  SubscriptionAPIResponse,
  SubscriptionResponse,
  PlansResponse,
  PaymentMethodResponse,
  InvoicesResponse,
  CheckoutSessionResponse,
  BillingCycle,
  PaymentProvider,
  SubscriptionStatus,
  UsageEvent
} from '@/types/subscription';
import { SubscriptionError, PaymentError, PlanLimitError } from '@/types/subscription';

export class SubscriptionService {
  private stripePublishableKey: string;
  private webhookEndpoints: Map<PaymentProvider, string> = new Map();

  constructor() {
    this.stripePublishableKey = process.env.VITE_STRIPE_PUBLISHABLE_KEY || '';
    this.setupWebhookEndpoints();
  }

  // Subscription Management
  async createSubscription(
    userId: string,
    planId: string,
    paymentMethodId: string,
    billingCycle: BillingCycle = 'monthly',
    discountCode?: string
  ): Promise<SubscriptionResponse> {
    try {
      // Get plan details
      const plan = await this.getPlanById(planId);
      if (!plan.success || !plan.data) {
        throw new SubscriptionError('Plan not found', 'PLAN_NOT_FOUND');
      }

      // Check if user already has an active subscription
      const { data: existingSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .in('status', ['active', 'trialing'])
        .single();

      if (existingSubscription) {
        throw new SubscriptionError('User already has an active subscription', 'SUBSCRIPTION_EXISTS');
      }

      // Calculate pricing
      const amount = billingCycle === 'yearly' ? plan.data.price_yearly : plan.data.price_monthly;
      
      // Apply discount if provided
      let discountAmount = 0;
      let discountId: string | undefined;
      if (discountCode) {
        const discountResult = await this.validateDiscount(discountCode, planId);
        if (discountResult.success && discountResult.data) {
          discountAmount = this.calculateDiscountAmount(amount, discountResult.data);
          discountId = discountResult.data.id;
        }
      }

      const finalAmount = amount - discountAmount;

      // Create subscription record
      const trialEnd = plan.data.trial_days > 0 
        ? new Date(Date.now() + plan.data.trial_days * 24 * 60 * 60 * 1000).toISOString()
        : undefined;

      const subscriptionData = {
        user_id: userId,
        plan_id: planId,
        status: plan.data.trial_days > 0 ? 'trialing' as SubscriptionStatus : 'active' as SubscriptionStatus,
        current_period_start: new Date().toISOString(),
        current_period_end: this.calculatePeriodEnd(billingCycle),
        trial_start: plan.data.trial_days > 0 ? new Date().toISOString() : undefined,
        trial_end: trialEnd,
        payment_method_id: paymentMethodId,
        billing_cycle: billingCycle,
        next_billing_date: trialEnd || this.calculatePeriodEnd(billingCycle),
        amount: finalAmount,
        currency: plan.data.currency,
        discount_id: discountId,
        created_at: new Date().toISOString()
      };

      const { data: subscription, error } = await supabase
        .from('subscriptions')
        .insert(subscriptionData)
        .select()
        .single();

      if (error) {
        throw new SubscriptionError('Failed to create subscription', 'SUBSCRIPTION_CREATE_ERROR', undefined, { error });
      }

      // Create billing event
      await this.createBillingEvent(
        userId,
        subscription.id,
        'subscription_created',
        finalAmount,
        plan.data.currency,
        'Subscription created'
      );

      // If not in trial, process initial payment
      if (plan.data.trial_days === 0 && finalAmount > 0) {
        await this.processPayment(subscription.id, paymentMethodId, finalAmount, plan.data.currency);
      }

      return {
        success: true,
        data: subscription,
        plan: plan.data
      };
    } catch (error) {
      console.error('Subscription creation error:', error);
      return {
        success: false,
        error: {
          code: error instanceof SubscriptionError ? error.code : 'SUBSCRIPTION_CREATE_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create subscription'
        }
      };
    }
  }

  async getSubscription(userId: string): Promise<SubscriptionResponse> {
    try {
      const { data: subscription, error } = await supabase
        .from('subscriptions')
        .select(`
          *,
          subscription_plans!inner(*)
        `)
        .eq('user_id', userId)
        .in('status', ['active', 'trialing', 'past_due'])
        .single();

      if (error) {
        throw new SubscriptionError('Subscription not found', 'SUBSCRIPTION_NOT_FOUND', undefined, { error });
      }

      // Get current usage
      const usageResult = await this.getCurrentUsage(userId);

      return {
        success: true,
        data: subscription,
        plan: subscription.subscription_plans,
        usage: usageResult.success ? usageResult.data : undefined
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'SUBSCRIPTION_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch subscription'
        }
      };
    }
  }

  async updateSubscription(
    userId: string,
    updates: Partial<Subscription>
  ): Promise<SubscriptionResponse> {
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        throw new SubscriptionError('Failed to update subscription', 'SUBSCRIPTION_UPDATE_ERROR', undefined, { error });
      }

      // Create billing event
      await this.createBillingEvent(
        userId,
        data.id,
        'subscription_updated',
        undefined,
        data.currency,
        'Subscription updated'
      );

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'SUBSCRIPTION_UPDATE_ERROR',
          message: error instanceof Error ? error.message : 'Failed to update subscription'
        }
      };
    }
  }

  async cancelSubscription(
    userId: string,
    immediate = false
  ): Promise<SubscriptionResponse> {
    try {
      const currentSubscription = await this.getSubscription(userId);
      if (!currentSubscription.success || !currentSubscription.data) {
        throw new SubscriptionError('Subscription not found', 'SUBSCRIPTION_NOT_FOUND');
      }

      const canceledAt = new Date().toISOString();
      const endedAt = immediate ? canceledAt : currentSubscription.data.current_period_end;

      const { data, error } = await supabase
        .from('subscriptions')
        .update({
          status: immediate ? 'canceled' : 'active',
          canceled_at: canceledAt,
          ended_at: immediate ? canceledAt : undefined,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        throw new SubscriptionError('Failed to cancel subscription', 'SUBSCRIPTION_CANCEL_ERROR', undefined, { error });
      }

      // Create billing event
      await this.createBillingEvent(
        userId,
        data.id,
        'subscription_canceled',
        undefined,
        data.currency,
        immediate ? 'Subscription canceled immediately' : 'Subscription will cancel at period end'
      );

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'SUBSCRIPTION_CANCEL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to cancel subscription'
        }
      };
    }
  }

  async changePlan(
    userId: string,
    newPlanId: string,
    billingCycle?: BillingCycle
  ): Promise<SubscriptionResponse> {
    try {
      const currentSubscription = await this.getSubscription(userId);
      if (!currentSubscription.success || !currentSubscription.data) {
        throw new SubscriptionError('Subscription not found', 'SUBSCRIPTION_NOT_FOUND');
      }

      const newPlan = await this.getPlanById(newPlanId);
      if (!newPlan.success || !newPlan.data) {
        throw new SubscriptionError('Plan not found', 'PLAN_NOT_FOUND');
      }

      const subscription = currentSubscription.data;
      const plan = newPlan.data;
      const newBillingCycle = billingCycle || subscription.billing_cycle;
      const newAmount = newBillingCycle === 'yearly' ? plan.price_yearly : plan.price_monthly;

      // Calculate proration if upgrading mid-cycle
      const prorationAmount = this.calculateProration(
        subscription.amount,
        newAmount,
        new Date(subscription.current_period_end).getTime() - Date.now()
      );

      const { data, error } = await supabase
        .from('subscriptions')
        .update({
          plan_id: newPlanId,
          billing_cycle: newBillingCycle,
          amount: newAmount,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        throw new SubscriptionError('Failed to change plan', 'PLAN_CHANGE_ERROR', undefined, { error });
      }

      // Process proration payment if needed
      if (prorationAmount > 0 && subscription.payment_method_id) {
        await this.processPayment(
          subscription.id,
          subscription.payment_method_id,
          prorationAmount,
          subscription.currency,
          'Plan upgrade proration'
        );
      }

      // Create billing event
      await this.createBillingEvent(
        userId,
        data.id,
        'subscription_updated',
        newAmount,
        data.currency,
        `Plan changed to ${plan.name}`
      );

      return { success: true, data, plan };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'PLAN_CHANGE_ERROR',
          message: error instanceof Error ? error.message : 'Failed to change plan'
        }
      };
    }
  }

  // Plan Management
  async getPlans(): Promise<PlansResponse> {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price_monthly', { ascending: true });

      if (error) {
        throw new SubscriptionError('Failed to fetch plans', 'PLANS_FETCH_ERROR', undefined, { error });
      }

      // Get current user's plan if logged in
      let currentPlan: SubscriptionPlan | undefined;
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const subscriptionResult = await this.getSubscription(user.id);
        if (subscriptionResult.success && subscriptionResult.plan) {
          currentPlan = subscriptionResult.plan;
        }
      }

      return {
        success: true,
        data: data || [],
        current_plan: currentPlan
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'PLANS_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch plans'
        }
      };
    }
  }

  async getPlanById(planId: string): Promise<SubscriptionAPIResponse<SubscriptionPlan>> {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('id', planId)
        .eq('is_active', true)
        .single();

      if (error) {
        throw new SubscriptionError('Plan not found', 'PLAN_NOT_FOUND', undefined, { error });
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'PLAN_NOT_FOUND',
          message: error instanceof Error ? error.message : 'Plan not found'
        }
      };
    }
  }

  // Payment Methods
  async addPaymentMethod(
    userId: string,
    paymentMethodData: Omit<PaymentMethod, 'id' | 'user_id' | 'created_at' | 'updated_at'>
  ): Promise<PaymentMethodResponse> {
    try {
      // Set as default if it's the first payment method
      const { data: existingMethods } = await supabase
        .from('payment_methods')
        .select('id')
        .eq('user_id', userId)
        .eq('is_active', true);

      const isFirst = !existingMethods || existingMethods.length === 0;

      const newPaymentMethod = {
        ...paymentMethodData,
        user_id: userId,
        is_default: isFirst || paymentMethodData.is_default,
        created_at: new Date().toISOString()
      };

      // If setting as default, unset other defaults
      if (newPaymentMethod.is_default) {
        await supabase
          .from('payment_methods')
          .update({ is_default: false })
          .eq('user_id', userId);
      }

      const { data, error } = await supabase
        .from('payment_methods')
        .insert(newPaymentMethod)
        .select()
        .single();

      if (error) {
        throw new SubscriptionError('Failed to add payment method', 'PAYMENT_METHOD_ADD_ERROR', undefined, { error });
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'PAYMENT_METHOD_ADD_ERROR',
          message: error instanceof Error ? error.message : 'Failed to add payment method'
        }
      };
    }
  }

  async getPaymentMethods(userId: string): Promise<SubscriptionAPIResponse<PaymentMethod[]>> {
    try {
      const { data, error } = await supabase
        .from('payment_methods')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('is_default', { ascending: false });

      if (error) {
        throw new SubscriptionError('Failed to fetch payment methods', 'PAYMENT_METHODS_FETCH_ERROR', undefined, { error });
      }

      return { success: true, data: data || [] };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'PAYMENT_METHODS_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch payment methods'
        }
      };
    }
  }

  async deletePaymentMethod(userId: string, paymentMethodId: string): Promise<SubscriptionAPIResponse<void>> {
    try {
      const { error } = await supabase
        .from('payment_methods')
        .update({ is_active: false })
        .eq('id', paymentMethodId)
        .eq('user_id', userId);

      if (error) {
        throw new SubscriptionError('Failed to delete payment method', 'PAYMENT_METHOD_DELETE_ERROR', undefined, { error });
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'PAYMENT_METHOD_DELETE_ERROR',
          message: error instanceof Error ? error.message : 'Failed to delete payment method'
        }
      };
    }
  }

  // Invoices
  async getInvoices(userId: string, limit = 20): Promise<InvoicesResponse> {
    try {
      const { data, error } = await supabase
        .from('invoices')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw new SubscriptionError('Failed to fetch invoices', 'INVOICES_FETCH_ERROR', undefined, { error });
      }

      // Get upcoming invoice if exists
      const { data: upcomingInvoice } = await supabase
        .from('invoices')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'draft')
        .single();

      return {
        success: true,
        data: data || [],
        upcoming_invoice: upcomingInvoice || undefined
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'INVOICES_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch invoices'
        }
      };
    }
  }

  // Usage Tracking
  async trackUsage(event: UsageEvent): Promise<SubscriptionAPIResponse<void>> {
    try {
      // Get current subscription
      const subscription = await this.getSubscription(event.user_id);
      if (!subscription.success || !subscription.data || !subscription.plan) {
        throw new SubscriptionError('Active subscription not found', 'SUBSCRIPTION_NOT_FOUND');
      }

      // Check usage limits
      const currentUsage = await this.getCurrentUsage(event.user_id);
      if (currentUsage.success && currentUsage.data) {
        const exceeded = this.checkUsageLimits(event, currentUsage.data, subscription.plan.limits);
        if (exceeded) {
          throw new PlanLimitError(
            event.feature,
            this.getFeatureUsage(event.feature, currentUsage.data),
            this.getFeatureLimit(event.feature, subscription.plan.limits),
            subscription.data.id
          );
        }
      }

      // Record usage event
      await supabase
        .from('usage_events')
        .insert({
          user_id: event.user_id,
          subscription_id: event.subscription_id,
          feature: event.feature,
          quantity: event.quantity,
          timestamp: event.timestamp,
          metadata: event.metadata
        });

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: error instanceof PlanLimitError ? 'USAGE_LIMIT_EXCEEDED' : 'USAGE_TRACKING_ERROR',
          message: error instanceof Error ? error.message : 'Failed to track usage'
        }
      };
    }
  }

  async getCurrentUsage(userId: string): Promise<SubscriptionAPIResponse<SubscriptionUsage>> {
    try {
      const currentPeriodStart = new Date();
      currentPeriodStart.setDate(1);
      currentPeriodStart.setHours(0, 0, 0, 0);

      const { data, error } = await supabase
        .from('subscription_usage')
        .select('*')
        .eq('user_id', userId)
        .gte('period_start', currentPeriodStart.toISOString())
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        throw new SubscriptionError('Failed to fetch usage', 'USAGE_FETCH_ERROR', undefined, { error });
      }

      return { success: true, data: data || this.createEmptyUsage(userId) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'USAGE_FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch usage'
        }
      };
    }
  }

  // Checkout and Payment Processing
  async createCheckoutSession(
    userId: string,
    planId: string,
    billingCycle: BillingCycle = 'monthly',
    successUrl: string,
    cancelUrl: string
  ): Promise<CheckoutSessionResponse> {
    try {
      // This would typically integrate with Stripe or another payment processor
      // For now, we'll create a mock session
      const sessionId = `cs_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const expiresAt = new Date(Date.now() + 30 * 60 * 1000).toISOString(); // 30 minutes

      // Store session data temporarily
      await supabase
        .from('checkout_sessions')
        .insert({
          session_id: sessionId,
          user_id: userId,
          plan_id: planId,
          billing_cycle: billingCycle,
          success_url: successUrl,
          cancel_url: cancelUrl,
          expires_at: expiresAt,
          status: 'pending',
          created_at: new Date().toISOString()
        });

      return {
        success: true,
        data: {
          session_id: sessionId,
          url: `/checkout/${sessionId}`,
          expires_at: expiresAt
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'CHECKOUT_SESSION_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create checkout session'
        }
      };
    }
  }

  // Utility Methods
  private calculatePeriodEnd(billingCycle: BillingCycle): string {
    const now = new Date();
    switch (billingCycle) {
      case 'monthly':
        now.setMonth(now.getMonth() + 1);
        break;
      case 'yearly':
        now.setFullYear(now.getFullYear() + 1);
        break;
      case 'quarterly':
        now.setMonth(now.getMonth() + 3);
        break;
      case 'weekly':
        now.setDate(now.getDate() + 7);
        break;
    }
    return now.toISOString();
  }

  private calculateProration(currentAmount: number, newAmount: number, daysRemaining: number): number {
    const dailyDiff = (newAmount - currentAmount) / 30; // Assume 30 days per month
    const daysRemainingInMonth = Math.ceil(daysRemaining / (1000 * 60 * 60 * 24));
    return Math.max(0, dailyDiff * daysRemainingInMonth);
  }

  private calculateDiscountAmount(amount: number, discount: Discount): number {
    if (discount.type === 'percentage') {
      return amount * (discount.value / 100);
    } else if (discount.type === 'fixed_amount') {
      return Math.min(discount.value, amount);
    }
    return 0;
  }

  private async validateDiscount(code: string, planId: string): Promise<SubscriptionAPIResponse<Discount>> {
    try {
      const { data, error } = await supabase
        .from('discounts')
        .select('*')
        .eq('code', code.toUpperCase())
        .eq('is_active', true)
        .single();

      if (error) {
        throw new SubscriptionError('Invalid discount code', 'DISCOUNT_INVALID');
      }

      // Check validity dates
      const now = new Date();
      const validFrom = new Date(data.valid_from);
      const validUntil = data.valid_until ? new Date(data.valid_until) : null;

      if (now < validFrom || (validUntil && now > validUntil)) {
        throw new SubscriptionError('Discount code has expired', 'DISCOUNT_EXPIRED');
      }

      // Check applicable plans
      if (data.applicable_plans.length > 0 && !data.applicable_plans.includes(planId)) {
        throw new SubscriptionError('Discount not applicable to this plan', 'DISCOUNT_NOT_APPLICABLE');
      }

      // Check redemption limits
      if (data.max_redemptions && data.redemptions_count >= data.max_redemptions) {
        throw new SubscriptionError('Discount code has reached its usage limit', 'DISCOUNT_LIMIT_REACHED');
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: error instanceof SubscriptionError ? error.code : 'DISCOUNT_VALIDATION_ERROR',
          message: error instanceof Error ? error.message : 'Failed to validate discount'
        }
      };
    }
  }

  private async processPayment(
    subscriptionId: string,
    paymentMethodId: string,
    amount: number,
    currency: string,
    description = 'Subscription payment'
  ): Promise<void> {
    // This would integrate with actual payment processors
    // For now, we'll simulate payment processing
    
    const paymentIntentData = {
      subscription_id: subscriptionId,
      payment_method_id: paymentMethodId,
      amount,
      currency,
      description,
      status: 'succeeded' as const,
      created_at: new Date().toISOString(),
      succeeded_at: new Date().toISOString()
    };

    await supabase
      .from('payment_intents')
      .insert(paymentIntentData);
  }

  private async createBillingEvent(
    userId: string,
    subscriptionId: string,
    type: string,
    amount?: number,
    currency?: string,
    description = ''
  ): Promise<void> {
    await supabase
      .from('billing_events')
      .insert({
        user_id: userId,
        subscription_id: subscriptionId,
        type,
        amount,
        currency,
        description,
        processed_at: new Date().toISOString(),
        created_at: new Date().toISOString()
      });
  }

  private checkUsageLimits(event: UsageEvent, usage: SubscriptionUsage, limits: any): boolean {
    const featureUsage = this.getFeatureUsage(event.feature, usage);
    const featureLimit = this.getFeatureLimit(event.feature, limits);
    
    return featureUsage + event.quantity > featureLimit;
  }

  private getFeatureUsage(feature: string, usage: SubscriptionUsage): number {
    switch (feature) {
      case 'links': return usage.links_created;
      case 'rooms': return usage.rooms_used;
      case 'avatars': return usage.avatars_created;
      case 'chat_sessions': return usage.chat_sessions;
      case 'page_views': return usage.page_views;
      case 'api_requests': return usage.api_requests;
      default: return 0;
    }
  }

  private getFeatureLimit(feature: string, limits: any): number {
    switch (feature) {
      case 'links': return limits.max_links;
      case 'rooms': return limits.max_rooms;
      case 'avatars': return limits.max_avatars;
      case 'chat_sessions': return limits.max_chat_sessions_per_month;
      case 'page_views': return limits.max_page_views_per_month;
      default: return Infinity;
    }
  }

  private createEmptyUsage(userId: string): SubscriptionUsage {
    const now = new Date();
    const periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    return {
      id: '',
      user_id: userId,
      subscription_id: '',
      period_start: periodStart.toISOString(),
      period_end: periodEnd.toISOString(),
      links_created: 0,
      rooms_used: 0,
      avatars_created: 0,
      chat_sessions: 0,
      page_views: 0,
      api_requests: 0,
      storage_used_mb: 0,
      bandwidth_used_gb: 0,
      created_at: new Date().toISOString()
    };
  }

  private setupWebhookEndpoints(): void {
    this.webhookEndpoints.set('stripe', '/api/webhooks/stripe');
    this.webhookEndpoints.set('paypal', '/api/webhooks/paypal');
    this.webhookEndpoints.set('heey_wallet', '/api/webhooks/heey-wallet');
  }
}

// Export singleton instance
export const subscriptionService = new SubscriptionService();

// Export factory function for testing
export const createSubscriptionService = (): SubscriptionService => new SubscriptionService();