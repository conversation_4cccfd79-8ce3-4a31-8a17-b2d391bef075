
import { supabase } from '@/integrations/supabase/client';

const AVATAR_API_URL = 'https://lnooinvefbuowtkqkbjd.supabase.co/functions/v1/avatar';

// Helper to add a timeout to fetch requests
const fetchWithTimeout = async (input: RequestInfo | URL, init?: RequestInit, timeout = 10000) => {
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);
  try {
    return await fetch(input, { ...(init || {}), signal: controller.signal });
  } finally {
    clearTimeout(id);
  }
};

// Helper to get auth token
const getAuthToken = async () => {
  const { data: { session } } = await supabase.auth.getSession();
  return session?.access_token;
};

// GET /api/avatar - Get user's avatar
export const fetchUserAvatar = async (): Promise<{ avatar_url: string | null }> => {
  const token = await getAuthToken();
  if (!token) throw new Error('Not authenticated');

  const response = await fetchWithTimeout(AVATAR_API_URL, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch avatar');
  }

  return response.json();
};

// POST /api/avatar - Save avatar URL
export const saveUserAvatar = async (avatar_url: string): Promise<{ avatar_url: string }> => {
  const token = await getAuthToken();
  if (!token) throw new Error('Not authenticated');

  const response = await fetchWithTimeout(AVATAR_API_URL, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ avatar_url }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to save avatar');
  }

  return response.json();
};

// DELETE /api/avatar - Delete user's avatar
export const deleteUserAvatar = async (): Promise<{ message: string }> => {
  const token = await getAuthToken();
  if (!token) throw new Error('Not authenticated');

  const response = await fetchWithTimeout(AVATAR_API_URL, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to delete avatar');
  }

  return response.json();
};
