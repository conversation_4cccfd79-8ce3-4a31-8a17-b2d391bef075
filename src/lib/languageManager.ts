// Language Manager
// Advanced language management system with dynamic loading, caching, and validation

import { supabase } from '@/integrations/supabase/client';

interface LanguageConfig {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  direction: 'ltr' | 'rtl';
  region?: string;
  fallback?: string;
  completeness: number;
  isActive: boolean;
  lastUpdated: Date;
}

interface TranslationKey {
  key: string;
  value: string;
  context?: string;
  interpolations?: string[];
  pluralization?: {
    zero?: string;
    one?: string;
    two?: string;
    few?: string;
    many?: string;
    other: string;
  };
}

interface TranslationStats {
  totalKeys: number;
  translatedKeys: number;
  missingKeys: string[];
  outdatedKeys: string[];
  completeness: number;
  lastSyncTime: Date;
}

interface LanguageDetectionResult {
  language: string;
  confidence: number;
  source: 'browser' | 'user_preference' | 'geolocation' | 'fallback';
}

export class LanguageManager {
  private currentLanguage: string = 'en';
  private fallbackLanguage: string = 'en';
  private translations: Map<string, Map<string, TranslationKey>> = new Map();
  private languageConfigs: Map<string, LanguageConfig> = new Map();
  private loadingPromises: Map<string, Promise<void>> = new Map();
  private translationCache: Map<string, string> = new Map();
  private observers: Set<(language: string) => void> = new Set();
  private pluralRules: Map<string, Intl.PluralRules> = new Map();
  private rtlLanguages = new Set(['ar', 'he', 'fa', 'ur', 'yi']);

  // Initialize language manager
  async initialize(): Promise<void> {
    try {
      await this.loadLanguageConfigs();
      const detectedLanguage = await this.detectUserLanguage();
      await this.setLanguage(detectedLanguage.language);
      this.setupPluralRules();
    } catch (error) {
      console.error('Language manager initialization failed:', error);
      await this.setLanguage(this.fallbackLanguage);
    }
  }

  // Language Detection
  async detectUserLanguage(): Promise<LanguageDetectionResult> {
    // Check user preference from database
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('preferred_language')
          .eq('id', user.id)
          .single();

        if (profile?.preferred_language && this.isLanguageSupported(profile.preferred_language)) {
          return {
            language: profile.preferred_language,
            confidence: 1.0,
            source: 'user_preference'
          };
        }
      }
    } catch (error) {
      console.debug('Could not load user language preference:', error);
    }

    // Check browser language
    const browserLanguages = navigator.languages || [navigator.language];
    for (const lang of browserLanguages) {
      const normalizedLang = this.normalizeLanguageCode(lang);
      if (this.isLanguageSupported(normalizedLang)) {
        return {
          language: normalizedLang,
          confidence: 0.8,
          source: 'browser'
        };
      }

      // Try language without region (e.g., 'en' from 'en-US')
      const baseLang = normalizedLang.split('-')[0];
      if (this.isLanguageSupported(baseLang)) {
        return {
          language: baseLang,
          confidence: 0.6,
          source: 'browser'
        };
      }
    }

    // Geolocation-based detection (optional)
    try {
      const geoLanguage = await this.detectLanguageFromGeolocation();
      if (geoLanguage && this.isLanguageSupported(geoLanguage)) {
        return {
          language: geoLanguage,
          confidence: 0.4,
          source: 'geolocation'
        };
      }
    } catch (error) {
      console.debug('Geolocation language detection failed:', error);
    }

    // Fallback
    return {
      language: this.fallbackLanguage,
      confidence: 0.2,
      source: 'fallback'
    };
  }

  // Language Configuration Management
  async loadLanguageConfigs(): Promise<void> {
    try {
      const { data: configs, error } = await supabase
        .from('language_configs')
        .select('*')
        .eq('is_active', true);

      if (error) throw error;

      if (configs) {
        for (const config of configs) {
          this.languageConfigs.set(config.code, {
            code: config.code,
            name: config.name,
            nativeName: config.native_name,
            flag: config.flag,
            direction: config.direction || 'ltr',
            region: config.region,
            fallback: config.fallback,
            completeness: config.completeness || 0,
            isActive: config.is_active,
            lastUpdated: new Date(config.updated_at)
          });
        }
      }

      // Add default English config if not present
      if (!this.languageConfigs.has('en')) {
        this.languageConfigs.set('en', {
          code: 'en',
          name: 'English',
          nativeName: 'English',
          flag: '🇺🇸',
          direction: 'ltr',
          completeness: 100,
          isActive: true,
          lastUpdated: new Date()
        });
      }
    } catch (error) {
      console.error('Failed to load language configs:', error);
      // Use default configuration
      this.loadDefaultLanguageConfigs();
    }
  }

  private loadDefaultLanguageConfigs(): void {
    const defaultConfigs: LanguageConfig[] = [
      {
        code: 'en',
        name: 'English',
        nativeName: 'English',
        flag: '🇺🇸',
        direction: 'ltr',
        completeness: 100,
        isActive: true,
        lastUpdated: new Date()
      },
      {
        code: 'es',
        name: 'Spanish',
        nativeName: 'Español',
        flag: '🇪🇸',
        direction: 'ltr',
        completeness: 85,
        isActive: true,
        lastUpdated: new Date()
      },
      {
        code: 'ar',
        name: 'Arabic',
        nativeName: 'العربية',
        flag: '🇸🇦',
        direction: 'rtl',
        completeness: 70,
        isActive: true,
        lastUpdated: new Date()
      },
      {
        code: 'zh',
        name: 'Chinese',
        nativeName: '中文',
        flag: '🇨🇳',
        direction: 'ltr',
        completeness: 90,
        isActive: true,
        lastUpdated: new Date()
      }
    ];

    for (const config of defaultConfigs) {
      this.languageConfigs.set(config.code, config);
    }
  }

  // Translation Loading and Management
  async setLanguage(languageCode: string): Promise<void> {
    const normalizedCode = this.normalizeLanguageCode(languageCode);
    
    if (!this.isLanguageSupported(normalizedCode)) {
      console.warn(`Language ${normalizedCode} not supported, falling back to ${this.fallbackLanguage}`);
      await this.setLanguage(this.fallbackLanguage);
      return;
    }

    if (this.currentLanguage === normalizedCode) {
      return; // Already set
    }

    // Load translations if not already loaded
    if (!this.translations.has(normalizedCode)) {
      await this.loadTranslations(normalizedCode);
    }

    this.currentLanguage = normalizedCode;
    this.updateDocumentDirection();
    this.notifyObservers();

    // Save user preference
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('profiles')
          .update({ preferred_language: normalizedCode })
          .eq('id', user.id);
      }
    } catch (error) {
      console.debug('Could not save language preference:', error);
    }
  }

  async loadTranslations(languageCode: string): Promise<void> {
    const normalizedCode = this.normalizeLanguageCode(languageCode);

    // Check if already loading
    if (this.loadingPromises.has(normalizedCode)) {
      return this.loadingPromises.get(normalizedCode);
    }

    const loadingPromise = this.performTranslationLoad(normalizedCode);
    this.loadingPromises.set(normalizedCode, loadingPromise);

    try {
      await loadingPromise;
    } finally {
      this.loadingPromises.delete(normalizedCode);
    }
  }

  private async performTranslationLoad(languageCode: string): Promise<void> {
    try {
      // First try to load from database
      const { data: dbTranslations, error } = await supabase
        .from('translations')
        .select('key, value, context, interpolations, pluralization')
        .eq('language_code', languageCode);

      if (error) throw error;

      const translationMap = new Map<string, TranslationKey>();

      if (dbTranslations && dbTranslations.length > 0) {
        for (const translation of dbTranslations) {
          translationMap.set(translation.key, {
            key: translation.key,
            value: translation.value,
            context: translation.context,
            interpolations: translation.interpolations,
            pluralization: translation.pluralization
          });
        }
      } else {
        // Fallback to static files
        await this.loadTranslationsFromFile(languageCode, translationMap);
      }

      this.translations.set(languageCode, translationMap);
    } catch (error) {
      console.error(`Failed to load translations for ${languageCode}:`, error);
      
      // Load from fallback language if this isn't the fallback
      if (languageCode !== this.fallbackLanguage) {
        await this.loadTranslations(this.fallbackLanguage);
      }
    }
  }

  private async loadTranslationsFromFile(
    languageCode: string, 
    translationMap: Map<string, TranslationKey>
  ): Promise<void> {
    try {
      // Dynamic import of translation files
      const module = await import(`@/i18n/locales/${languageCode}.json`);
      const translations = module.default || module;
      
      this.flattenTranslations(translations, '', translationMap);
    } catch (error) {
      console.warn(`Could not load translation file for ${languageCode}:`, error);
    }
  }

  private flattenTranslations(
    obj: any, 
    prefix: string, 
    map: Map<string, TranslationKey>
  ): void {
    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        if ('value' in value || 'other' in value) {
          // Handle pluralization or complex translation object
          map.set(fullKey, {
            key: fullKey,
            value: value.value || value.other || String(value),
            context: value.context,
            interpolations: value.interpolations,
            pluralization: value.zero || value.one ? value : undefined
          });
        } else {
          // Recursively flatten nested objects
          this.flattenTranslations(value, fullKey, map);
        }
      } else {
        map.set(fullKey, {
          key: fullKey,
          value: String(value)
        });
      }
    }
  }

  // Translation Functions
  translate(key: string, interpolations?: Record<string, string | number>, count?: number): string {
    const cacheKey = `${this.currentLanguage}:${key}:${JSON.stringify(interpolations || {})}:${count || 0}`;
    
    if (this.translationCache.has(cacheKey)) {
      return this.translationCache.get(cacheKey)!;
    }

    let result = this.getTranslation(key, this.currentLanguage, interpolations, count);
    
    // Fallback to fallback language if translation not found
    if (!result && this.currentLanguage !== this.fallbackLanguage) {
      result = this.getTranslation(key, this.fallbackLanguage, interpolations, count);
    }
    
    // Final fallback to key itself
    if (!result) {
      result = key;
      console.warn(`Translation missing for key: ${key} in language: ${this.currentLanguage}`);
    }

    this.translationCache.set(cacheKey, result);
    return result;
  }

  private getTranslation(
    key: string, 
    languageCode: string, 
    interpolations?: Record<string, string | number>,
    count?: number
  ): string | null {
    const languageTranslations = this.translations.get(languageCode);
    if (!languageTranslations) return null;

    const translation = languageTranslations.get(key);
    if (!translation) return null;

    let result = translation.value;

    // Handle pluralization
    if (count !== undefined && translation.pluralization) {
      result = this.getPluralizedTranslation(translation.pluralization, count, languageCode);
    }

    // Handle interpolations
    if (interpolations) {
      result = this.interpolateTranslation(result, interpolations);
    }

    return result;
  }

  private getPluralizedTranslation(
    pluralization: NonNullable<TranslationKey['pluralization']>,
    count: number,
    languageCode: string
  ): string {
    const pluralRule = this.getPluralRule(languageCode);
    const category = pluralRule.select(count);
    
    switch (category) {
      case 'zero': return pluralization.zero || pluralization.other;
      case 'one': return pluralization.one || pluralization.other;
      case 'two': return pluralization.two || pluralization.other;
      case 'few': return pluralization.few || pluralization.other;
      case 'many': return pluralization.many || pluralization.other;
      default: return pluralization.other;
    }
  }

  private interpolateTranslation(text: string, interpolations: Record<string, string | number>): string {
    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      const value = interpolations[key];
      return value !== undefined ? String(value) : match;
    });
  }

  // Utility Methods
  getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  getCurrentLanguageConfig(): LanguageConfig | null {
    return this.languageConfigs.get(this.currentLanguage) || null;
  }

  getAvailableLanguages(): LanguageConfig[] {
    return Array.from(this.languageConfigs.values()).filter(config => config.isActive);
  }

  isRTL(languageCode?: string): boolean {
    const code = languageCode || this.currentLanguage;
    const config = this.languageConfigs.get(code);
    return config?.direction === 'rtl' || this.rtlLanguages.has(code);
  }

  private isLanguageSupported(languageCode: string): boolean {
    return this.languageConfigs.has(this.normalizeLanguageCode(languageCode));
  }

  private normalizeLanguageCode(languageCode: string): string {
    return languageCode.toLowerCase().split('-')[0];
  }

  private updateDocumentDirection(): void {
    const direction = this.isRTL() ? 'rtl' : 'ltr';
    document.documentElement.dir = direction;
    document.documentElement.lang = this.currentLanguage;
  }

  private setupPluralRules(): void {
    for (const config of this.languageConfigs.values()) {
      try {
        this.pluralRules.set(config.code, new Intl.PluralRules(config.code));
      } catch (error) {
        console.warn(`Could not create plural rules for ${config.code}:`, error);
        this.pluralRules.set(config.code, new Intl.PluralRules('en'));
      }
    }
  }

  private getPluralRule(languageCode: string): Intl.PluralRules {
    return this.pluralRules.get(languageCode) || this.pluralRules.get('en')!;
  }

  private async detectLanguageFromGeolocation(): Promise<string | null> {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve(null);
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            // Use a geolocation service to determine country/language
            const response = await fetch(
              `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${position.coords.latitude}&longitude=${position.coords.longitude}&localityLanguage=en`
            );
            const data = await response.json();
            
            // Map country codes to languages (simplified)
            const countryToLanguage: Record<string, string> = {
              'ES': 'es',
              'SA': 'ar',
              'CN': 'zh',
              'FR': 'fr',
              'DE': 'de',
              'IT': 'it',
              'PT': 'pt',
              'RU': 'ru',
              'JP': 'ja',
              'KR': 'ko'
            };

            const language = countryToLanguage[data.countryCode] || null;
            resolve(language);
          } catch (error) {
            resolve(null);
          }
        },
        () => resolve(null),
        { timeout: 5000 }
      );
    });
  }

  // Translation Management
  async getTranslationStats(languageCode: string): Promise<TranslationStats> {
    const referenceTranslations = this.translations.get(this.fallbackLanguage) || new Map();
    const targetTranslations = this.translations.get(languageCode) || new Map();

    const totalKeys = referenceTranslations.size;
    const translatedKeys = Array.from(referenceTranslations.keys()).filter(
      key => targetTranslations.has(key)
    ).length;

    const missingKeys = Array.from(referenceTranslations.keys()).filter(
      key => !targetTranslations.has(key)
    );

    const completeness = totalKeys > 0 ? (translatedKeys / totalKeys) * 100 : 0;

    return {
      totalKeys,
      translatedKeys,
      missingKeys,
      outdatedKeys: [], // Would implement version checking
      completeness,
      lastSyncTime: new Date()
    };
  }

  async updateTranslation(
    languageCode: string,
    key: string,
    value: string,
    context?: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('translations')
        .upsert({
          language_code: languageCode,
          key,
          value,
          context,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      // Update local cache
      const languageTranslations = this.translations.get(languageCode) || new Map();
      languageTranslations.set(key, { key, value, context });
      this.translations.set(languageCode, languageTranslations);

      // Clear translation cache for this key
      this.clearTranslationCache(key);
    } catch (error) {
      console.error('Failed to update translation:', error);
      throw error;
    }
  }

  private clearTranslationCache(key?: string): void {
    if (key) {
      // Clear specific key from cache
      for (const cacheKey of this.translationCache.keys()) {
        if (cacheKey.includes(`:${key}:`)) {
          this.translationCache.delete(cacheKey);
        }
      }
    } else {
      // Clear entire cache
      this.translationCache.clear();
    }
  }

  // Observer Pattern
  subscribe(callback: (language: string) => void): () => void {
    this.observers.add(callback);
    return () => this.observers.delete(callback);
  }

  private notifyObservers(): void {
    this.observers.forEach(callback => {
      try {
        callback(this.currentLanguage);
      } catch (error) {
        console.error('Error in language change observer:', error);
      }
    });
  }

  // Cleanup
  destroy(): void {
    this.observers.clear();
    this.translationCache.clear();
    this.loadingPromises.clear();
  }
}

// Export singleton instance
export const languageManager = new LanguageManager();

// Convenience functions
export const t = (key: string, interpolations?: Record<string, string | number>, count?: number): string => {
  return languageManager.translate(key, interpolations, count);
};

export const getCurrentLanguage = (): string => languageManager.getCurrentLanguage();
export const setLanguage = (languageCode: string): Promise<void> => languageManager.setLanguage(languageCode);
export const getAvailableLanguages = (): LanguageConfig[] => languageManager.getAvailableLanguages();
export const isRTL = (languageCode?: string): boolean => languageManager.isRTL(languageCode);