/* eslint-disable @typescript-eslint/no-explicit-any */
// Biolinks Service
// Manages biolinks pages (minimal API used by SetLinksPanel)

import { supabase } from '@/integrations/supabase/client';

type JsonObject = Record<string, unknown>;

interface APIResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: JsonObject;
  };
}

interface BioPageMin {
  id: string;
  user_id: string;
  username: string;
  display_name: string;
  theme?: JsonObject;
  is_public: boolean;
  is_verified: boolean;
  page_views: number;
  total_clicks: number;
  created_at: string;
  updated_at?: string;
}

class BioLinksError extends Error {
  code: string;
  id?: string;
  details?: JsonObject;
  constructor(message: string, code: string, id?: string, details?: JsonObject) {
    super(message);
    this.name = 'BioLinksError';
    this.code = code;
    this.id = id;
    this.details = details;
  }
}

class PageNotFoundError extends BioLinksError {
  constructor(username: string) {
    super(`Page not found: ${username}`, 'PAGE_NOT_FOUND', undefined, { username });
    this.name = 'PageNotFoundError';
  }
}

const sb = supabase as any;

export class BioLinksService {
  private listeners: Map<string, Set<(event: JsonObject) => void>> = new Map();

  async createPage(
    userId: string,
    username: string,
    displayName: string,
  theme?: JsonObject
  ): Promise<APIResponse<BioPageMin>> {
    try {
      const { data: existingPage } = await sb
        .from('bio_pages')
        .select('id')
        .eq('username', username.toLowerCase())
        .single();

      if (existingPage) {
        return { success: false, error: { code: 'USERNAME_TAKEN', message: 'Username is already taken' } };
      }

      const pageData = {
        user_id: userId,
        username: username.toLowerCase(),
        display_name: displayName,
        theme: theme || this.getDefaultTheme(),
        is_public: true,
        is_verified: false,
        page_views: 0,
        total_clicks: 0,
        created_at: new Date().toISOString()
      };

      const { data, error } = await sb
        .from('bio_pages')
        .insert(pageData)
        .select()
        .single();

      if (error) throw new BioLinksError('Failed to create page', 'PAGE_CREATE_ERROR', undefined, { error });

      this.emitEvent({ type: 'page_created', page_id: data.id, user_id: userId, timestamp: new Date().toISOString() });
      return { success: true, data: data as BioPageMin };
    } catch (error) {
      return { success: false, error: { code: 'PAGE_CREATE_ERROR', message: error instanceof Error ? error.message : 'Failed to create page' } };
    }
  }

  async getPage(username: string, includeLinks = true, includeAnalytics = false): Promise<APIResponse<BioPageMin> & { links?: unknown[]; analytics?: unknown }> {
    try {
      const { data: page, error } = await sb
        .from('bio_pages')
        .select('*')
        .eq('username', username.toLowerCase())
        .eq('is_public', true)
        .single();

      if (error || !page) throw new PageNotFoundError(username);

      let links;
      let analytics;

      if (includeLinks) {
        const { data: linksData } = await sb
          .from('bio_links')
          .select('*')
          .eq('user_id', page.user_id)
          .eq('is_active', true)
          .order('position', { ascending: true });
        links = linksData || [];
      }

      if (includeAnalytics) {
        const { data: analyticsData } = await sb
          .from('page_analytics')
          .select('*')
          .eq('page_id', page.id)
          .eq('date', new Date().toISOString().split('T')[0])
          .single();
        analytics = analyticsData || undefined;
      }

      await sb.rpc('increment_page_views', { page_id: page.id });
      this.emitEvent({ type: 'page_viewed', page_id: page.id, user_id: page.user_id, timestamp: new Date().toISOString() });

      return { success: true, data: page as BioPageMin, links, analytics };
    } catch (error) {
      return { success: false, error: { code: 'PAGE_FETCH_ERROR', message: error instanceof Error ? error.message : 'Failed to fetch page' } };
    }
  }

  private getDefaultTheme(): JsonObject {
    return {
      name: 'default',
      background_type: 'gradient',
      background_value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      text_color: '#ffffff',
      link_style: 'rounded',
      button_style: 'solid',
      font_family: 'inter',
      animations_enabled: true
    };
  }

  addEventListener(eventType: string, callback: (event: JsonObject) => void): void {
    if (!this.listeners.has(eventType)) this.listeners.set(eventType, new Set());
    this.listeners.get(eventType)!.add(callback);
  }

  private emitEvent(event: JsonObject & { type: string }): void {
    this.listeners.get(event.type)?.forEach(cb => cb(event));
    this.listeners.get('all')?.forEach(cb => cb(event));
  }
}

export const bioLinksService = new BioLinksService();
export const createBioLinksService = (): BioLinksService => new BioLinksService();