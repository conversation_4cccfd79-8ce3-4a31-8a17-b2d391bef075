// Ready Player Me Integration Service
// Handles RPM avatar creation, customization, and export

import type { 
  RPMAvatar, 
  ReadyPlayerMeConfig, 
  AvatarCreationOptions,
  AvatarAPIResponse 
} from '@/types/avatar';

export class ReadyPlayerMeService {
  private subdomain: string;
  private baseUrl: string;

  constructor(subdomain = 'heey') {
    this.subdomain = subdomain;
    this.baseUrl = `https://${subdomain}.readyplayer.me`;
  }

  /**
   * Generate RPM avatar creation URL with configuration
   */
  generateAvatarURL(options: AvatarCreationOptions = {}): string {
    const params = new URLSearchParams();
    
    // Core parameters
    params.set('frameApi', 'true');
    params.set('clearCache', 'true');
    params.set('source', 'web');
    
    // Body type configuration
    if (options.bodyType) {
      params.set('bodyType', options.bodyType);
    } else {
      params.set('bodyType', 'halfbody'); // Default
    }
    
    // Gender selection
    if (options.gender) {
      params.set('gender', options.gender);
    }
    
    // Style configuration
    if (options.style) {
      params.set('style', options.style);
    }
    
    // Template selection
    if (options.template) {
      params.set('template', options.template);
    }
    
    // Quick start mode
    params.set('quickStart', 'true');
    
    // Customization options
    if (options.customizations) {
      Object.entries(options.customizations).forEach(([key, value]) => {
        if (typeof value === 'boolean' && value) {
          params.set(key, 'true');
        } else if (typeof value === 'string') {
          params.set(key, value);
        }
      });
    }

    return `${this.baseUrl}/avatar?${params.toString()}`;
  }

  /**
   * Generate configuration for RPM iframe
   */
  getIframeConfig(options: AvatarCreationOptions = {}): ReadyPlayerMeConfig {
    return {
      subdomain: this.subdomain,
      quickStart: true,
      clearCache: true,
      bodyType: options.bodyType || 'halfbody',
      language: 'en',
      avatarTemplate: options.template,
      customizations: {
        hair: true,
        beard: true,
        eyes: true,
        eyebrows: true,
        facewear: true,
        headwear: true,
        clothing: true,
        ...options.customizations
      }
    };
  }

  /**
   * Process avatar export from RPM
   */
  async processAvatarExport(avatarUrl: string): Promise<AvatarAPIResponse<RPMAvatar>> {
    try {
      // Validate RPM URL
      if (!this.isValidRPMUrl(avatarUrl)) {
        throw new Error('Invalid Ready Player Me avatar URL');
      }

      // Extract avatar data from URL
      const avatarData = this.extractAvatarData(avatarUrl);
      
      // Generate thumbnail URL
      const thumbnailUrl = this.generateThumbnailUrl(avatarUrl);
      
      // Fetch avatar metadata (if available)
      const metadata = await this.fetchAvatarMetadata(avatarData.id);

      const rpmAvatar: RPMAvatar = {
        id: avatarData.id,
        partner: this.subdomain,
        modelUrl: avatarUrl,
        thumbnailUrl,
        updatedAt: new Date().toISOString(),
        metadata
      };

      return {
        success: true,
        data: rpmAvatar
      };
    } catch (error) {
      console.error('Error processing RPM avatar export:', error);
      return {
        success: false,
        error: {
          code: 'RPM_EXPORT_ERROR',
          message: error instanceof Error ? error.message : 'Failed to process avatar export'
        }
      };
    }
  }

  /**
   * Validate Ready Player Me avatar URL
   */
  isValidRPMUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      
      // Check domain
      if (!parsedUrl.hostname.includes('readyplayer.me') && 
          !parsedUrl.hostname.includes('models.readyplayer.me')) {
        return false;
      }
      
      // Check file extension
      if (!parsedUrl.pathname.endsWith('.glb')) {
        return false;
      }
      
      // Check for avatar ID pattern
      const pathParts = parsedUrl.pathname.split('/');
      const filename = pathParts[pathParts.length - 1];
      const avatarId = filename.replace('.glb', '');
      
      // RPM avatar IDs are typically long alphanumeric strings
      return avatarId.length >= 20 && /^[a-zA-Z0-9-_]+$/.test(avatarId);
    } catch {
      return false;
    }
  }

  /**
   * Extract avatar data from RPM URL
   */
  private extractAvatarData(url: string): { id: string; type: string } {
    const parsedUrl = new URL(url);
    const pathParts = parsedUrl.pathname.split('/');
    const filename = pathParts[pathParts.length - 1];
    const avatarId = filename.replace('.glb', '');
    
    // Determine avatar type based on URL structure
    let type = 'standard';
    if (url.includes('fullbody')) {
      type = 'fullbody';
    } else if (url.includes('halfbody')) {
      type = 'halfbody';
    }

    return { id: avatarId, type };
  }

  /**
   * Generate thumbnail URL from avatar URL
   */
  private generateThumbnailUrl(avatarUrl: string): string {
    // RPM provides thumbnail images by replacing .glb with .png
    return avatarUrl.replace('.glb', '.png');
  }

  /**
   * Fetch avatar metadata from RPM API (if available)
   */
  private async fetchAvatarMetadata(avatarId: string): Promise<Record<string, unknown> | undefined> {
    try {
      // Note: This is a placeholder for RPM API integration
      // The actual implementation would depend on RPM's API availability
      
      // For now, return basic metadata structure
      return {
        source: 'ready_player_me',
        id: avatarId,
        created_at: new Date().toISOString(),
        version: '1.0'
      };
    } catch (error) {
      console.warn('Failed to fetch avatar metadata:', error);
      return undefined;
    }
  }

  /**
   * Handle RPM iframe events
   */
  setupEventListeners(
    iframe: HTMLIFrameElement,
    callbacks: {
      onAvatarExported?: (avatarUrl: string) => void;
      onUserSet?: (userId: string) => void;
      onAvatarDraft?: (data: unknown) => void;
      onError?: (error: string) => void;
    }
  ): () => void {
    const handleMessage = (event: MessageEvent) => {
      // Verify origin for security
      if (!event.origin.includes('readyplayer.me')) {
        return;
      }

      try {
        const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
        
        switch (data.eventName) {
          case 'v1.frame.ready':
            console.log('RPM iframe is ready');
            break;
            
          case 'v1.avatar.exported':
            if (data.url && callbacks.onAvatarExported) {
              callbacks.onAvatarExported(data.url);
            }
            break;
            
          case 'v1.user.set':
            if (data.id && callbacks.onUserSet) {
              callbacks.onUserSet(data.id);
            }
            break;
            
          case 'v1.avatar.draft':
            if (callbacks.onAvatarDraft) {
              callbacks.onAvatarDraft(data);
            }
            break;
            
          default:
            console.log('Unhandled RPM event:', data.eventName);
        }
      } catch (error) {
        console.error('Error handling RPM message:', error);
        if (callbacks.onError) {
          callbacks.onError('Failed to process RPM event');
        }
      }
    };

    window.addEventListener('message', handleMessage);
    
    // Return cleanup function
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }

  /**
   * Get avatar customization options
   */
  getCustomizationOptions(): Record<string, unknown> {
    return {
      bodyType: {
        options: ['halfbody', 'fullbody'],
        default: 'halfbody',
        description: 'Avatar body type'
      },
      gender: {
        options: ['male', 'female'],
        description: 'Avatar gender'
      },
      style: {
        options: ['realistic', 'cartoon', 'anime'],
        default: 'realistic',
        description: 'Avatar visual style'
      },
      customizations: {
        hair: { enabled: true, description: 'Hair customization' },
        beard: { enabled: true, description: 'Facial hair customization' },
        eyes: { enabled: true, description: 'Eye customization' },
        eyebrows: { enabled: true, description: 'Eyebrow customization' },
        facewear: { enabled: true, description: 'Glasses and face accessories' },
        headwear: { enabled: true, description: 'Hats and head accessories' },
        clothing: { enabled: true, description: 'Clothing customization' }
      }
    };
  }

  /**
   * Generate avatar preview URL
   */
  generatePreviewUrl(avatarUrl: string, options: { 
    background?: string; 
    pose?: string; 
    size?: 'sm' | 'md' | 'lg' 
  } = {}): string {
    const baseUrl = avatarUrl.replace('.glb', '.png');
    const params = new URLSearchParams();
    
    if (options.background) {
      params.set('background', options.background);
    }
    
    if (options.pose) {
      params.set('pose', options.pose);
    }
    
    if (options.size) {
      const sizes = { sm: '256', md: '512', lg: '1024' };
      params.set('w', sizes[options.size]);
      params.set('h', sizes[options.size]);
    }
    
    return params.toString() ? `${baseUrl}?${params.toString()}` : baseUrl;
  }

  /**
   * Check avatar compatibility
   */
  checkCompatibility(avatarUrl: string): {
    isCompatible: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      const url = new URL(avatarUrl);
      
      // Check if it's a valid RPM URL
      if (!this.isValidRPMUrl(avatarUrl)) {
        issues.push('URL is not a valid Ready Player Me avatar URL');
      }
      
      // Check for HTTPS
      if (url.protocol !== 'https:') {
        issues.push('Avatar URL should use HTTPS for security');
      }
      
      // Check file extension
      if (!url.pathname.endsWith('.glb')) {
        issues.push('Avatar file should be in GLB format');
      }
      
      // Recommendations
      recommendations.push('Ensure avatar is optimized for web use');
      recommendations.push('Test avatar loading performance on mobile devices');
      
    } catch {
      issues.push('Invalid avatar URL format');
    }

    return {
      isCompatible: issues.length === 0,
      issues,
      recommendations
    };
  }
}

// Export singleton instance
export const rpmService = new ReadyPlayerMeService();

// Export utility functions
export function createRPMIframe(
  container: HTMLElement,
  options: AvatarCreationOptions = {}
): HTMLIFrameElement {
  const iframe = document.createElement('iframe');
  iframe.src = rpmService.generateAvatarURL(options);
  iframe.style.width = '100%';
  iframe.style.height = '100%';
  iframe.style.border = 'none';
  iframe.allow = 'camera; microphone; clipboard-write';
  
  container.appendChild(iframe);
  
  return iframe;
}

export function extractRPMAvatarId(url: string): string | null {
  try {
    const parsedUrl = new URL(url);
    const pathParts = parsedUrl.pathname.split('/');
    const filename = pathParts[pathParts.length - 1];
    return filename.replace('.glb', '');
  } catch {
    return null;
  }
}