// Referral Service
// Manages referral program, tracking, rewards, and analytics

import { supabase } from '@/integrations/supabase/client';
import type {
  Referral,
  ReferralProgram,
  ReferralReward,
  ReferralCommission,
  ReferralLink,
  ReferralTracking,
  ReferralAnalytics,
  ReferralLeaderboard,
  ReferralNotification,
  ReferralPayout,
  ReferralAPIResponse,
  ReferralDashboardResponse,
  ReferralEvent,
  ReferralEventType,
  ReferralStatus,
  RewardType,
  TriggerEvent,
  PayoutMethod,
  UTMParams,
  DeviceInfo,
  LocationInfo
} from '@/types/referral';
import { ReferralError, ReferralCodeError, FraudDetectionError } from '@/types/referral';

export class ReferralService {
  private eventListeners: Map<string, Set<(event: ReferralEvent) => void>> = new Map();
  private fraudDetectionThreshold = 0.7;
  private maxSameIpReferrals = 5;
  private minTimeBetweenReferrals = 24 * 60 * 60 * 1000; // 24 hours

  // Referral Management
  async createReferral(
    referrerId: string,
    email?: string,
    metadata?: Record<string, unknown>
  ): Promise<ReferralAPIResponse<Referral>> {
    try {
      // Generate unique referral code
      const referralCode = await this.generateUniqueReferralCode(referrerId);

      const referralData = {
        referrer_id: referrerId,
        referral_code: referralCode,
        status: 'pending' as ReferralStatus,
        email,
        reward_earned: 0,
        commission_earned: 0,
        tier_level: 1,
        metadata,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('referrals')
        .insert(referralData)
        .select()
        .single();

      if (error) {
        throw new ReferralError('Failed to create referral', 'REFERRAL_CREATE_ERROR', undefined, { error });
      }

      // Create referral link
      await this.createReferralLink(referrerId, referralCode);

      // Emit referral created event
      this.emitEvent({
        type: 'referral_created',
        referral_id: data.id,
        user_id: referrerId,
        data: { referral_code: referralCode },
        timestamp: new Date().toISOString()
      });

      return { success: true, data };
    } catch (error) {
      console.error('Referral creation error:', error);
      return {
        success: false,
        error: {
          code: error instanceof ReferralError ? error.code : 'REFERRAL_CREATE_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create referral'
        }
      };
    }
  }

  async trackReferralClick(
    referralCode: string,
    visitorId: string,
    metadata: {
      ip_address?: string;
      user_agent?: string;
      referrer_url?: string;
      landing_page: string;
      utm_params?: UTMParams;
      device_info?: DeviceInfo;
      location_info?: LocationInfo;
    }
  ): Promise<ReferralAPIResponse<ReferralTracking>> {
    try {
      // Validate referral code
      const { data: referral } = await supabase
        .from('referrals')
        .select('*')
        .eq('referral_code', referralCode)
        .eq('status', 'pending')
        .single();

      if (!referral) {
        throw new ReferralCodeError('Invalid or expired referral code', referralCode);
      }

      // Check for fraud patterns
      const fraudScore = await this.detectFraud({
        referrer_id: referral.referrer_id,
        metadata: {
          ...metadata,
          visitor_id: visitorId
        }
      });

      if (fraudScore > this.fraudDetectionThreshold) {
        throw new FraudDetectionError(
          'Suspicious referral activity detected',
          fraudScore,
          ['high_fraud_score'],
          referral.id
        );
      }

      // Record tracking data
      const trackingData = {
        referral_code: referralCode,
        visitor_id: visitorId,
        ip_address: metadata.ip_address,
        user_agent: metadata.user_agent,
        referrer_url: metadata.referrer_url,
        landing_page: metadata.landing_page,
        utm_params: metadata.utm_params,
        device_info: metadata.device_info,
        location_info: metadata.location_info,
        clicked_at: new Date().toISOString()
      };

      const { data: tracking, error } = await supabase
        .from('referral_tracking')
        .insert(trackingData)
        .select()
        .single();

      if (error) {
        throw new ReferralError('Failed to track referral click', 'TRACKING_ERROR', referral.id, { error });
      }

      // Update referral link click count
      await supabase
        .from('referral_links')
        .update({
          click_count: supabase.raw('click_count + 1'),
          last_clicked_at: new Date().toISOString()
        })
        .eq('referral_code', referralCode);

      // Emit referral clicked event
      this.emitEvent({
        type: 'referral_clicked',
        referral_id: referral.id,
        user_id: referral.referrer_id,
        data: { visitor_id: visitorId, landing_page: metadata.landing_page },
        timestamp: new Date().toISOString()
      });

      return { success: true, data: tracking };
    } catch (error) {
      return {
        success: false,
        error: {
          code: error instanceof ReferralError ? error.code : 'TRACKING_ERROR',
          message: error instanceof Error ? error.message : 'Failed to track referral click'
        }
      };
    }
  }

  async processReferralConversion(
    referralCode: string,
    newUserId: string,
    triggerEvent: TriggerEvent = 'signup',
    conversionValue?: number
  ): Promise<ReferralAPIResponse<{ referral: Referral; reward: ReferralReward }>> {
    try {
      // Get referral
      const { data: referral, error: referralError } = await supabase
        .from('referrals')
        .select('*')
        .eq('referral_code', referralCode)
        .single();

      if (referralError || !referral) {
        throw new ReferralCodeError('Referral not found', referralCode);
      }

      // Prevent self-referral
      if (referral.referrer_id === newUserId) {
        throw new ReferralError('Self-referral not allowed', 'SELF_REFERRAL_ERROR', referral.id);
      }

      // Check if already converted
      if (referral.status === 'converted') {
        throw new ReferralError('Referral already converted', 'ALREADY_CONVERTED', referral.id);
      }

      // Get current program configuration
      const program = await this.getCurrentProgram();
      if (!program) {
        throw new ReferralError('No active referral program', 'NO_ACTIVE_PROGRAM');
      }

      // Calculate reward
      const rewardAmount = this.calculateReward(triggerEvent, program.reward_structure, conversionValue);

      // Update referral status
      const { data: updatedReferral, error: updateError } = await supabase
        .from('referrals')
        .update({
          referred_user_id: newUserId,
          status: 'converted' as ReferralStatus,
          conversion_date: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', referral.id)
        .select()
        .single();

      if (updateError) {
        throw new ReferralError('Failed to update referral', 'UPDATE_ERROR', referral.id, { updateError });
      }

      // Create reward
      const reward = await this.createReward({
        referral_id: referral.id,
        referrer_id: referral.referrer_id,
        referred_user_id: newUserId,
        type: program.reward_structure.type,
        amount: rewardAmount,
        currency: program.reward_structure.currency || program.payout_currency,
        trigger_event: triggerEvent
      });

      // Update tracking record
      await supabase
        .from('referral_tracking')
        .update({
          converted_at: new Date().toISOString(),
          conversion_value: conversionValue
        })
        .eq('referral_code', referralCode)
        .eq('visitor_id', newUserId);

      // Update referral link conversion count
      await supabase
        .from('referral_links')
        .update({
          conversion_count: supabase.raw('conversion_count + 1')
        })
        .eq('referral_code', referralCode);

      // Check for tier upgrade
      await this.checkTierUpgrade(referral.referrer_id);

      // Send notification
      await this.sendNotification(referral.referrer_id, {
        type: 'referral_conversion',
        title: 'Referral Converted!',
        message: `Your referral has signed up and you've earned a reward!`,
        data: { reward_amount: rewardAmount, currency: program.payout_currency }
      });

      // Emit conversion event
      this.emitEvent({
        type: 'referral_converted',
        referral_id: referral.id,
        user_id: referral.referrer_id,
        data: { 
          referred_user_id: newUserId, 
          trigger_event: triggerEvent,
          reward_amount: rewardAmount 
        },
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        data: {
          referral: updatedReferral,
          reward: reward.data!
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: error instanceof ReferralError ? error.code : 'CONVERSION_ERROR',
          message: error instanceof Error ? error.message : 'Failed to process referral conversion'
        }
      };
    }
  }

  async getUserReferrals(
    userId: string,
    status?: ReferralStatus,
    limit = 50,
    offset = 0
  ): Promise<ReferralAPIResponse<Referral[]>> {
    try {
      let query = supabase
        .from('referrals')
        .select('*')
        .eq('referrer_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error, count } = await query;

      if (error) {
        throw new ReferralError('Failed to fetch referrals', 'FETCH_ERROR', undefined, { error });
      }

      return {
        success: true,
        data: data || [],
        pagination: {
          page: Math.floor(offset / limit) + 1,
          limit,
          total: count || 0,
          hasMore: (count || 0) > offset + limit
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch referrals'
        }
      };
    }
  }

  async getReferralDashboard(userId: string): Promise<ReferralDashboardResponse> {
    try {
      // Get summary stats
      const { data: referrals } = await supabase
        .from('referrals')
        .select('status, reward_earned, commission_earned, created_at')
        .eq('referrer_id', userId);

      const totalReferrals = referrals?.length || 0;
      const successfulReferrals = referrals?.filter(r => r.status === 'converted').length || 0;
      const pendingReferrals = referrals?.filter(r => r.status === 'pending').length || 0;
      const conversionRate = totalReferrals > 0 ? (successfulReferrals / totalReferrals) * 100 : 0;
      const totalEarnings = referrals?.reduce((sum, r) => sum + r.reward_earned + r.commission_earned, 0) || 0;

      // Get recent activity
      const { data: recentActivity } = await supabase
        .from('referral_notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      // Get current tier info
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('referral_tier_level')
        .eq('id', userId)
        .single();

      const currentTier = userProfile?.referral_tier_level || 1;

      // Get performance metrics for this month
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const { data: monthlyTracking } = await supabase
        .from('referral_tracking')
        .select('clicked_at, converted_at')
        .gte('clicked_at', startOfMonth.toISOString());

      const clicksThisMonth = monthlyTracking?.length || 0;
      const conversionsThisMonth = monthlyTracking?.filter(t => t.converted_at).length || 0;

      return {
        success: true,
        data: {
          summary: {
            total_referrals: totalReferrals,
            successful_referrals: successfulReferrals,
            pending_referrals: pendingReferrals,
            conversion_rate: conversionRate,
            total_earnings: totalEarnings,
            pending_earnings: 0, // Would calculate from pending rewards
            current_tier: currentTier,
            next_tier_progress: 0 // Would calculate based on tier requirements
          },
          recent_activity: (recentActivity || []).map(activity => ({
            id: activity.id,
            type: activity.type,
            description: activity.message,
            timestamp: activity.created_at
          })),
          performance_metrics: {
            clicks_this_month: clicksThisMonth,
            conversions_this_month: conversionsThisMonth,
            revenue_this_month: 0, // Would calculate from this month's conversions
            top_performing_link: {
              link_id: '',
              url: '',
              clicks: 0,
              conversions: 0,
              conversion_rate: 0,
              revenue_generated: 0
            },
            trending_sources: []
          },
          tier_info: {
            current_tier: {
              level: currentTier,
              name: `Tier ${currentTier}`,
              requirements: [],
              benefits: [],
            },
            progress_percentage: 0,
            requirements_status: []
          }
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DASHBOARD_ERROR',
          message: error instanceof Error ? error.message : 'Failed to load dashboard'
        }
      };
    }
  }

  // Reward Management
  async createReward(rewardData: {
    referral_id: string;
    referrer_id: string;
    referred_user_id: string;
    type: RewardType;
    amount: number;
    currency: string;
    trigger_event: TriggerEvent;
  }): Promise<ReferralAPIResponse<ReferralReward>> {
    try {
      const reward = {
        ...rewardData,
        status: 'pending' as const,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('referral_rewards')
        .insert(reward)
        .select()
        .single();

      if (error) {
        throw new ReferralError('Failed to create reward', 'REWARD_CREATE_ERROR', rewardData.referral_id, { error });
      }

      // Update referral earned amount
      await supabase
        .from('referrals')
        .update({
          reward_earned: supabase.raw(`reward_earned + ${rewardData.amount}`)
        })
        .eq('id', rewardData.referral_id);

      // Emit reward earned event
      this.emitEvent({
        type: 'reward_earned',
        referral_id: rewardData.referral_id,
        user_id: rewardData.referrer_id,
        data: { amount: rewardData.amount, type: rewardData.type },
        timestamp: new Date().toISOString()
      });

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'REWARD_CREATE_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create reward'
        }
      };
    }
  }

  async processPayouts(userId: string): Promise<ReferralAPIResponse<ReferralPayout[]>> {
    try {
      // Get pending rewards and commissions
      const { data: pendingRewards } = await supabase
        .from('referral_rewards')
        .select('*')
        .eq('referrer_id', userId)
        .eq('status', 'approved');

      const { data: pendingCommissions } = await supabase
        .from('referral_commissions')
        .select('*')
        .eq('referrer_id', userId)
        .eq('status', 'earned');

      const totalAmount = (pendingRewards || []).reduce((sum, r) => sum + r.amount, 0) +
                         (pendingCommissions || []).reduce((sum, c) => sum + c.amount, 0);

      // Get program configuration for minimum payout
      const program = await this.getCurrentProgram();
      const minPayoutAmount = program?.min_payout_amount || 25;

      if (totalAmount < minPayoutAmount) {
        throw new ReferralError(
          `Minimum payout amount is ${minPayoutAmount}`,
          'MINIMUM_PAYOUT_NOT_MET',
          undefined,
          { required: minPayoutAmount, current: totalAmount }
        );
      }

      // Create payout record
      const payoutData = {
        user_id: userId,
        amount: totalAmount,
        currency: program?.payout_currency || 'USD',
        method: 'paypal' as PayoutMethod, // Default method
        status: 'pending' as const,
        net_amount: totalAmount, // Would subtract fees
        retry_count: 0,
        created_at: new Date().toISOString()
      };

      const { data: payout, error } = await supabase
        .from('referral_payouts')
        .insert(payoutData)
        .select()
        .single();

      if (error) {
        throw new ReferralError('Failed to create payout', 'PAYOUT_CREATE_ERROR', undefined, { error });
      }

      // Mark rewards and commissions as paid
      if (pendingRewards && pendingRewards.length > 0) {
        await supabase
          .from('referral_rewards')
          .update({
            status: 'paid',
            paid_out_at: new Date().toISOString(),
            payout_reference: payout.id
          })
          .in('id', pendingRewards.map(r => r.id));
      }

      if (pendingCommissions && pendingCommissions.length > 0) {
        await supabase
          .from('referral_commissions')
          .update({
            status: 'paid',
            paid_out_at: new Date().toISOString(),
            payout_reference: payout.id
          })
          .in('id', pendingCommissions.map(c => c.id));
      }

      // Send notification
      await this.sendNotification(userId, {
        type: 'payout_processed',
        title: 'Payout Processed',
        message: `Your payout of ${totalAmount} ${payoutData.currency} is being processed.`,
        data: { amount: totalAmount, currency: payoutData.currency }
      });

      return { success: true, data: [payout] };
    } catch (error) {
      return {
        success: false,
        error: {
          code: error instanceof ReferralError ? error.code : 'PAYOUT_ERROR',
          message: error instanceof Error ? error.message : 'Failed to process payout'
        }
      };
    }
  }

  // Analytics
  async getReferralAnalytics(
    userId: string,
    periodStart: string,
    periodEnd: string
  ): Promise<ReferralAPIResponse<ReferralAnalytics>> {
    try {
      // This would typically aggregate data from multiple tables
      // For now, return a basic structure
      const analyticsData: ReferralAnalytics = {
        id: `analytics_${userId}_${Date.now()}`,
        user_id: userId,
        period_start: periodStart,
        period_end: periodEnd,
        total_referrals: 0,
        successful_referrals: 0,
        pending_referrals: 0,
        conversion_rate: 0,
        total_rewards_earned: 0,
        total_commissions_earned: 0,
        total_revenue_generated: 0,
        top_performing_links: [],
        traffic_sources: [],
        geographic_distribution: [],
        device_breakdown: [],
        tier_progression: {
          current_tier: 1,
          progress_to_next: 0,
          requirements_met: [],
          requirements_pending: []
        },
        created_at: new Date().toISOString()
      };

      return { success: true, data: analyticsData };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ANALYTICS_ERROR',
          message: error instanceof Error ? error.message : 'Failed to fetch analytics'
        }
      };
    }
  }

  // Utility Methods
  private async generateUniqueReferralCode(userId: string): Promise<string> {
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      const code = this.generateReferralCode(userId);
      
      const { data: existing } = await supabase
        .from('referrals')
        .select('id')
        .eq('referral_code', code)
        .single();

      if (!existing) {
        return code;
      }
      
      attempts++;
    }

    throw new ReferralCodeError('Failed to generate unique referral code');
  }

  private generateReferralCode(userId: string): string {
    // Generate a code based on user ID and random elements
    const userPart = userId.slice(-4).toUpperCase();
    const randomPart = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${userPart}${randomPart}`;
  }

  private async createReferralLink(userId: string, referralCode: string): Promise<void> {
    const linkData = {
      user_id: userId,
      referral_code: referralCode,
      url: `${window.location.origin}?ref=${referralCode}`,
      is_active: true,
      click_count: 0,
      conversion_count: 0,
      created_at: new Date().toISOString()
    };

    await supabase
      .from('referral_links')
      .insert(linkData);
  }

  private async getCurrentProgram(): Promise<ReferralProgram | null> {
    const { data } = await supabase
      .from('referral_programs')
      .select('*')
      .eq('is_active', true)
      .single();

    return data;
  }

  private calculateReward(
    triggerEvent: TriggerEvent,
    rewardStructure: any,
    conversionValue?: number
  ): number {
    if (rewardStructure.type === 'cash' && rewardStructure.amount) {
      return rewardStructure.amount;
    }
    
    if (rewardStructure.type === 'percentage' && rewardStructure.percentage && conversionValue) {
      return (conversionValue * rewardStructure.percentage) / 100;
    }

    // Default reward
    return 10; // $10 default
  }

  private async detectFraud(referral: Partial<Referral>): Promise<number> {
    let fraudScore = 0;

    // Check for same IP referrals
    if (referral.metadata?.ip_address) {
      const { data: sameIpReferrals } = await supabase
        .from('referral_tracking')
        .select('id')
        .eq('ip_address', referral.metadata.ip_address)
        .gte('clicked_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      if (sameIpReferrals && sameIpReferrals.length >= this.maxSameIpReferrals) {
        fraudScore += 0.4;
      }
    }

    // Check for rapid referrals from same user
    if (referral.referrer_id) {
      const { data: recentReferrals } = await supabase
        .from('referrals')
        .select('created_at')
        .eq('referrer_id', referral.referrer_id)
        .gte('created_at', new Date(Date.now() - this.minTimeBetweenReferrals).toISOString());

      if (recentReferrals && recentReferrals.length > 3) {
        fraudScore += 0.3;
      }
    }

    return Math.min(fraudScore, 1.0);
  }

  private async checkTierUpgrade(userId: string): Promise<void> {
    // Get user's current tier and referral stats
    const { data: profile } = await supabase
      .from('profiles')
      .select('referral_tier_level')
      .eq('id', userId)
      .single();

    const { data: referrals } = await supabase
      .from('referrals')
      .select('status')
      .eq('referrer_id', userId);

    const successfulReferrals = referrals?.filter(r => r.status === 'converted').length || 0;
    const currentTier = profile?.referral_tier_level || 1;

    // Simple tier upgrade logic
    let newTier = currentTier;
    if (successfulReferrals >= 50 && currentTier < 5) newTier = 5;
    else if (successfulReferrals >= 25 && currentTier < 4) newTier = 4;
    else if (successfulReferrals >= 10 && currentTier < 3) newTier = 3;
    else if (successfulReferrals >= 5 && currentTier < 2) newTier = 2;

    if (newTier > currentTier) {
      await supabase
        .from('profiles')
        .update({ referral_tier_level: newTier })
        .eq('id', userId);

      await this.sendNotification(userId, {
        type: 'tier_upgraded',
        title: 'Tier Upgraded!',
        message: `Congratulations! You've been upgraded to Tier ${newTier}!`,
        data: { old_tier: currentTier, new_tier: newTier }
      });

      this.emitEvent({
        type: 'tier_changed',
        user_id: userId,
        data: { old_tier: currentTier, new_tier: newTier },
        timestamp: new Date().toISOString()
      });
    }
  }

  private async sendNotification(
    userId: string,
    notification: {
      type: string;
      title: string;
      message: string;
      data?: Record<string, unknown>;
    }
  ): Promise<void> {
    await supabase
      .from('referral_notifications')
      .insert({
        user_id: userId,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        is_read: false,
        priority: 'medium',
        created_at: new Date().toISOString()
      });
  }

  // Event Management
  addEventListener(eventType: ReferralEventType | 'all', callback: (event: ReferralEvent) => void): void {
    const key = eventType;
    if (!this.eventListeners.has(key)) {
      this.eventListeners.set(key, new Set());
    }
    this.eventListeners.get(key)?.add(callback);
  }

  removeEventListener(eventType: ReferralEventType | 'all', callback: (event: ReferralEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emitEvent(event: ReferralEvent): void {
    // Emit to specific event type listeners
    const typeListeners = this.eventListeners.get(event.type);
    typeListeners?.forEach(callback => callback(event));

    // Emit to 'all' event listeners
    const allListeners = this.eventListeners.get('all');
    allListeners?.forEach(callback => callback(event));
  }
}

// Export singleton instance
export const referralService = new ReferralService();

// Export factory function for testing
export const createReferralService = (): ReferralService => new ReferralService();