// AI Service Integration
// Handles AI-powered chat responses using Gemini and other providers

import type { 
  AIResponse, 
  AIPersonality, 
  ChatContext, 
  ChatMessage,
  AIServiceConfig,
  ChatAPIResponse 
} from '@/types/chat';

export interface AIProvider {
  generateResponse(prompt: string, context: ChatContext): Promise<AIResponse>;
  extractTopics(messages: ChatMessage[]): Promise<string[]>;
  analyzeSentiment(content: string): Promise<{ sentiment: string; confidence: number }>;
  generateSuggestions(context: ChatContext): Promise<string[]>;
}

// Gemini AI Provider
export class GeminiAIProvider implements AIProvider {
  private apiKey: string;
  private model: string;
  private baseUrl: string;

  constructor(config: { apiKey: string; model?: string }) {
    this.apiKey = config.apiKey;
    this.model = config.model || 'gemini-pro';
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  }

  async generateResponse(prompt: string, context: ChatContext): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      const systemPrompt = this.buildSystemPrompt(context);
      const fullPrompt = `${systemPrompt}\n\nUser: ${prompt}\nAssistant:`;

      const response = await fetch(`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: fullPrompt
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          },
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const processingTime = Date.now() - startTime;

      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No response generated from Gemini');
      }

      const candidate = data.candidates[0];
      const content = candidate.content?.parts?.[0]?.text || '';

      if (!content) {
        throw new Error('Empty response from Gemini');
      }

      return {
        content: content.trim(),
        confidence: this.calculateConfidence(candidate),
        processing_time_ms: processingTime,
        metadata: {
          model_used: this.model,
          tokens_used: this.estimateTokens(fullPrompt + content),
          context_length: fullPrompt.length,
          temperature: 0.7
        }
      };
    } catch (error) {
      console.error('Gemini AI error:', error);
      throw new Error(`AI service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async extractTopics(messages: ChatMessage[]): Promise<string[]> {
    try {
      const conversation = messages
        .map(msg => `${msg.sender_type}: ${msg.content}`)
        .join('\n');

      const prompt = `Analyze the following conversation and extract the main topics discussed. Return only the topics as a comma-separated list without any additional text:

${conversation}

Topics:`;

      const response = await fetch(`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.3,
            maxOutputTokens: 256,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to extract topics: ${response.status}`);
      }

      const data = await response.json();
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
      
      return content
        .split(',')
        .map((topic: string) => topic.trim())
        .filter((topic: string) => topic.length > 0)
        .slice(0, 10); // Limit to 10 topics
    } catch (error) {
      console.error('Topic extraction error:', error);
      return [];
    }
  }

  async analyzeSentiment(content: string): Promise<{ sentiment: string; confidence: number }> {
    try {
      const prompt = `Analyze the sentiment of the following text and respond with only one word: positive, negative, or neutral.

Text: "${content}"

Sentiment:`;

      const response = await fetch(`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 10,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to analyze sentiment: ${response.status}`);
      }

      const data = await response.json();
      const result = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim().toLowerCase() || 'neutral';
      
      const validSentiments = ['positive', 'negative', 'neutral'];
      const sentiment = validSentiments.includes(result) ? result : 'neutral';
      
      return {
        sentiment,
        confidence: 0.8 // Default confidence
      };
    } catch (error) {
      console.error('Sentiment analysis error:', error);
      return { sentiment: 'neutral', confidence: 0.5 };
    }
  }

  async generateSuggestions(context: ChatContext): Promise<string[]> {
    try {
      const recentMessage = context.recent_messages[context.recent_messages.length - 1];
      if (!recentMessage) return [];

      const prompt = `Based on the following conversation context, suggest 3 short follow-up questions or topics (each under 10 words). Return only the suggestions, one per line:

Recent message: "${recentMessage.content}"
Topics discussed: ${context.conversation_history.topics.join(', ')}

Suggestions:`;

      const response = await fetch(`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.8,
            maxOutputTokens: 150,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate suggestions: ${response.status}`);
      }

      const data = await response.json();
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
      
      return content
        .split('\n')
        .map((suggestion: string) => suggestion.trim())
        .filter((suggestion: string) => suggestion.length > 0 && suggestion.length < 100)
        .slice(0, 3);
    } catch (error) {
      console.error('Suggestion generation error:', error);
      return [];
    }
  }

  private buildSystemPrompt(context: ChatContext): string {
    const userProfile = context.user_profile;
    const conversationHistory = context.conversation_history;

    return `You are an AI assistant representing ${userProfile.name || 'the profile owner'}. 

Profile context:
- Bio: ${userProfile.bio || 'Not provided'}
- Interests: ${userProfile.interests?.join(', ') || 'Not specified'}

Conversation context:
- Topics discussed: ${conversationHistory.topics.join(', ')}
- Current sentiment: ${conversationHistory.sentiment_trend > 0 ? 'positive' : conversationHistory.sentiment_trend < 0 ? 'negative' : 'neutral'}
- Session duration: ${context.session_info.duration_minutes} minutes
- Message count: ${context.session_info.message_count}

Instructions:
- Be helpful, friendly, and engaging
- Stay in character as representing the profile owner
- Keep responses concise but informative
- Ask follow-up questions to maintain engagement
- Avoid controversial or sensitive topics
- If you don't know something, be honest about it`;
  }

  private calculateConfidence(candidate: any): number {
    // Calculate confidence based on finish reason and safety ratings
    if (candidate.finishReason === 'STOP') {
      return 0.9;
    } else if (candidate.finishReason === 'MAX_TOKENS') {
      return 0.7;
    } else {
      return 0.5;
    }
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }
}

// AI Service Manager
export class AIService {
  private provider: AIProvider;
  private personality: AIPersonality;
  private fallbackResponses: string[] = [
    "I'm here to help! Could you tell me more about what you're looking for?",
    "That's interesting! What would you like to know more about?",
    "I'd be happy to help. Could you provide more details?",
    "Thanks for reaching out! How can I assist you today?",
    "I appreciate your message. What specific information can I help you find?"
  ];

  constructor(provider: AIProvider, personality: AIPersonality) {
    this.provider = provider;
    this.personality = personality;
  }

  async generateResponse(
    message: string,
    context: ChatContext
  ): Promise<ChatAPIResponse<AIResponse>> {
    try {
      // Validate input
      if (!message.trim()) {
        throw new Error('Message content is required');
      }

      if (message.length > 1000) {
        throw new Error('Message is too long');
      }

      // Check for forbidden topics
      if (this.containsForbiddenTopics(message)) {
        return {
          success: true,
          data: {
            content: "I appreciate your message, but I'd prefer to focus on other topics. What else would you like to discuss?",
            confidence: 1.0,
            processing_time_ms: 10
          }
        };
      }

      // Generate AI response
      const response = await this.provider.generateResponse(message, context);
      
      // Apply personality adjustments
      const personalizedResponse = this.applyPersonality(response);

      return {
        success: true,
        data: personalizedResponse
      };
    } catch (error) {
      console.error('AI response generation error:', error);
      
      // Return fallback response
      const fallback = this.getFallbackResponse();
      return {
        success: true,
        data: {
          content: fallback,
          confidence: 0.5,
          processing_time_ms: 10,
          metadata: {
            model_used: 'fallback',
            tokens_used: 0,
            context_length: 0,
            temperature: 0
          }
        }
      };
    }
  }

  async extractTopicsFromConversation(messages: ChatMessage[]): Promise<ChatAPIResponse<string[]>> {
    try {
      const topics = await this.provider.extractTopics(messages);
      return {
        success: true,
        data: topics
      };
    } catch (error) {
      console.error('Topic extraction error:', error);
      return {
        success: false,
        error: {
          code: 'TOPIC_EXTRACTION_ERROR',
          message: error instanceof Error ? error.message : 'Failed to extract topics'
        }
      };
    }
  }

  async analyzeSentiment(content: string): Promise<ChatAPIResponse<{ sentiment: string; confidence: number }>> {
    try {
      const result = await this.provider.analyzeSentiment(content);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('Sentiment analysis error:', error);
      return {
        success: false,
        error: {
          code: 'SENTIMENT_ANALYSIS_ERROR',
          message: error instanceof Error ? error.message : 'Failed to analyze sentiment'
        }
      };
    }
  }

  async generateSuggestions(context: ChatContext): Promise<ChatAPIResponse<string[]>> {
    try {
      const suggestions = await this.provider.generateSuggestions(context);
      return {
        success: true,
        data: suggestions
      };
    } catch (error) {
      console.error('Suggestion generation error:', error);
      return {
        success: false,
        error: {
          code: 'SUGGESTION_ERROR',
          message: error instanceof Error ? error.message : 'Failed to generate suggestions'
        }
      };
    }
  }

  private applyPersonality(response: AIResponse): AIResponse {
    let content = response.content;

    // Apply tone adjustments
    switch (this.personality.tone) {
      case 'enthusiastic':
        content = this.addEnthusiasm(content);
        break;
      case 'professional':
        content = this.makeProfessional(content);
        break;
      case 'casual':
        content = this.makeCasual(content);
        break;
      case 'humorous':
        content = this.addHumor(content);
        break;
    }

    // Apply response length preference
    if (this.personality.response_length === 'short') {
      content = this.shortenResponse(content);
    } else if (this.personality.response_length === 'long') {
      content = this.expandResponse(content);
    }

    return {
      ...response,
      content
    };
  }

  private containsForbiddenTopics(message: string): boolean {
    const forbidden = this.personality.forbidden_topics || [];
    const lowerMessage = message.toLowerCase();
    
    return forbidden.some(topic => 
      lowerMessage.includes(topic.toLowerCase())
    );
  }

  private getFallbackResponse(): string {
    return this.fallbackResponses[Math.floor(Math.random() * this.fallbackResponses.length)];
  }

  private addEnthusiasm(content: string): string {
    // Add enthusiastic elements
    if (!content.includes('!')) {
      content = content.replace(/\.$/, '!');
    }
    return content;
  }

  private makeProfessional(content: string): string {
    // Make more formal and professional
    return content.replace(/\b(gonna|wanna|gotta)\b/g, (match) => {
      switch (match) {
        case 'gonna': return 'going to';
        case 'wanna': return 'want to';
        case 'gotta': return 'have to';
        default: return match;
      }
    });
  }

  private makeCasual(content: string): string {
    // Make more casual and friendly
    content = content.replace(/\bI would\b/g, "I'd");
    content = content.replace(/\bYou are\b/g, "You're");
    return content;
  }

  private addHumor(content: string): string {
    // Add light humor (basic implementation)
    const humorPhrases = [
      "😊",
      "(in a good way!)",
      "- pretty cool, right?"
    ];
    
    if (Math.random() < 0.3) { // 30% chance to add humor
      const phrase = humorPhrases[Math.floor(Math.random() * humorPhrases.length)];
      content += ` ${phrase}`;
    }
    
    return content;
  }

  private shortenResponse(content: string): string {
    // Shorten to first sentence or two
    const sentences = content.split(/[.!?]+/);
    return sentences.slice(0, 2).join('. ').trim() + (sentences.length > 2 ? '.' : '');
  }

  private expandResponse(content: string): string {
    // Add follow-up question or elaboration
    const followUps = [
      " What are your thoughts on this?",
      " Would you like me to explain more about any particular aspect?",
      " Is there anything specific you'd like to explore further?",
      " Let me know if you need more details on any part of this!"
    ];
    
    const followUp = followUps[Math.floor(Math.random() * followUps.length)];
    return content + followUp;
  }
}

// Factory function to create AI service
export function createAIService(config: AIServiceConfig): AIService {
  let provider: AIProvider;

  switch (config.provider) {
    case 'gemini':
      provider = new GeminiAIProvider({
        apiKey: config.api_key,
        model: config.model
      });
      break;
    default:
      throw new Error(`Unsupported AI provider: ${config.provider}`);
  }

  return new AIService(provider, config.personality);
}

// Default AI configuration
export const defaultAIConfig: AIServiceConfig = {
  provider: 'gemini',
  model: 'gemini-pro',
  api_key: import.meta.env.VITE_GEMINI_API_KEY || '',
  max_tokens: 1024,
  temperature: 0.7,
  personality: {
    tone: 'friendly',
    style: 'conversational',
    traits: ['helpful', 'engaging', 'knowledgeable'],
    interests: ['technology', 'creativity', 'learning'],
    response_length: 'medium',
    knowledge_areas: ['general'],
    conversation_starters: [
      "Hi there! How can I help you today?",
      "Welcome! What would you like to know?",
      "Hello! I'm here to assist you with any questions."
    ]
  }
};