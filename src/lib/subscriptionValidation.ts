// Subscription Validation
// Validation utilities for subscriptions, payments, and billing

import type {
  Subscription,
  SubscriptionPlan,
  PaymentMethod,
  Invoice,
  Discount,
  SubscriptionValidationRules,
  SubscriptionValidationResult,
  BillingCycle,
  PaymentMethodType,
  SubscriptionStatus,
  CardDetails,
  BankDetails,
  BillingAddress
} from '@/types/subscription';
import { SubscriptionError, PaymentError } from '@/types/subscription';

// Default validation rules
export const defaultSubscriptionValidationRules: SubscriptionValidationRules = {
  payment_method: {
    required_for_trial: false,
    allowed_types: ['card', 'bank_account', 'wallet']
  },
  billing: {
    grace_period_days: 7,
    max_failed_attempts: 3,
    dunning_enabled: true
  },
  plan_changes: {
    allow_downgrades: true,
    proration_enabled: true,
    immediate_change: true
  }
};

// Card brand patterns
const CARD_PATTERNS = {
  visa: /^4[0-9]{12}(?:[0-9]{3})?$/,
  mastercard: /^5[1-5][0-9]{14}$/,
  amex: /^3[47][0-9]{13}$/,
  discover: /^6(?:011|5[0-9]{2})[0-9]{12}$/,
  diners: /^3[0689][0-9]{11}$/,
  jcb: /^(?:2131|1800|35\d{3})\d{11}$/
};

// Blocked card numbers (for testing/security)
const BLOCKED_CARD_NUMBERS = [
  '****************', // Declined card
  '****************', // Incorrect CVC
  '****************', // Expired card
];

export class SubscriptionValidator {
  private rules: SubscriptionValidationRules;

  constructor(rules: SubscriptionValidationRules = defaultSubscriptionValidationRules) {
    this.rules = rules;
  }

  // Subscription Validation
  validateSubscription(subscription: Partial<Subscription>): SubscriptionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const requiredActions: string[] = [];

    // Required fields validation
    if (!subscription.user_id) {
      errors.push('User ID is required');
    }

    if (!subscription.plan_id) {
      errors.push('Plan ID is required');
    }

    if (!subscription.billing_cycle) {
      errors.push('Billing cycle is required');
    }

    // Status validation
    if (subscription.status) {
      const validStatuses: SubscriptionStatus[] = [
        'active', 'trialing', 'past_due', 'canceled', 'unpaid', 'incomplete', 'incomplete_expired', 'paused'
      ];
      if (!validStatuses.includes(subscription.status)) {
        errors.push(`Invalid subscription status: ${subscription.status}`);
      }
    }

    // Date validation
    if (subscription.current_period_start && subscription.current_period_end) {
      const startDate = new Date(subscription.current_period_start);
      const endDate = new Date(subscription.current_period_end);
      
      if (startDate >= endDate) {
        errors.push('Current period end must be after current period start');
      }
    }

    // Trial validation
    if (subscription.trial_start && subscription.trial_end) {
      const trialStart = new Date(subscription.trial_start);
      const trialEnd = new Date(subscription.trial_end);
      
      if (trialStart >= trialEnd) {
        errors.push('Trial end must be after trial start');
      }

      // Check if trial is in the past
      if (trialEnd < new Date()) {
        warnings.push('Trial period has expired');
        if (subscription.status === 'trialing') {
          requiredActions.push('Update subscription status from trialing');
        }
      }
    }

    // Payment method validation for non-trial subscriptions
    if (subscription.status !== 'trialing' && !subscription.payment_method_id) {
      if (this.rules.payment_method.required_for_trial) {
        errors.push('Payment method is required');
      } else {
        warnings.push('No payment method attached to subscription');
        requiredActions.push('Add a payment method before trial expires');
      }
    }

    // Amount validation
    if (subscription.amount !== undefined) {
      if (subscription.amount < 0) {
        errors.push('Subscription amount cannot be negative');
      }
      if (subscription.amount === 0 && subscription.status === 'active') {
        warnings.push('Active subscription with zero amount');
      }
    }

    // Currency validation
    if (subscription.currency) {
      const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'];
      if (!validCurrencies.includes(subscription.currency.toUpperCase())) {
        warnings.push(`Currency ${subscription.currency} may not be fully supported`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      can_proceed: errors.length === 0,
      required_actions: requiredActions.length > 0 ? requiredActions : undefined
    };
  }

  // Plan Validation
  validatePlan(plan: Partial<SubscriptionPlan>): SubscriptionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!plan.name || plan.name.trim().length === 0) {
      errors.push('Plan name is required');
    }

    if (!plan.currency) {
      errors.push('Plan currency is required');
    }

    // Price validation
    if (plan.price_monthly !== undefined) {
      if (plan.price_monthly < 0) {
        errors.push('Monthly price cannot be negative');
      }
    }

    if (plan.price_yearly !== undefined) {
      if (plan.price_yearly < 0) {
        errors.push('Yearly price cannot be negative');
      }
      
      // Check if yearly price offers discount
      if (plan.price_monthly && plan.price_yearly >= plan.price_monthly * 12) {
        warnings.push('Yearly price does not offer a discount over monthly');
      }
    }

    // Trial validation
    if (plan.trial_days !== undefined) {
      if (plan.trial_days < 0) {
        errors.push('Trial days cannot be negative');
      }
      if (plan.trial_days > 365) {
        warnings.push('Trial period longer than 1 year is unusual');
      }
    }

    // Features validation
    if (plan.features) {
      plan.features.forEach((feature, index) => {
        if (!feature.name || feature.name.trim().length === 0) {
          errors.push(`Feature ${index + 1} must have a name`);
        }
        if (feature.limit !== undefined && feature.limit < 0) {
          errors.push(`Feature ${feature.name} limit cannot be negative`);
        }
      });
    }

    // Limits validation
    if (plan.limits) {
      const limits = plan.limits;
      const limitFields = [
        'max_links', 'max_rooms', 'max_avatars', 'max_chat_sessions_per_month',
        'max_page_views_per_month', 'max_custom_themes', 'analytics_retention_days'
      ];

      limitFields.forEach(field => {
        const value = limits[field as keyof typeof limits];
        if (typeof value === 'number' && value < 0) {
          errors.push(`${field} cannot be negative`);
        }
      });

      if (limits.analytics_retention_days > 2555) { // 7 years
        warnings.push('Analytics retention period is very long');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      can_proceed: errors.length === 0
    };
  }

  // Payment Method Validation
  validatePaymentMethod(paymentMethod: Partial<PaymentMethod>): SubscriptionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!paymentMethod.type) {
      errors.push('Payment method type is required');
    } else if (!this.rules.payment_method.allowed_types.includes(paymentMethod.type)) {
      errors.push(`Payment method type '${paymentMethod.type}' is not allowed`);
    }

    if (!paymentMethod.provider) {
      errors.push('Payment provider is required');
    }

    // Type-specific validation
    if (paymentMethod.type === 'card' && paymentMethod.card_details) {
      const cardValidation = this.validateCardDetails(paymentMethod.card_details);
      errors.push(...cardValidation.errors);
      warnings.push(...cardValidation.warnings);
    }

    if (paymentMethod.type === 'bank_account' && paymentMethod.bank_details) {
      const bankValidation = this.validateBankDetails(paymentMethod.bank_details);
      errors.push(...bankValidation.errors);
      warnings.push(...bankValidation.warnings);
    }

    // Billing address validation
    if (paymentMethod.billing_address) {
      const addressValidation = this.validateBillingAddress(paymentMethod.billing_address);
      errors.push(...addressValidation.errors);
      warnings.push(...addressValidation.warnings);
    }

    // Expiration validation
    if (paymentMethod.expires_at) {
      const expirationDate = new Date(paymentMethod.expires_at);
      if (expirationDate <= new Date()) {
        errors.push('Payment method has expired');
      } else if (expirationDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) {
        warnings.push('Payment method expires within 30 days');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      can_proceed: errors.length === 0
    };
  }

  // Card Details Validation
  validateCardDetails(cardDetails: Partial<CardDetails>): SubscriptionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Last four validation
    if (!cardDetails.last_four) {
      errors.push('Card last four digits are required');
    } else if (!/^\d{4}$/.test(cardDetails.last_four)) {
      errors.push('Card last four must be 4 digits');
    }

    // Expiration validation
    if (!cardDetails.exp_month || !cardDetails.exp_year) {
      errors.push('Card expiration month and year are required');
    } else {
      if (cardDetails.exp_month < 1 || cardDetails.exp_month > 12) {
        errors.push('Invalid expiration month');
      }

      const currentYear = new Date().getFullYear();
      if (cardDetails.exp_year < currentYear) {
        errors.push('Card has expired');
      } else if (cardDetails.exp_year > currentYear + 20) {
        warnings.push('Expiration year seems too far in the future');
      }

      // Check if card expires this month
      const currentMonth = new Date().getMonth() + 1;
      if (cardDetails.exp_year === currentYear && cardDetails.exp_month === currentMonth) {
        warnings.push('Card expires this month');
      }
    }

    // Brand validation
    if (cardDetails.brand) {
      const validBrands = ['visa', 'mastercard', 'amex', 'discover', 'diners', 'jcb'];
      if (!validBrands.includes(cardDetails.brand.toLowerCase())) {
        warnings.push(`Uncommon card brand: ${cardDetails.brand}`);
      }
    }

    // Country validation
    if (cardDetails.country) {
      const restrictedCountries = ['XX', 'ZZ']; // Example restricted countries
      if (restrictedCountries.includes(cardDetails.country.toUpperCase())) {
        errors.push(`Cards from ${cardDetails.country} are not supported`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      can_proceed: errors.length === 0
    };
  }

  // Bank Details Validation
  validateBankDetails(bankDetails: Partial<BankDetails>): SubscriptionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!bankDetails.bank_name || bankDetails.bank_name.trim().length === 0) {
      errors.push('Bank name is required');
    }

    if (!bankDetails.account_type) {
      errors.push('Account type is required');
    } else {
      const validAccountTypes = ['checking', 'savings'];
      if (!validAccountTypes.includes(bankDetails.account_type.toLowerCase())) {
        errors.push('Invalid account type');
      }
    }

    if (!bankDetails.routing_number) {
      errors.push('Routing number is required');
    } else if (!/^\d{9}$/.test(bankDetails.routing_number)) {
      errors.push('Routing number must be 9 digits');
    }

    if (!bankDetails.last_four) {
      errors.push('Account last four digits are required');
    } else if (!/^\d{4}$/.test(bankDetails.last_four)) {
      errors.push('Account last four must be 4 digits');
    }

    if (!bankDetails.country) {
      errors.push('Bank country is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      can_proceed: errors.length === 0
    };
  }

  // Billing Address Validation
  validateBillingAddress(address: Partial<BillingAddress>): SubscriptionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!address.name || address.name.trim().length === 0) {
      errors.push('Billing name is required');
    }

    if (!address.line1 || address.line1.trim().length === 0) {
      errors.push('Address line 1 is required');
    }

    if (!address.city || address.city.trim().length === 0) {
      errors.push('City is required');
    }

    if (!address.country || address.country.trim().length === 0) {
      errors.push('Country is required');
    } else if (address.country.length !== 2) {
      errors.push('Country must be a 2-letter country code');
    }

    // Postal code validation (basic)
    if (!address.postal_code || address.postal_code.trim().length === 0) {
      errors.push('Postal code is required');
    } else {
      // US postal code validation
      if (address.country?.toUpperCase() === 'US') {
        if (!/^\d{5}(-\d{4})?$/.test(address.postal_code)) {
          errors.push('Invalid US postal code format');
        }
      }
      // UK postal code validation
      if (address.country?.toUpperCase() === 'GB') {
        if (!/^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$/.test(address.postal_code.toUpperCase())) {
          warnings.push('Postal code format may be invalid for UK');
        }
      }
    }

    // State validation for US addresses
    if (address.country?.toUpperCase() === 'US' && !address.state) {
      errors.push('State is required for US addresses');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      can_proceed: errors.length === 0
    };
  }

  // Invoice Validation
  validateInvoice(invoice: Partial<Invoice>): SubscriptionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!invoice.user_id) {
      errors.push('User ID is required');
    }

    if (!invoice.amount_due || invoice.amount_due < 0) {
      errors.push('Amount due must be a positive number');
    }

    if (!invoice.currency) {
      errors.push('Currency is required');
    }

    // Amount consistency
    if (invoice.amount_due !== undefined && invoice.amount_paid !== undefined && invoice.amount_remaining !== undefined) {
      const calculatedRemaining = invoice.amount_due - invoice.amount_paid;
      if (Math.abs(calculatedRemaining - invoice.amount_remaining) > 0.01) {
        errors.push('Invoice amount calculations are inconsistent');
      }
    }

    // Date validation
    if (invoice.due_date) {
      const dueDate = new Date(invoice.due_date);
      const now = new Date();
      
      if (dueDate < now) {
        warnings.push('Invoice is past due');
      }
    }

    // Line items validation
    if (invoice.line_items) {
      let totalLineItems = 0;
      invoice.line_items.forEach((item, index) => {
        if (!item.description || item.description.trim().length === 0) {
          errors.push(`Line item ${index + 1} must have a description`);
        }
        if (item.quantity <= 0) {
          errors.push(`Line item ${index + 1} quantity must be positive`);
        }
        if (item.unit_price < 0) {
          errors.push(`Line item ${index + 1} unit price cannot be negative`);
        }
        if (Math.abs(item.amount - (item.quantity * item.unit_price)) > 0.01) {
          errors.push(`Line item ${index + 1} amount calculation is incorrect`);
        }
        totalLineItems += item.amount;
      });

      // Check if line items total matches subtotal
      if (invoice.subtotal !== undefined && Math.abs(totalLineItems - invoice.subtotal) > 0.01) {
        errors.push('Line items total does not match subtotal');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      can_proceed: errors.length === 0
    };
  }

  // Discount Validation
  validateDiscount(discount: Partial<Discount>): SubscriptionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!discount.name || discount.name.trim().length === 0) {
      errors.push('Discount name is required');
    }

    if (!discount.code || discount.code.trim().length === 0) {
      errors.push('Discount code is required');
    } else {
      // Code format validation
      if (!/^[A-Z0-9_-]+$/.test(discount.code.toUpperCase())) {
        errors.push('Discount code can only contain letters, numbers, underscores, and hyphens');
      }
      if (discount.code.length < 3) {
        errors.push('Discount code must be at least 3 characters');
      }
      if (discount.code.length > 50) {
        errors.push('Discount code cannot exceed 50 characters');
      }
    }

    if (!discount.type) {
      errors.push('Discount type is required');
    }

    if (discount.value === undefined || discount.value === null) {
      errors.push('Discount value is required');
    } else {
      if (discount.value <= 0) {
        errors.push('Discount value must be positive');
      }

      // Type-specific validation
      if (discount.type === 'percentage' && discount.value > 100) {
        errors.push('Percentage discount cannot exceed 100%');
      }

      if (discount.type === 'fixed_amount' && !discount.currency) {
        errors.push('Currency is required for fixed amount discounts');
      }
    }

    // Date validation
    if (discount.valid_from && discount.valid_until) {
      const validFrom = new Date(discount.valid_from);
      const validUntil = new Date(discount.valid_until);
      
      if (validFrom >= validUntil) {
        errors.push('Valid until date must be after valid from date');
      }
    }

    // Redemption validation
    if (discount.max_redemptions !== undefined) {
      if (discount.max_redemptions <= 0) {
        errors.push('Max redemptions must be positive');
      }
      
      if (discount.redemptions_count !== undefined && discount.redemptions_count >= discount.max_redemptions) {
        warnings.push('Discount has reached its redemption limit');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      can_proceed: errors.length === 0
    };
  }

  // Plan Change Validation
  validatePlanChange(
    currentPlan: SubscriptionPlan,
    newPlan: SubscriptionPlan,
    subscription: Subscription
  ): SubscriptionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const requiredActions: string[] = [];

    // Check if downgrade is allowed
    const isDowngrade = newPlan.price_monthly < currentPlan.price_monthly;
    if (isDowngrade && !this.rules.plan_changes.allow_downgrades) {
      errors.push('Plan downgrades are not allowed');
    }

    // Check feature limitations
    if (newPlan.limits) {
      // Would need to check current usage against new limits
      if (newPlan.limits.max_links < currentPlan.limits.max_links) {
        warnings.push('New plan has fewer link limits');
        requiredActions.push('Review current link usage before downgrading');
      }

      if (newPlan.limits.max_rooms < currentPlan.limits.max_rooms) {
        warnings.push('New plan has fewer room limits');
        requiredActions.push('Review current room usage before downgrading');
      }
    }

    // Trial considerations
    if (subscription.status === 'trialing') {
      warnings.push('Changing plans during trial period');
      if (newPlan.trial_days === 0) {
        requiredActions.push('Trial will end immediately upon plan change');
      }
    }

    // Payment method requirements
    if (!subscription.payment_method_id && newPlan.price_monthly > 0) {
      errors.push('Payment method required for paid plan');
      requiredActions.push('Add a payment method before changing to paid plan');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      can_proceed: errors.length === 0,
      required_actions: requiredActions.length > 0 ? requiredActions : undefined
    };
  }

  // Billing Cycle Validation
  validateBillingCycle(cycle: BillingCycle, plan: SubscriptionPlan): SubscriptionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if billing cycle is supported by the plan
    if (!plan.billing_cycles.includes(cycle)) {
      errors.push(`Billing cycle '${cycle}' is not supported by this plan`);
    }

    // Validate cycle format
    const validCycles: BillingCycle[] = ['monthly', 'yearly', 'quarterly', 'weekly'];
    if (!validCycles.includes(cycle)) {
      errors.push(`Invalid billing cycle: ${cycle}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      can_proceed: errors.length === 0
    };
  }

  // Rule Management
  updateRules(newRules: Partial<SubscriptionValidationRules>): void {
    this.rules = { ...this.rules, ...newRules };
  }

  getRules(): SubscriptionValidationRules {
    return { ...this.rules };
  }
}

// Utility functions
export const validateSubscription = (
  subscription: Partial<Subscription>,
  rules?: SubscriptionValidationRules
): SubscriptionValidationResult => {
  const validator = new SubscriptionValidator(rules);
  return validator.validateSubscription(subscription);
};

export const validatePaymentMethod = (
  paymentMethod: Partial<PaymentMethod>,
  rules?: SubscriptionValidationRules
): SubscriptionValidationResult => {
  const validator = new SubscriptionValidator(rules);
  return validator.validatePaymentMethod(paymentMethod);
};

export const validatePlanChange = (
  currentPlan: SubscriptionPlan,
  newPlan: SubscriptionPlan,
  subscription: Subscription,
  rules?: SubscriptionValidationRules
): SubscriptionValidationResult => {
  const validator = new SubscriptionValidator(rules);
  return validator.validatePlanChange(currentPlan, newPlan, subscription);
};

// Credit card utilities
export const validateCreditCardNumber = (cardNumber: string): { isValid: boolean; brand?: string } => {
  const cleanNumber = cardNumber.replace(/\s/g, '');
  
  // Check if it's a blocked test card
  if (BLOCKED_CARD_NUMBERS.includes(cleanNumber)) {
    return { isValid: false };
  }

  // Detect card brand
  let brand: string | undefined;
  for (const [brandName, pattern] of Object.entries(CARD_PATTERNS)) {
    if (pattern.test(cleanNumber)) {
      brand = brandName;
      break;
    }
  }

  // Luhn algorithm validation
  const isValid = luhnCheck(cleanNumber);

  return { isValid, brand };
};

export const luhnCheck = (cardNumber: string): boolean => {
  let sum = 0;
  let isEven = false;

  for (let i = cardNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cardNumber.charAt(i), 10);

    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEven = !isEven;
  }

  return sum % 10 === 0;
};

// Format validation errors for API responses
export const formatSubscriptionValidationErrors = (result: SubscriptionValidationResult): SubscriptionError => {
  return new SubscriptionError(
    result.errors.join(', '),
    'VALIDATION_ERROR'
  );
};

// Export singleton validator instance
export const subscriptionValidator = new SubscriptionValidator();