
import { motion } from 'framer-motion';
import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Upload, User, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface AvatarUploadPanelProps {
  avatarFile: File | null;
  onAvatarChange: (file: File | null) => void;
}

export function AvatarUploadPanel({ avatarFile, onAvatarChange }: AvatarUploadPanelProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const { t } = useTranslation();

  const handleFileUpload = (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    if (!file.type.startsWith('image/')) return;

    onAvatarChange(file);
    
    // Create preview URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const removeAvatar = () => {
    onAvatarChange(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Card className="glass-panel">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="w-5 h-5" />
            <span>{t('avatarUploadPanel.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Avatar Preview */}
          <div className="flex justify-center">
            <div className="relative">
              <Avatar className="w-24 h-24">
                <AvatarImage src={previewUrl || undefined} />
                <AvatarFallback className="text-2xl">
                  <User className="w-8 h-8" />
                </AvatarFallback>
              </Avatar>
              
              {avatarFile && (
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 w-6 h-6 rounded-full p-0"
                  onClick={removeAvatar}
                >
                  <X className="w-3 h-3" />
                </Button>
              )}
            </div>
          </div>

          {/* Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
              isDragOver 
                ? 'border-primary bg-primary/10' 
                : 'border-white/20 hover:border-white/40'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <Upload className="w-6 h-6 mx-auto mb-2 text-muted-foreground" />
            <p className="text-xs text-muted-foreground mb-2">
              {t('avatarUploadPanel.drop')}
            </p>
            <Button
              variant="outline"
              size="sm"
              className="glass-button"
              onClick={() => document.getElementById('avatar-upload')?.click()}
            >
              {t('avatarUploadPanel.chooseImage')}
            </Button>
            <input
              id="avatar-upload"
              type="file"
              accept="image/*"
              className="hidden"
              onChange={(e) => handleFileUpload(e.target.files)}
            />
          </div>

          {/* File Info */}
          {avatarFile && (
            <div className="p-3 glass-button rounded-lg">
              <p className="text-sm font-medium">{avatarFile.name}</p>
              <p className="text-xs text-muted-foreground">
                {(avatarFile.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
          )}

          {/* Info */}
          <div className="p-3 glass-button rounded-lg">
            <p className="text-xs text-muted-foreground">
              {t('avatarUploadPanel.uploadInfo')}
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
