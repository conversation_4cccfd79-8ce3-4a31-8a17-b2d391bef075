
import { memo } from 'react';

interface FloatingLogo {
  top: string;
  left: string;
  size: number;
  duration: number;
  opacity: number;
  delay: number;
}

interface OptimizedFloatingLogosProps {
  logos: FloatingLogo[];
}

export const OptimizedFloatingLogos = memo(({ logos }: OptimizedFloatingLogosProps) => {
  return (
    <>
      {logos.map((logo, i) => (
        <div
          key={i}
          className="floating-logos pointer-events-none absolute select-none will-change-transform"
          style={{
            top: logo.top,
            left: logo.left,
            width: `${logo.size}rem`,
            height: `${logo.size}rem`,
            opacity: logo.opacity,
            filter: 'blur(0.5px)',
            animation: `floatingLogo ${logo.duration}s linear infinite`,
            animationDelay: `${logo.delay}s`,
            contentVisibility: 'auto',
            containIntrinsicSize: `${logo.size}rem ${logo.size}rem`
          }}
        >
          <img
            src="/lovable-uploads/4fd12ff6-660e-4077-8399-cc47ddfdbf40.png"
            alt=""
            width={logo.size * 16}
            height={logo.size * 16}
            loading="lazy"
            className="w-full h-full"
          />
        </div>
      ))}
    </>
  );
});

OptimizedFloatingLogos.displayName = 'OptimizedFloatingLogos';
