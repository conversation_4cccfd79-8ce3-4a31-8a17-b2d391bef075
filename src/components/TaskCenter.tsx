
import { motion } from 'framer-motion';
import { CheckCircle, Circle, Star, Trophy, Calendar, Share2, UserPlus, CreditCard } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

const dailyTasks = [
  { id: 1, title: 'Daily Login', reward: 5, completed: true, icon: Calendar },
  { id: 2, title: 'Update Avatar', reward: 10, completed: true, icon: Star },
  { id: 3, title: 'Share Bio Link', reward: 15, completed: false, icon: Share2 },
];

const weeklyTasks = [
  { id: 4, title: 'Invite 3 Friends', reward: 50, completed: false, progress: 1, target: 3, icon: UserPlus },
  { id: 5, title: 'Complete Profile', reward: 25, completed: true, icon: Trophy },
  { id: 6, title: 'Subscribe to Pro', reward: 100, completed: false, icon: CreditCard },
];

const achievements = [
  { id: 7, title: 'First Avatar', reward: 20, completed: true, description: 'Create your first AI avatar' },
  { id: 8, title: 'Social Butterfly', reward: 75, completed: false, description: 'Get 100 bio link visits' },
  { id: 9, title: 'Influencer', reward: 150, completed: false, description: 'Refer 10 users successfully' },
];

export function TaskCenter() {
  const totalDaily = dailyTasks.reduce((sum, task) => sum + (task.completed ? task.reward : 0), 0);
  const maxDaily = dailyTasks.reduce((sum, task) => sum + task.reward, 0);

  return (
    <div className="space-y-10">
      {/* Daily Progress */}
      <Card className="glass-card overflow-hidden">
        <CardHeader className="px-8 pt-8 pb-6">
          <CardTitle className="flex items-center justify-between text-3xl font-bold">
            Daily Tasks
            <div className="flex items-center space-x-3">
              <img src="/3D-coin/heycoin-gold.webp" alt="HEEY Coin" className="w-8 h-8 object-contain" />
              <Badge variant="outline" className="px-4 py-2 text-lg font-semibold rounded-[1rem] bg-white/10 backdrop-blur-xl">
                {totalDaily}/{maxDaily} $HEEY
              </Badge>
            </div>
          </CardTitle>
          <CardDescription className="text-xl font-medium">
            Complete daily tasks to earn $HEEY
          </CardDescription>
        </CardHeader>
        <CardContent className="px-8 pb-8">
          <div className="mb-8">
            <Progress value={(totalDaily / maxDaily) * 100} className="h-4 rounded-[1rem]" />
          </div>
          <div className="space-y-6">
            {dailyTasks.map((task, index) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`flex items-center justify-between p-6 rounded-2xl bg-background/20 backdrop-blur-sm ${
                  task.completed ? 'bg-green-500/10' : ''
                }`}
              >
                <div className="flex items-center space-x-6">
                  <div className={`w-16 h-16 rounded-xl flex items-center justify-center backdrop-blur-xl ${
                    task.completed ? 'bg-green-500/15' : 'bg-muted/10'
                  }`}>
                    <task.icon className={`w-8 h-8 ${
                      task.completed ? 'text-green-400' : 'text-muted-foreground'
                    }`} />
                  </div>
                  <div>
                    <p className="font-semibold text-xl text-foreground">{task.title}</p>
                    <div className="flex items-center space-x-2">
                      <img src="/3D-coin/heycoin-gold.webp" alt="HEEY Coin" className="w-5 h-5 object-contain" />
                      <p className="text-lg text-muted-foreground font-medium">{'+' + task.reward + ' $HEEY'}</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {task.completed ? (
                    <CheckCircle className="w-8 h-8 text-green-400" />
                  ) : (
                    <Button size="sm" variant="outline" className="rounded-[1rem] px-6 py-3 text-base font-semibold bg-white/10 backdrop-blur-xl">
                      Complete
                    </Button>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Weekly Tasks */}
      <Card className="glass-card overflow-hidden">
        <CardHeader className="px-8 pt-8 pb-6">
          <CardTitle className="text-3xl font-bold">Weekly Challenges</CardTitle>
          <CardDescription className="text-xl font-medium">
            Bigger rewards for weekly milestones
          </CardDescription>
        </CardHeader>
        <CardContent className="px-8 pb-8">
          <div className="space-y-8">
            {weeklyTasks.map((task, index) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`p-8 rounded-2xl bg-background/20 backdrop-blur-sm ${
                  task.completed ? 'bg-green-500/10' : ''
                }`}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-6">
                    <div className={`w-18 h-18 rounded-xl flex items-center justify-center backdrop-blur-xl ${
                      task.completed ? 'bg-green-500/15' : 'bg-muted/10'
                    }`}>
                      <task.icon className={`w-9 h-9 ${
                        task.completed ? 'text-green-400' : 'text-muted-foreground'
                      }`} />
                    </div>
                    <div>
                      <p className="font-semibold text-xl text-foreground">{task.title}</p>
                      <div className="flex items-center space-x-2">
                        <img src="/3D-coin/heycoin-gold.webp" alt="HEEY Coin" className="w-5 h-5 object-contain" />
                        <p className="text-lg text-muted-foreground font-medium">{'+' + task.reward + ' $HEEY'}</p>
                      </div>
                    </div>
                  </div>
                  <Badge variant={task.completed ? "default" : "secondary"} className="px-4 py-2 text-lg font-semibold rounded-[1rem]">
                    {task.completed ? 'Completed' : 'In Progress'}
                  </Badge>
                </div>
                {task.progress !== undefined && task.target && !task.completed && (
                  <div className="mt-6">
                    <div className="flex justify-between text-lg font-medium mb-3">
                      <span>{task.progress}/{task.target}</span>
                      <span>{Math.round((task.progress / task.target) * 100)}%</span>
                    </div>
                    <Progress value={(task.progress / task.target) * 100} className="h-4 rounded-[1rem]" />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Achievements */}
      <Card className="glass-card overflow-hidden">
        <CardHeader className="px-8 pt-8 pb-6">
          <CardTitle className="text-3xl font-bold">Achievements</CardTitle>
          <CardDescription className="text-xl font-medium">
            Special rewards for major milestones
          </CardDescription>
        </CardHeader>
        <CardContent className="px-8 pb-8">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.id}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`p-8 rounded-2xl text-center relative overflow-hidden bg-background/20 backdrop-blur-sm ${
                  achievement.completed ? 'bg-yellow-500/10' : ''
                }`}
              >
                <div className={`w-20 h-20 rounded-xl flex items-center justify-center mx-auto mb-6 backdrop-blur-xl ${
                  achievement.completed ? 'bg-yellow-500/15' : 'bg-muted/10'
                }`}>
                  <Trophy className={`w-10 h-10 ${
                    achievement.completed ? 'text-yellow-400' : 'text-muted-foreground'
                  }`} />
                </div>
                <h4 className="font-bold text-xl mb-3 text-foreground">{achievement.title}</h4>
                <p className="text-lg text-muted-foreground mb-6 font-medium">{achievement.description}</p>
                <div className="flex items-center justify-center space-x-2">
                  <img src="/3D-coin/heycoin-gold.webp" alt="HEEY Coin" className="w-6 h-6 object-contain" />
                  <Badge variant={achievement.completed ? "default" : "secondary"} className="px-4 py-2 text-lg font-semibold rounded-[1rem]">
                    {'+' + achievement.reward + ' $HEEY'}
                  </Badge>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
