
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { loadModelViewer } from './AvatarViewer';

interface AvatarPreviewProps {
  avatarUrl: string;
}

export function AvatarPreview({ avatarUrl }: AvatarPreviewProps) {
  const { t } = useTranslation();
  useEffect(() => {
    loadModelViewer();
  }, []);

  if (!avatarUrl) {
    return (
      <div className="w-full h-full flex items-center justify-center glass-panel rounded-lg">
        <p className="text-muted-foreground">{t('avatarPreview.noAvatar')}</p>
      </div>
    );
  }

  return (
    <model-viewer
      src={avatarUrl}
      camera-controls
      auto-rotate
      ar
      loading="lazy"
      reveal="interaction"
      style={{ width: '100%', height: '100%' }}
    ></model-viewer>
  );
}
