
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LogOut, X, User } from 'lucide-react';
import { AppIcon } from '@/components/AppIcon';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';

interface AccountSidebarProps {
  onClose?: () => void;
  onLogout?: () => void;
  className?: string;
}

export function AccountSidebar({ onClose, onLogout, className }: AccountSidebarProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { signOut } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  const menuItems = [
    { id: 'account', label: t('accountSidebar.accountSetting'), icon: 'Settings', path: '/my-account/account' },
    { id: 'payment', label: t('accountSidebar.paymentSetting'), icon: 'Card', path: '/my-account/payment' },
    { id: 'upgrade', label: t('accountSidebar.upgrade'), icon: 'Crown', path: '/my-account/upgrade' },
  ];

  const handleLogout = async () => {
    try {
      console.log('Starting logout process...');

      // Call the signOut function from AuthContext
      await signOut();

      // Clear any additional localStorage items
      localStorage.removeItem('user');
      localStorage.clear();

      console.log('Logout successful, navigating to home...');

      // Close the sidebar if needed
      onClose?.();

      // Call the optional onLogout callback
      onLogout?.();

      // Navigate to home page
      navigate('/', { replace: true });

      // Show success toast
      toast({
        title: t('toasts.signedOutTitle'),
        description: t('toasts.signedOutDesc'),
      });
      
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: t('toasts.signoutFailed'),
        description: t('toasts.signoutError'),
        variant: 'destructive',
      });
    }
  };

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Enhanced Header with improved glassmorphism */}
      <div className="p-4 bg-[#001B23]/80 backdrop-blur-lg shadow-lg shadow-black/20">
        <div className="flex items-center justify-between">
          <h2 className="heading-md flex items-center gap-2">
            <User className="h-10 w-10 text-gradient" />
            {t('accountSidebar.myAccount')}
          </h2>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose} className="md:hidden">
              <X className="h-10 w-10" />
            </Button>
          )}
        </div>
        <p className="body-sm text-muted-foreground mt-1">{t('accountSidebar.manageProfile')}</p>
      </div>

      {/* Enhanced menu items with improved glass cards */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3 custom-scrollbar">
        {menuItems.map((item, index) => {
          const isActive = location.pathname === item.path;
          return (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <Card
                className={cn(
                  'bg-[#001B23]/60 backdrop-blur-lg shadow-xl shadow-black/30 rounded-xl cursor-pointer transition-all duration-200 hover:bg-[#001B23]/75 hover:shadow-2xl hover:shadow-black/35',
                  isActive && 'ring-2 ring-[#1DEBF6]/50 bg-[#001B23]/75 shadow-2xl shadow-black/35'
                )}
                onClick={() => navigate(item.path)}
              >
                <div className="p-4 flex items-center gap-3">
                  <AppIcon
                    name={item.icon}
                    alt={item.label}
                    className="h-10 w-10 text-gradient"
                  />
                  <h3 className="heading-sm">{item.label}</h3>
                </div>
              </Card>
            </motion.div>
          );
        })}

        <div className="mt-4 pt-4 border-t border-white/10">
          <Button
            variant="ghost"
            className="w-full justify-start h-12 rounded-lg text-foreground hover:bg-destructive/20"
            onClick={handleLogout}
          >
            <LogOut className="mr-3 h-6 w-6" />
            <span className="text-sm">{t('accountSidebar.logout')}</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
