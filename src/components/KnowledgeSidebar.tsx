
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

interface KnowledgeSection {
  id: string;
  title: string;
  description: string;
  icon: string;
}

interface KnowledgeSidebarProps {
  sections: KnowledgeSection[];
  selectedSection: string | null;
  onSelectSection: (sectionId: string) => void;
  onClose?: () => void;
  className?: string;
}

export function KnowledgeSidebar({
  sections,
  selectedSection,
  onSelectSection,
  onClose,
  className
}: KnowledgeSidebarProps) {
  const { t } = useTranslation();
  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="p-4 glass-header border-b border-border/50">
        <div className="flex items-center justify-between">
          <h2 className="heading-md flex items-center gap-2">
            <AppIcon name="Upload" alt="Upload" className="h-10 w-10 text-primary" />
            {t('knowledgeSidebar.title')}
          </h2>
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="md:hidden"
            >
              <X className="h-10 w-10" />
            </Button>
          )}
        </div>
        <p className="body-sm text-muted-foreground mt-1">
          {t('knowledgeSidebar.description')}
        </p>
      </div>

      {/* Sections List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3 custom-scrollbar">
        {sections.map((section, index) => (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <Card
              className={cn(
                "glass-card cursor-pointer transition-all duration-200 hover:glass-modal",
                selectedSection === section.id && "ring-2 ring-primary/50 glass-modal"
              )}
              onClick={() => onSelectSection(section.id)}
            >
              <div className="p-4">
                <div className="flex items-center gap-3 mb-2">
                  <AppIcon name={section.icon} alt={t(`knowledgeSidebar.sections.${section.id}`)} className="h-10 w-10 text-primary" />
                  <h3 className="heading-sm">
                    {t(`knowledgeSidebar.sections.${section.id}`)}
                  </h3>
                </div>

                <p className="body-sm text-muted-foreground">
                  {t(`knowledgeSidebar.sections.${section.id}Desc`)}
                </p>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
