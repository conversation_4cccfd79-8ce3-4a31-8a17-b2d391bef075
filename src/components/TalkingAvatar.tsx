
import { useEffect } from 'react';
import { loadModelViewer } from './AvatarViewer';

interface TalkingAvatarProps {
  isPlaying: boolean;
  audioRef: React.RefObject<HTMLAudioElement>;
}

export function TalkingAvatar({ isPlaying, audioRef }: TalkingAvatarProps) {
  useEffect(() => {
    loadModelViewer();
  }, []);
  
  return (
    <model-viewer
      src="/assets/avatar.glb"
      camera-controls
      auto-rotate
      ar
      loading="lazy"
      reveal="interaction"
      style={{ width: '100%', height: '100%' }}
    ></model-viewer>
  );
}

export function FallbackAvatar({ isPlaying }: { isPlaying: boolean }) {
  useEffect(() => {
    loadModelViewer();
  }, []);
  
  return (
    <model-viewer
      src="/assets/avatar.glb"
      camera-controls
      auto-rotate
      ar
      loading="lazy"
      reveal="interaction"
      style={{ width: '100%', height: '100%' }}
    ></model-viewer>
  );
}
