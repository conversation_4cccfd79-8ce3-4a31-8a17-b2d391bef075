
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Coins, Lock, Check } from 'lucide-react';

interface Room {
  id: string;
  name: string;
  description: string;
  modelUrl: string;
  price: number;
  isFree: boolean;
  isUnlocked: boolean;
  category: 'nature' | 'urban' | 'fantasy' | 'space';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

interface RoomCardProps {
  room: Room;
  isSelected: boolean;
  onSelect: () => void;
  userTokens: number;
}

export function RoomCard({ room, isSelected, onSelect, userTokens }: RoomCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [modelError, setModelError] = useState(false);
  
  const canAfford = userTokens >= room.price;
  const canUnlock = room.isFree || canAfford;

  // Load model-viewer component
  useEffect(() => {
    if (!customElements.get('model-viewer')) {
      import('@google/model-viewer');
    }
  }, []);


  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card
        className={`glass-panel hover:glass-button transition-all duration-300 cursor-pointer overflow-hidden rounded-3xl ${
          isSelected ? 'ring-2 ring-primary ring-offset-2 ring-offset-background' : ''
        }`}
        onClick={onSelect}
      >
        <CardContent className="p-0">
          {/* 3D Model Preview */}
          <div className="relative h-48 bg-gradient-to-br from-background/50 to-primary/20 overflow-hidden">
            {!modelError ? (
              <model-viewer
                src={room.modelUrl}
                auto-rotate
                camera-controls
                loading="lazy"
                style={{ 
                  width: '100%', 
                  height: '100%',
                  background: 'transparent'
                }}
                onError={() => setModelError(true)}
              ></model-viewer>
            ) : (
              <div className="flex items-center justify-center h-full bg-gradient-to-br from-primary/20 to-secondary/20">
                <div className="text-center">
                  <div className="text-4xl mb-2">🏠</div>
                  <div className="text-sm text-muted-foreground">Preview Unavailable</div>
                </div>
              </div>
            )}
            

            {/* Lock/Price Overlay */}
            {!room.isUnlocked && (
              <div className="absolute inset-0 bg-black/40 backdrop-blur-sm flex items-center justify-center">
                <div className="text-center text-white">
                  {room.isFree ? (
                    <Lock className="h-8 w-8 mx-auto mb-2" />
                  ) : (
                    <div className="flex items-center gap-2 glass-panel px-3 py-2 rounded-xl">
                      <Coins className="h-5 w-5 text-yellow-400" />
                      <span className="font-bold text-yellow-400">{room.price}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Hover Preview Controls */}
            {isHovered && room.isUnlocked && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="absolute bottom-3 right-3"
              >
                <Badge className="glass-panel backdrop-blur-xl text-xs">
                  Drag to explore
                </Badge>
              </motion.div>
            )}
          </div>

          {/* Room Info */}
          <div className="p-4">
            <div className="flex items-start justify-between mb-2">
              <div>
                <h3 className="font-semibold text-lg">{room.name}</h3>
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {room.description}
                </p>
              </div>
              {isSelected && (
                <div className="ml-2">
                  <div className="w-6 h-6 rounded-full bg-primary flex items-center justify-center">
                    <Check className="h-4 w-4 text-primary-foreground" />
                  </div>
                </div>
              )}
            </div>

            {!room.isUnlocked && (
              <div className="flex items-center gap-2 justify-end">
                {room.isFree ? (
                  <span className="text-sm text-green-400 font-medium">FREE</span>
                ) : (
                  <div className="flex items-center gap-1">
                    <Coins className="h-4 w-4 text-yellow-400" />
                    <span className={`text-sm font-medium ${canAfford ? 'text-green-400' : 'text-red-400'}`}>{room.price}</span>
                  </div>
                )}
              </div>
            )}

            {/* Action Button */}
            {!room.isUnlocked && (
              <Button
                className={`w-full mt-3 ${
                  canUnlock 
                    ? 'brand-gradient hover:opacity-90' 
                    : 'bg-muted text-muted-foreground cursor-not-allowed'
                }`}
                disabled={!canUnlock}
                onClick={(e) => {
                  e.stopPropagation();
                  if (canUnlock) {
                    // Handle unlock logic
                    console.log('Unlocking room:', room.id);
                  }
                }}
              >
                {room.isFree ? 'Unlock Free' : `Unlock for ${room.price} $HEEY`}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
