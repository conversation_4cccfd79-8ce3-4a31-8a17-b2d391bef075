
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { TikTokBackground } from '@/components/TikTokBackground';
import { RoomCard } from '@/components/RoomCard';
import { ModernWalletCard } from '@/components/wallet/ModernWalletCard';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/hooks/use-toast';

interface Room {
  id: string;
  name: string;
  description: string;
  modelUrl: string;
  price: number;
  isFree: boolean;
  isUnlocked: boolean;
  category: 'nature' | 'urban' | 'fantasy' | 'space';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

const mockRooms: Room[] = [
  {
    id: '1',
    name: 'Snow Village',
    description: 'A peaceful winter wonderland with cozy cottages',
    modelUrl: '/assets/models/snow_village.glb',
    price: 0,
    isFree: true,
    isUnlocked: true,
    category: 'nature',
    rarity: 'common'
  },
  {
    id: '2',
    name: 'Cyberpunk City',
    description: 'Neon-lit futuristic cityscape with flying cars',
    modelUrl: '/assets/models/cyberpunk_city.glb',
    price: 100,
    isFree: false,
    isUnlocked: false,
    category: 'urban',
    rarity: 'rare'
  },
  {
    id: '3',
    name: 'Crystal Cave',
    description: 'Mystical underground cavern filled with gems',
    modelUrl: '/assets/models/crystal_cave.glb',
    price: 250,
    isFree: false,
    isUnlocked: false,
    category: 'fantasy',
    rarity: 'epic'
  },
  {
    id: '4',
    name: 'Space Station',
    description: 'Advanced orbital platform among the stars',
    modelUrl: '/assets/models/space_station.glb',
    price: 500,
    isFree: false,
    isUnlocked: false,
    category: 'space',
    rarity: 'legendary'
  },
  {
    id: '5',
    name: 'Forest Temple',
    description: 'Ancient ruins overgrown with mystical vegetation',
    modelUrl: '/assets/models/forest_temple.glb',
    price: 150,
    isFree: false,
    isUnlocked: false,
    category: 'nature',
    rarity: 'rare'
  },
  {
    id: '6',
    name: 'Beach Paradise',
    description: 'Tropical beach with crystal clear waters',
    modelUrl: '/assets/models/beach_paradise.glb',
    price: 75,
    isFree: false,
    isUnlocked: false,
    category: 'nature',
    rarity: 'common'
  }
];

export function RoomSelectionPanel() {
  const [selectedRoom, setSelectedRoom] = useState<string>('1');
  const [userTokens] = useState(320); // Mock user token balance
  const { t } = useTranslation();
  const { toast } = useToast();

  const handleDeposit = () => {
    toast({
      title: "Deposit Feature",
      description: "Deposit functionality will be implemented soon.",
    });
  };

  const handleSend = () => {
    toast({
      title: "Send Feature", 
      description: "Send functionality will be implemented soon.",
    });
  };

  return (
    <div className="relative min-h-screen mobile-full-screen">
      {/* TikTok Background */}
      <TikTokBackground />
      
      <div className="relative z-10 p-6">
        {/* Modern Wallet Card */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <ModernWalletCard
            tokenSymbol="HEEY"
            tokenBalance={userTokens}
            usdEquivalent={userTokens * 0.15} // Mock USD conversion rate
            onDeposit={handleDeposit}
            onSend={handleSend}
          />
        </motion.div>

        {/* Room Grid */}
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 gap-4"
        >
          {mockRooms.map((room, index) => (
            <motion.div
              key={room.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
            >
              <RoomCard
                room={room}
                isSelected={selectedRoom === room.id}
                onSelect={() => setSelectedRoom(room.id)}
                userTokens={userTokens}
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Selected Room Actions */}
        {selectedRoom && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="fixed bottom-6 left-6 right-6 z-20"
          >
            <div className="glass-panel p-4 rounded-2xl border border-white/20 backdrop-blur-xl">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold">
                    {mockRooms.find(r => r.id === selectedRoom)?.name}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Selected Room
                  </p>
                </div>
                <Button className="brand-gradient hover:opacity-90">
                  Apply Room
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
