
import { motion } from 'framer-motion';
import { User, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AvatarPreview } from './AvatarPreview';
import { useNavigate } from 'react-router-dom';
import { useAvatarManager } from '@/hooks/useAvatarManager';

interface AvatarManagerProps {
  readonly?: boolean;
}

export function AvatarManager({ readonly = false }: AvatarManagerProps) {
  const navigate = useNavigate();
  const { 
    avatarData, 
    isLoading, 
    deleteAvatar, 
    hasAvatar 
  } = useAvatarManager();

  const handleOpenCreator = () => {
    console.log('Redirecting to avatar creation page...');
    navigate('/avatar');
  };

  const handleDeleteAvatar = () => {
    if (window.confirm('Are you sure you want to delete your avatar?')) {
      deleteAvatar();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6 }}
      className="glass-container h-full flex flex-col items-center justify-center relative overflow-hidden p-4 sm:p-6"
    >
      {/* Avatar Display */}
      <div className="flex-1 flex items-center justify-center w-full">
        {hasAvatar && avatarData?.url ? (
          <div className="w-full h-64 sm:h-80 md:h-96 relative">
            <AvatarPreview avatarUrl={avatarData.url} />
          </div>
        ) : (
          <div className="relative">
            <motion.div
              animate={{ rotateY: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="w-40 h-40 sm:w-48 sm:h-48 glass-panel rounded-full flex items-center justify-center relative"
            >
              <User className="w-16 h-16 sm:w-20 sm:h-20 text-primary" />
              <div className="absolute inset-0 brand-gradient opacity-20 rounded-full" />
            </motion.div>
            
            {/* Floating particles */}
            <motion.div
              animate={{
                y: [-10, 10, -10],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{ duration: 3, repeat: Infinity }}
              className="absolute -top-2 -right-2 w-4 h-4 bg-primary/60 rounded-full"
            />
            <motion.div
              animate={{
                y: [10, -10, 10],
                opacity: [0.3, 0.8, 0.3]
              }}
              transition={{ duration: 2.5, repeat: Infinity, delay: 0.5 }}
              className="absolute -bottom-1 -left-3 w-3 h-3 bg-primary/40 rounded-full"
            />
          </div>
        )}
      </div>

      {/* Avatar Metadata */}
      {hasAvatar && avatarData?.metadata && !readonly && (
        <div className="mb-4 text-center">
          <p className="text-xs text-muted-foreground">
            Created: {new Date(avatarData.metadata.createdAt).toLocaleDateString()}
          </p>
          {avatarData.metadata.fileSize && (
            <p className="text-xs text-muted-foreground">
              Size: {(avatarData.metadata.fileSize / 1024 / 1024).toFixed(1)} MB
            </p>
          )}
        </div>
      )}

      {/* Controls */}
      {!readonly && (
        <div className="mt-4 sm:mt-6 space-y-3 w-full max-w-xs">
          <Button
            onClick={handleOpenCreator}
            disabled={isLoading}
            className="w-full brand-gradient hover:opacity-90 text-white flex items-center justify-center space-x-2"
          >
            <Settings className="w-4 h-4" />
            <span className="text-sm">{hasAvatar ? 'Edit Avatar' : 'Create Avatar'}</span>
          </Button>
          
          <p className="text-xs text-muted-foreground text-center">
            {hasAvatar 
              ? 'Click to edit your 3D avatar' 
              : 'Create your personalized 3D avatar'
            }
          </p>
        </div>
      )}

      {/* Readonly indicator */}
      {readonly && (
        <div className="mt-4">
          <p className="text-sm text-muted-foreground text-center">
            {hasAvatar ? '3D Avatar' : 'Avatar Preview'}
          </p>
        </div>
      )}
    </motion.div>
  );
}
