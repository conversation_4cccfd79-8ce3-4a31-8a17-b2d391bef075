
import { motion } from 'framer-motion';
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { MessageCircle, Send, Bot } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { createAIService, defaultAIConfig } from '@/lib/aiService';
import { chatService } from '@/lib/chatService';

export function ChatPersonalization() {
  const [personality, setPersonality] = useState('');
  const [greeting, setGreeting] = useState('');
  const [testMessage, setTestMessage] = useState('');
  const [chatMessages, setChatMessages] = useState([
    { role: 'assistant', content: 'Hi! I\'m your AI assistant. Ask me anything!' }
  ]);
  const { t } = useTranslation();

  const handleTestChat = async () => {
    if (!testMessage.trim()) return;

    const userMessage = { role: 'user', content: testMessage };
    setChatMessages(prev => [...prev, userMessage]);

    const ai = createAIService({
      ...defaultAIConfig,
      personality: {
        ...defaultAIConfig.personality,
        style: 'conversational',
        tone: 'friendly',
        response_length: 'short',
        traits: ['helpful', 'engaging'],
      }
    });

    // Minimal mock context for testing locally
    const ctxResult = await chatService.buildChatContext('test_session');
    const context = ctxResult.success && ctxResult.data ? ctxResult.data : {
      recent_messages: [],
      user_profile: { name: 'You' },
      conversation_history: { topics: [], sentiment_trend: 0, key_points: [] },
      session_info: { duration_minutes: 0, message_count: 0, last_interaction: new Date().toISOString() }
    } as any;

    const aiResult = await ai.generateResponse(testMessage, context);
    const reply = aiResult.success && aiResult.data ? aiResult.data.content : `Thanks for testing! You said: "${testMessage}".`;

    setChatMessages(prev => [...prev, { role: 'assistant', content: reply }]);
    setTestMessage('');
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
      className="h-full"
    >
      <Card className="glass-panel h-full">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bot className="w-5 h-5" />
            <span>{t('chatPersonalization.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Personality Settings */}
          <div className="space-y-2">
            <Label htmlFor="personality">{t('chatPersonalization.personalityLabel')}</Label>
            <Textarea
              id="personality"
              placeholder={t('chatPersonalization.personalityPlaceholder')}
              value={personality}
              onChange={(e) => setPersonality(e.target.value)}
              className="glass-button resize-none"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="greeting">{t('chatPersonalization.greetingLabel')}</Label>
            <Input
              id="greeting"
              placeholder={t('chatPersonalization.greetingPlaceholder')}
              value={greeting}
              onChange={(e) => setGreeting(e.target.value)}
              className="glass-button"
            />
          </div>

          {/* Test Chat */}
          <div className="border-t border-white/10 pt-4">
            <h4 className="font-medium mb-3 flex items-center space-x-2">
              <MessageCircle className="w-4 h-4" />
              <span>{t('chatPersonalization.testTitle')}</span>
            </h4>
            
            <div className="glass-button rounded-lg p-3 h-32 overflow-y-auto space-y-2 mb-3">
              {chatMessages.map((message, index) => (
                <div
                  key={index}
                  className={`text-sm p-2 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-primary/20 text-right ml-4'
                      : 'bg-white/10 mr-4'
                  }`}
                >
                  {message.content}
                </div>
              ))}
            </div>

            <div className="flex space-x-2">
              <Input
                placeholder={t('chatPersonalization.testPlaceholder')}
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleTestChat()}
                className="glass-button flex-1"
              />
              <Button
                onClick={handleTestChat}
                size="sm"
                className="brand-gradient hover:opacity-90 text-white"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
