
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

// Dynamically import model-viewer to avoid loading the heavy library
// until this component is mounted. This improves initial load time
// and ensures the custom element is defined before use on mobile.
export const loadModelViewer = () => import('@google/model-viewer');

interface AvatarViewerProps {
  avatarUrl: string;
  className?: string;
}

export function AvatarViewer({ avatarUrl, className = '' }: AvatarViewerProps) {
  const { t } = useTranslation();

  // Load the model-viewer library when the component mounts. This is
  // necessary because the custom element is only defined after the
  // script executes. Loading it lazily helps performance on mobile
  // devices and avoids issues where the element isn't registered.
  useEffect(() => {
    loadModelViewer();
  }, []);

  if (!avatarUrl) {
    return (
      <div className={`w-full h-full flex items-center justify-center bg-muted rounded-lg ${className}`}>
        <p className="text-muted-foreground">{t('avatarViewer.noAvatar')}</p>
      </div>
    );
  }

  return (
    <div className={`w-full rounded-lg overflow-hidden ${className}`}>
      <model-viewer
        src={avatarUrl}
        alt="3D Avatar"
        camera-controls
        auto-rotate
        loading="lazy"
        reveal="interaction"
        style={{
          width: '100%',
          height: 'auto',
          backgroundColor: 'transparent',
          minHeight: '200px', // Ensure minimum height for mobile
          touchAction: 'pan-y', // Allow vertical scrolling on mobile
        }}
      ></model-viewer>
    </div>
  );
}
