import React, { CSSProperties, useMemo } from 'react';

const coinIcons = [
  '/3D-coin/heycoin-gold.webp',
  '/3D-coin/heycoin-logo.webp',
  '/3D-coin/heycoin-purple.webp',
  '/3D-coin/heycoin-silver.webp',
  '/3D-coin/heycoin-turquoise.webp',
];

interface LayerItem {
  style: CSSProperties;
  className: string;
  src?: string;
}

function random(min: number, max: number) {
  return Math.random() * (max - min) + min;
}

function randomItem<T>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)];
}

function generateShapes(count: number): LayerItem[] {
  return Array.from({ length: count }).map(() => {
    const size = random(8, 18);
    const hue = random(180, 240);
    const style: CSSProperties = {
      width: `${size}rem`,
      height: `${size}rem`,
      top: `${random(-10, 90)}%`,
      left: `${random(-10, 90)}%`,
      opacity: random(0.05, 0.15),
      filter: `blur(${random(20, 40)}px)`,
      background: `linear-gradient(${random(0, 360)}deg, hsl(${hue},70%,55%), hsl(${
        hue
      },70%,45%))`,
      borderRadius: '9999px',
      willChange: 'transform, opacity',
    };
    return {
      style,
      className: 'animate-float-up-down animate-pulse-slow',
    };
  });
}

function generateIcons(count: number): LayerItem[] {
  return Array.from({ length: count }).map(() => {
    const sizeOptions = [
      random(1, 2), // small
      random(2, 3), // medium
      random(3, 4), // large
    ];
    const size = randomItem(sizeOptions);
    const style: CSSProperties = {
      width: `${size}rem`,
      height: `${size}rem`,
      top: `${random(0, 90)}%`,
      left: `${random(0, 90)}%`,
      opacity: random(0.3, 0.7),
      filter: `blur(${random(2, 4)}px)`,
      willChange: 'transform, opacity',
    };
    return {
      src: randomItem(coinIcons),
      style,
      className: 'animate-float-around animate-spin-slow',
    };
  });
}

function BackgroundLayer() {
  const shapes = useMemo(() => generateShapes(5), []);
  const icons = useMemo(() => generateIcons(5), []);

  return (
    <>
      {shapes.map((item, idx) => (
        <div
          key={`shape-${idx}`}
          className={`absolute ${item.className}`}
          style={item.style}
        />
      ))}
      {icons.map((item, idx) => (
        <img
          key={`icon-${idx}`}
          loading="lazy"
          src={item.src}
          className={`absolute object-contain ${item.className}`}
          style={item.style}
          alt=""
        />
      ))}
    </>
  );
}

export function TikTokBackground() {
  return (
    <div className="fixed inset-0 z-0 overflow-hidden pointer-events-none">
      <div className="absolute inset-0 bg-gradient-to-br from-[#001B23] to-[#031116]" />
      <BackgroundLayer />
    </div>
  );
}
