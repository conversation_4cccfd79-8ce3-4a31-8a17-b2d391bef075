
import { motion } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Twitter, Instagram, Linkedin, Github, Globe } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface SocialLinks {
  twitter: string;
  instagram: string;
  linkedin: string;
  github: string;
  website: string;
}

interface SocialLinksPanelProps {
  socialLinks: SocialLinks;
  onChange: (links: SocialLinks) => void;
}

export function SocialLinksPanel({ socialLinks, onChange }: SocialLinksPanelProps) {
  const { t } = useTranslation();
  const handleInputChange = (platform: keyof SocialLinks, value: string) => {
    onChange({
      ...socialLinks,
      [platform]: value
    });
  };

  const socialPlatforms = [
    { key: 'twitter' as keyof SocialLinks, label: t('socialLinksPanel.twitter'), icon: Twitter, placeholder: t('socialLinksPanel.urlPlaceholder.twitter') },
    { key: 'instagram' as keyof SocialLinks, label: t('socialLinksPanel.instagram'), icon: Instagram, placeholder: t('socialLinksPanel.urlPlaceholder.instagram') },
    { key: 'linkedin' as keyof SocialLinks, label: t('socialLinksPanel.linkedin'), icon: Linkedin, placeholder: t('socialLinksPanel.urlPlaceholder.linkedin') },
    { key: 'github' as keyof SocialLinks, label: t('socialLinksPanel.github'), icon: Github, placeholder: t('socialLinksPanel.urlPlaceholder.github') },
    { key: 'website' as keyof SocialLinks, label: t('socialLinksPanel.website'), icon: Globe, placeholder: t('socialLinksPanel.urlPlaceholder.website') },
  ];

  return (
    <div className="space-y-6">
      {socialPlatforms.map(({ key, label, icon: Icon, placeholder }) => (
        <div key={key} className="space-y-3">
          <Label htmlFor={key} className="body-md font-medium flex items-center gap-3">
            <Icon className="w-5 h-5 text-primary" />
            <span>{label}</span>
          </Label>
          <Input
            id={key}
            type="url"
            placeholder={placeholder}
            value={socialLinks[key]}
            onChange={(e) => handleInputChange(key, e.target.value)}
            className="glass-button h-12 text-base"
          />
        </div>
      ))}
      
      <div className="mt-8 p-4 glass-button rounded-2xl">
        <h4 className="body-lg font-medium mb-2">{t('socialLinksPanel.preview')}</h4>
        <p className="body-md text-muted-foreground">
          {t('socialLinksPanel.previewDescription')}
        </p>
      </div>
    </div>
  );
}
