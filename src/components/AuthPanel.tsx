
import { motion } from 'framer-motion';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Chrome, Loader2 } from 'lucide-react';
import { Logo } from './Logo';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { useRTL } from '@/hooks/useRTL';

interface AuthPanelProps {
  mode: 'login' | 'signup';
  onToggleMode: () => void;
  onSubmit: (email: string, password: string) => void;
}

export function AuthPanel({ mode, onToggleMode, onSubmit }: AuthPanelProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { signInWithProvider } = useAuth();
  const { t } = useTranslation();
  const { isRTL } = useRTL();

  const handleGoogleSignIn = async () => {
    setIsLoading(true);

    try {
      const { error } = await signInWithProvider('google');
      
      if (!error) {
        console.log('Google sign in successful');
      }
    } catch (error) {
      console.error('Google sign in failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className={`glass-panel rounded-2xl p-8 w-full max-w-md mx-4 relative overflow-hidden ${isRTL ? 'rtl' : ''}`}
    >
      <div className="absolute inset-0 bg-glass-gradient rounded-2xl" />
      
      <div className="relative z-10">
        <div className="text-center mb-8">
          <Logo size="md" />
          <motion.p 
            className="text-muted-foreground mt-4 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {t('auth.welcomeDescription')}
          </motion.p>
        </div>

        <div className="space-y-6">
          <div className="text-center">
            <p className="text-sm text-muted-foreground/80 mb-6">
              {t('auth.welcomeSubtext')}
            </p>
          </div>

          <Button 
            onClick={handleGoogleSignIn}
            disabled={isLoading}
            className={`w-full bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 flex items-center justify-center space-x-3 py-6 text-base font-medium shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] ${isRTL ? 'space-x-reverse' : ''}`}
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Chrome className="w-5 h-5" />
            )}
            <span>
              {isLoading ? t('auth.signingIn') : t('auth.signInWithGoogle')}
            </span>
          </Button>

          <p className="text-xs text-muted-foreground text-center">
            {t('auth.termsText')}{' '}
            <a href="#" className="underline hover:text-primary">{t('auth.termsOfService')}</a>
            {' '}{t('auth.and')}{' '}
            <a href="#" className="underline hover:text-primary">{t('auth.privacyPolicy')}</a>
          </p>
        </div>
      </div>
    </motion.div>
  );
}
