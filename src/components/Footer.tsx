import { motion } from 'framer-motion';
import { useLocation, useNavigate } from 'react-router-dom';
import { Logo } from '@/components/Logo';
import { Github, Twitter, MessageCircle, Mail, Heart } from 'lucide-react';
import { AppIcon } from '@/components/AppIcon';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import { useTranslation } from 'react-i18next';

function HomepageFooter() {
  const { t } = useTranslation();
  const socialLinks = [
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: MessageCircle, href: '#', label: 'Discord' },
    { icon: Github, href: '#', label: 'GitHub' },
    { icon: Mail, href: '#', label: 'Email' }
  ];

  const footerLinks = [
    {
      title: 'The Good Stuff',
      links: [
        { name: '<PERSON>', href: '#' },
        { name: '<PERSON><PERSON><PERSON>', href: '#' },
        { name: 'API', href: '#' },
        { name: 'What\'s New', href: '#' }
      ]
    },
    {
      title: 'About Us',
      links: [
        { name: 'Our Story', href: '#' },
        { name: 'Blog', href: '#' },
        { name: 'Join the Team', href: '#' },
        { name: 'Say Hi', href: '#' }
      ]
    },
    {
      title: 'Get Help',
      links: [
        { name: 'How It Works', href: '#' },
        { name: 'Help Center', href: '#' },
        { name: 'Community Vibes', href: '#' },
        { name: 'Status Check', href: '#' }
      ]
    },
    {
      title: 'The Fine Print',
      links: [
        { name: 'Privacy', href: '#' },
        { name: 'Terms', href: '#' },
        { name: 'Security', href: '#' },
        { name: 'Cookies', href: '#' }
      ]
    }
  ];

  return (
    <motion.footer
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.5 }}
      className="glass-footer mt-auto border-t border-white/10 overflow-hidden h-auto relative z-50"
    >
      <div className="mobile-container flex flex-col space-y-4 sm:space-y-6 md:space-y-8 lg:space-y-10 py-4 sm:py-6 md:py-8 lg:py-10 safe-area-bottom bg-background/20">
        {/* Brand and Social */}
        <div className="flex flex-col items-center text-center space-y-2 sm:space-y-3">
          <Logo size="md" />
          <p className="body-md text-muted-foreground max-w-sm">
            {t('footer.tagline')}
          </p>

          <div className="flex items-center justify-center space-x-2 sm:space-x-3">
            {socialLinks.map((social, index) => {
              const Icon = social.icon;
              return (
                <motion.a
                  key={index}
                  href={social.href}
                  className="glass-button p-1.5 sm:p-2 rounded-xl hover:bg-background/30 transition-all duration-200 group"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  aria-label={social.label}
                >
                  <Icon className="w-6 h-6 sm:w-8 sm:h-8 text-muted-foreground group-hover:text-primary transition-colors" />
                </motion.a>
              );
            })}
          </div>
        </div>

        {/* Footer Links Desktop */}
        <div className="hidden sm:grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6">
          {footerLinks.map((section, index) => (
            <div key={index} className="space-y-2 sm:space-y-3">
              <h4 className="heading-sm text-foreground">{section.title}</h4>
              <ul className="space-y-1 sm:space-y-2">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a
                      href={link.href}
                      className="body-sm text-muted-foreground hover:text-primary transition-colors duration-200 hover:underline"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Footer Links Mobile Accordion */}
        <div className="sm:hidden">
          <Accordion type="multiple" className="w-full">
            {footerLinks.map((section, index) => (
              <AccordionItem key={index} value={section.title}>
                <AccordionTrigger className="text-foreground body-md">
                  {section.title}
                </AccordionTrigger>
                <AccordionContent>
                  <ul className="space-y-2 pl-2">
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <a
                          href={link.href}
                          className="body-sm text-muted-foreground hover:text-primary transition-colors duration-200 hover:underline"
                        >
                          {link.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>

        {/* Newsletter Signup */}
        <motion.div
          className="glass-panel p-3 sm:p-4 rounded-2xl mb-3 sm:mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <div className="flex flex-col md:flex-row items-center justify-between space-y-2 sm:space-y-3 md:space-y-0 md:space-x-4">
            <div className="text-center md:text-left">
              <h4 className="heading-sm mb-1">{t('footer.newsletterTitle')}</h4>
              <p className="body-sm text-muted-foreground">
                {t('footer.newsletterDesc')}
              </p>
            </div>
            <div className="flex w-full md:w-auto space-x-2">
              <input
                type="email"
                placeholder={t('footer.newsletterPlaceholder')}
                className="flex-1 md:w-64 px-3 py-2 bg-background/20 border border-white/10 rounded-xl text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all"
              />
              <button className="btn-primary px-4 py-2 rounded-xl text-sm font-medium hover:scale-105 transition-transform">
                {t('footer.newsletterCta')}
              </button>
            </div>
          </div>
        </motion.div>

        {/* Bottom Bar */}
        <div className="flex flex-col md:flex-row items-center justify-between pt-3 sm:pt-4 border-t border-white/10 space-y-2 sm:space-y-3 md:space-y-0">
          <div className="flex items-center space-x-2 text-muted-foreground body-sm">
            <span>{t('footer.madeWith')}</span>
            <Heart className="w-4 h-4 text-red-500 fill-current" />
            <span>{t('footer.forEnergy')}</span>
          </div>

          <p className="body-sm text-muted-foreground">
            {t('footer.copyright')}
          </p>
        </div>
      </div>
    </motion.footer>
  );
}

function AppFooter() {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const mobileMenuItems = [
    { name: t('navigation.topics'), path: '/topics', icon: 'Message' },
    { name: t('navigation.knowledge'), path: '/knowledge', icon: 'Lamp' },
    { name: t('navigation.avatar'), path: '/avatar', icon: 'Person' },
    { name: t('navigation.bioLink'), path: '/bio-link', icon: 'Send' },
    { name: t('navigation.rewards'), path: '/rewards', icon: 'Medal' }
  ];

  return (
    <footer className="fixed bottom-0 left-0 right-0 z-50 bg-background/30 border-t border-white/10 overflow-hidden h-auto">
      <div className="bg-background/30 py-4 safe-area-bottom">
        <div className="flex items-end justify-center px-4">
          <div className="grid grid-cols-5 gap-1 w-full">
            {mobileMenuItems.map((item) => {
              const isActive = location.pathname === item.path;

              return (
                <motion.button
                  key={item.path}
                  onClick={() => navigate(item.path)}
                  className={`flex flex-col items-center justify-center rounded-lg transition-colors py-2 ${
                    isActive
                      ? 'text-primary'
                      : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                  }`}
                  whileTap={{ scale: 0.95 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <motion.div
                    animate={{ scale: isActive ? 1.1 : 1, y: isActive ? -1 : 0 }}
                    transition={{ duration: 0.2 }}
                    className="mb-1"
                  >
                    <AppIcon name={item.icon} alt={item.name} className="w-10 h-10" />
                  </motion.div>
                  <span className="text-xs font-medium leading-tight whitespace-nowrap text-center">
                    {item.name}
                  </span>
                </motion.button>
              );
            })}
          </div>
        </div>
      </div>
    </footer>
  );
}

export function Footer() {
  const { pathname } = useLocation();
  return pathname === '/' ? <HomepageFooter /> : <AppFooter />;
}
