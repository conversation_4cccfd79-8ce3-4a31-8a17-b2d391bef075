// Sketchfab Model Browser Component
// Browse and select 3D models from Sketchfab for room environments

import React, { useState, useEffect, useCallback } from 'react';
import { Search, Filter, Grid, List, Download, ExternalLink, Star, Eye } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { sketchfabService, type SketchfabModel, type SketchfabSearchOptions } from '@/lib/sketchfabService';
import type { Room3D } from '@/types/avatar';

interface SketchfabModelBrowserProps {
  onModelSelect: (model: SketchfabModel) => void;
  onRoomCreate: (room: Partial<Room3D>) => void;
  selectedCategory?: string;
  className?: string;
}

interface FilterState {
  category: string;
  downloadable: boolean;
  animated: boolean;
  rigged: boolean;
  maxFaceCount: number;
  sortBy: 'relevance' | 'recent' | 'popular' | 'likes';
}

const CATEGORIES = [
  { value: 'all', label: 'All Categories' },
  { value: 'architecture', label: 'Architecture' },
  { value: 'places-travel', label: 'Places & Travel' },
  { value: 'nature-plants', label: 'Nature & Plants' },
  { value: 'furniture-home', label: 'Furniture & Home' },
  { value: 'science-technology', label: 'Science & Technology' },
  { value: 'art-abstract', label: 'Art & Abstract' }
];

const FACE_COUNT_OPTIONS = [
  { value: 50000, label: '50K faces (Web optimized)' },
  { value: 100000, label: '100K faces (High quality)' },
  { value: 200000, label: '200K faces (Very detailed)' },
  { value: 999999, label: 'No limit' }
];

export const SketchfabModelBrowser: React.FC<SketchfabModelBrowserProps> = ({
  onModelSelect,
  onRoomCreate,
  selectedCategory = 'all',
  className = ''
}) => {
  const [models, setModels] = useState<SketchfabModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [nextCursor, setNextCursor] = useState<string | undefined>();

  const [filters, setFilters] = useState<FilterState>({
    category: selectedCategory,
    downloadable: true,
    animated: false,
    rigged: false,
    maxFaceCount: 50000,
    sortBy: 'popular'
  });

  // Search models
  const searchModels = useCallback(async (reset = false) => {
    setLoading(true);
    setError(null);

    try {
      const searchOptions: SketchfabSearchOptions = {
        query: searchQuery || undefined,
        categories: filters.category !== 'all' ? [filters.category] : undefined,
        downloadable: filters.downloadable,
        animated: filters.animated,
        rigged: filters.rigged,
        maxFaceCount: filters.maxFaceCount,
        sortBy: filters.sortBy,
        count: 24,
        cursor: reset ? undefined : nextCursor
      };

      const result = await sketchfabService.searchModels(searchOptions);

      if (result.success && result.data) {
        setModels(reset ? result.data : [...models, ...result.data]);
        setHasMore(result.pagination?.hasMore || false);
        // Note: Sketchfab uses cursor-based pagination, would need to extract from response
        setNextCursor(undefined); // Would be set from actual API response
      } else {
        setError(result.error?.message || 'Failed to search models');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
    } finally {
      setLoading(false);
    }
  }, [searchQuery, filters, models, nextCursor]);

  // Initial search
  useEffect(() => {
    searchModels(true);
  }, [searchQuery, filters]); // eslint-disable-line react-hooks/exhaustive-deps

  // Handle search input
  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value);
    setModels([]);
    setNextCursor(undefined);
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((key: keyof FilterState, value: unknown) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setModels([]);
    setNextCursor(undefined);
  }, []);

  // Load more models
  const loadMore = useCallback(() => {
    if (hasMore && !loading) {
      searchModels(false);
    }
  }, [hasMore, loading, searchModels]);

  // Handle model selection
  const handleModelSelect = useCallback((model: SketchfabModel) => {
    onModelSelect(model);
  }, [onModelSelect]);

  // Convert to room and add to user's rooms
  const handleAddToRooms = useCallback((model: SketchfabModel) => {
    const room = sketchfabService.convertToRoom(model, filters.category);
    onRoomCreate(room);
  }, [onRoomCreate, filters.category]);

  // Format numbers for display
  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900">3D Model Browser</h2>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
            <div className="flex border border-gray-200 rounded-md">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search for 3D models..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <Select
                  value={filters.category}
                  onValueChange={(value) => handleFilterChange('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {CATEGORIES.map(cat => (
                      <SelectItem key={cat.value} value={cat.value}>
                        {cat.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sort By
                </label>
                <Select
                  value={filters.sortBy}
                  onValueChange={(value) => handleFilterChange('sortBy', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="popular">Most Popular</SelectItem>
                    <SelectItem value="recent">Most Recent</SelectItem>
                    <SelectItem value="likes">Most Liked</SelectItem>
                    <SelectItem value="relevance">Most Relevant</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quality
                </label>
                <Select
                  value={filters.maxFaceCount.toString()}
                  onValueChange={(value) => handleFilterChange('maxFaceCount', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FACE_COUNT_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Options
                </label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="downloadable"
                      checked={filters.downloadable}
                      onCheckedChange={(checked) => handleFilterChange('downloadable', checked)}
                    />
                    <label htmlFor="downloadable" className="text-sm">
                      Downloadable only
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="animated"
                      checked={filters.animated}
                      onCheckedChange={(checked) => handleFilterChange('animated', checked)}
                    />
                    <label htmlFor="animated" className="text-sm">
                      Animated
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="rigged"
                      checked={filters.rigged}
                      onCheckedChange={(checked) => handleFilterChange('rigged', checked)}
                    />
                    <label htmlFor="rigged" className="text-sm">
                      Rigged
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
            <p className="text-red-800">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => searchModels(true)}
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        )}

        {loading && models.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">Searching models...</p>
            </div>
          </div>
        ) : (
          <>
            {/* Results Grid/List */}
            <div className={
              viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
            }>
              {models.map((model) => (
                <ModelCard
                  key={model.uid}
                  model={model}
                  viewMode={viewMode}
                  onSelect={handleModelSelect}
                  onAddToRooms={handleAddToRooms}
                />
              ))}
            </div>

            {/* Load More */}
            {hasMore && (
              <div className="mt-8 text-center">
                <Button
                  onClick={loadMore}
                  disabled={loading}
                  size="lg"
                >
                  {loading ? 'Loading...' : 'Load More Models'}
                </Button>
              </div>
            )}

            {/* No Results */}
            {models.length === 0 && !loading && (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <Search className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No models found</h3>
                <p className="text-gray-600">
                  Try adjusting your search terms or filters to find what you're looking for.
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

// Model Card Component
interface ModelCardProps {
  model: SketchfabModel;
  viewMode: 'grid' | 'list';
  onSelect: (model: SketchfabModel) => void;
  onAddToRooms: (model: SketchfabModel) => void;
}

const ModelCard: React.FC<ModelCardProps> = ({ model, viewMode, onSelect, onAddToRooms }) => {
  const thumbnailUrl = model.thumbnails.images.find(img => img.width >= 512)?.url ||
                      model.thumbnails.images[0]?.url;

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  if (viewMode === 'list') {
    return (
      <Card className="flex flex-row overflow-hidden hover:shadow-md transition-shadow">
        <div className="w-32 h-24 flex-shrink-0">
          <img
            src={thumbnailUrl}
            alt={model.name}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex-1 p-4">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-1 line-clamp-1">
                {model.name}
              </h3>
              <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                {model.description}
              </p>
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span className="flex items-center gap-1">
                  <Eye className="w-3 h-3" />
                  {formatNumber(model.viewCount)}
                </span>
                <span className="flex items-center gap-1">
                  <Star className="w-3 h-3" />
                  {formatNumber(model.likeCount)}
                </span>
                <span>by {model.user.displayName}</span>
              </div>
            </div>
            <div className="flex gap-2 ml-4">
              <Button
                size="sm"
                variant="outline"
                onClick={() => onSelect(model)}
              >
                Preview
              </Button>
              {model.isDownloadable && (
                <Button
                  size="sm"
                  onClick={() => onAddToRooms(model)}
                >
                  <Download className="w-4 h-4 mr-1" />
                  Add
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer group">
      <div className="aspect-square relative overflow-hidden">
        <img
          src={thumbnailUrl}
          alt={model.name}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
        />
        <div className="absolute top-2 right-2">
          {model.isDownloadable && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <Download className="w-3 h-3 mr-1" />
              Free
            </Badge>
          )}
        </div>
      </div>
      
      <CardHeader className="pb-2">
        <CardTitle className="text-sm line-clamp-2">
          {model.name}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="pb-2">
        <p className="text-xs text-gray-600 line-clamp-2 mb-2">
          {model.description}
        </p>
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span className="flex items-center gap-1">
            <Eye className="w-3 h-3" />
            {formatNumber(model.viewCount)}
          </span>
          <span className="flex items-center gap-1">
            <Star className="w-3 h-3" />
            {formatNumber(model.likeCount)}
          </span>
        </div>
      </CardContent>
      
      <CardFooter className="pt-0 flex gap-2">
        <Button
          size="sm"
          variant="outline"
          className="flex-1"
          onClick={() => onSelect(model)}
        >
          Preview
        </Button>
        {model.isDownloadable && (
          <Button
            size="sm"
            className="flex-1"
            onClick={() => onAddToRooms(model)}
          >
            Add to Rooms
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default SketchfabModelBrowser;