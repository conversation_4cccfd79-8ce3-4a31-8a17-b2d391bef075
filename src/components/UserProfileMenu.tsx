
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext';
import { useProfile } from '@/hooks/useProfile';
import { AuthModal } from '@/components/auth/AuthModal';
import { useNavigate } from 'react-router-dom';
import { DEFAULT_PROFILE_ICON } from '@/lib/profileIcon';
import { useTranslation } from 'react-i18next';

export function UserProfileMenu() {
  const { user, loading } = useAuth();
  const { profile, isLoading: profileLoading } = useProfile();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation();

  if (!user && !loading) {
    return (
      <>
        <Button
          onClick={() => setShowAuthModal(true)}
          className="brand-gradient hover:opacity-90"
        >
          {t('userProfile.login')}
        </Button>
        <AuthModal isOpen={showAuthModal} onClose={() => setShowAuthModal(false)} />
      </>
    );
  }

  const avatarSrc =
    profile?.avatar_thumbnail ||
    (user?.user_metadata as Record<string, unknown>)?.profile_icon ||
    DEFAULT_PROFILE_ICON;

  return (
    <Button
      variant="ghost"
      className="relative h-8 w-8 rounded-full"
      onClick={() => navigate('/my-account')}
    >
      <Avatar className="h-8 w-8">
        <AvatarImage
          src={avatarSrc}
          alt={profile?.display_name}
          loading="lazy"
          onError={(e) => {
            const target = e.currentTarget as HTMLImageElement;
            if (target.src !== DEFAULT_PROFILE_ICON) {
              target.src = DEFAULT_PROFILE_ICON;
            }
          }}
        />
        <AvatarFallback>
          {profile?.display_name?.[0]?.toUpperCase() ||
            user?.email?.[0]?.toUpperCase() ||
            'U'}
        </AvatarFallback>
      </Avatar>
    </Button>
  );
}
