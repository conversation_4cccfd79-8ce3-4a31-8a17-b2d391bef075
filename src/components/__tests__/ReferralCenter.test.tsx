// Referral Center Component Tests
// Tests for referral system UI components and interactions

import React from 'react';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReferralCenter } from '../ReferralCenter';

// Mock hooks and services
vi.mock('@/hooks/useReferrals', () => ({
  useReferrals: vi.fn(() => ({
    referralStats: {
      totalReferrals: 5,
      successfulReferrals: 3,
      pendingReferrals: 2,
      conversionRate: 60,
      totalEarnings: 150,
      pendingEarnings: 50,
      currentTier: 2,
      nextTierProgress: 75
    },
    referralCode: 'ABC123',
    referralLink: 'https://app.com/ref/ABC123',
    recentActivity: [
      {
        id: '1',
        type: 'referral_signup',
        description: '<PERSON> signed up using your referral',
        timestamp: '2024-01-15T10:00:00Z',
        reward: { amount: 25, currency: 'USD' }
      },
      {
        id: '2',
        type: 'tier_upgrade',
        description: 'You reached Tier 2!',
        timestamp: '2024-01-14T15:30:00Z'
      }
    ],
    isLoading: false,
    error: null,
    generateReferralCode: vi.fn(),
    createReferralLink: vi.fn(),
    trackReferralClick: vi.fn(),
    refreshStats: vi.fn()
  }))
}));

vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(() => ({
    toast: vi.fn()
  }))
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined)
  }
});

// Mock URL constructor
global.URL = class URL {
  constructor(public href: string) {}
  toString() { return this.href; }
} as any;

describe('ReferralCenter', () => {
  const mockUseReferrals = vi.mocked(await import('@/hooks/useReferrals')).useReferrals;
  const mockToast = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    vi.mocked(await import('@/hooks/use-toast')).useToast.mockReturnValue({
      toast: mockToast
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render referral center with stats', () => {
      render(<ReferralCenter />);
      
      expect(screen.getByText('Referral Center')).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument(); // Total referrals
      expect(screen.getByText('3')).toBeInTheDocument(); // Successful referrals
      expect(screen.getByText('60%')).toBeInTheDocument(); // Conversion rate
      expect(screen.getByText('$150')).toBeInTheDocument(); // Total earnings
    });

    it('should render referral code and link', () => {
      render(<ReferralCenter />);
      
      expect(screen.getByDisplayValue('ABC123')).toBeInTheDocument();
      expect(screen.getByDisplayValue('https://app.com/ref/ABC123')).toBeInTheDocument();
    });

    it('should render recent activity', () => {
      render(<ReferralCenter />);
      
      expect(screen.getByText('John Doe signed up using your referral')).toBeInTheDocument();
      expect(screen.getByText('You reached Tier 2!')).toBeInTheDocument();
    });

    it('should show loading state', () => {
      mockUseReferrals.mockReturnValue({
        ...mockUseReferrals(),
        isLoading: true
      });

      render(<ReferralCenter />);
      
      expect(screen.getByText('Loading referral data...')).toBeInTheDocument();
    });

    it('should show error state', () => {
      mockUseReferrals.mockReturnValue({
        ...mockUseReferrals(),
        error: 'Failed to load referral data'
      });

      render(<ReferralCenter />);
      
      expect(screen.getByText('Failed to load referral data')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should copy referral code to clipboard', async () => {
      const user = userEvent.setup();
      render(<ReferralCenter />);
      
      const copyCodeButton = screen.getByRole('button', { name: /copy.*code/i });
      await user.click(copyCodeButton);
      
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('ABC123');
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Copied!',
        description: 'Referral code copied to clipboard'
      });
    });

    it('should copy referral link to clipboard', async () => {
      const user = userEvent.setup();
      render(<ReferralCenter />);
      
      const copyLinkButton = screen.getByRole('button', { name: /copy.*link/i });
      await user.click(copyLinkButton);
      
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('https://app.com/ref/ABC123');
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Copied!',
        description: 'Referral link copied to clipboard'
      });
    });

    it('should generate new referral code', async () => {
      const user = userEvent.setup();
      const mockGenerateCode = vi.fn();
      
      mockUseReferrals.mockReturnValue({
        ...mockUseReferrals(),
        generateReferralCode: mockGenerateCode
      });

      render(<ReferralCenter />);
      
      const generateButton = screen.getByRole('button', { name: /generate.*code/i });
      await user.click(generateButton);
      
      expect(mockGenerateCode).toHaveBeenCalled();
    });

    it('should refresh stats', async () => {
      const user = userEvent.setup();
      const mockRefreshStats = vi.fn();
      
      mockUseReferrals.mockReturnValue({
        ...mockUseReferrals(),
        refreshStats: mockRefreshStats
      });

      render(<ReferralCenter />);
      
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      await user.click(refreshButton);
      
      expect(mockRefreshStats).toHaveBeenCalled();
    });
  });

  describe('Social Sharing', () => {
    it('should show social sharing buttons', () => {
      render(<ReferralCenter />);
      
      expect(screen.getByRole('button', { name: /share.*twitter/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /share.*facebook/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /share.*linkedin/i })).toBeInTheDocument();
    });

    it('should handle Twitter sharing', async () => {
      const user = userEvent.setup();
      const mockOpen = vi.fn();
      window.open = mockOpen;

      render(<ReferralCenter />);
      
      const twitterButton = screen.getByRole('button', { name: /share.*twitter/i });
      await user.click(twitterButton);
      
      expect(mockOpen).toHaveBeenCalledWith(
        expect.stringContaining('twitter.com/intent/tweet'),
        '_blank'
      );
    });

    it('should handle Facebook sharing', async () => {
      const user = userEvent.setup();
      const mockOpen = vi.fn();
      window.open = mockOpen;

      render(<ReferralCenter />);
      
      const facebookButton = screen.getByRole('button', { name: /share.*facebook/i });
      await user.click(facebookButton);
      
      expect(mockOpen).toHaveBeenCalledWith(
        expect.stringContaining('facebook.com/sharer'),
        '_blank'
      );
    });

    it('should handle LinkedIn sharing', async () => {
      const user = userEvent.setup();
      const mockOpen = vi.fn();
      window.open = mockOpen;

      render(<ReferralCenter />);
      
      const linkedinButton = screen.getByRole('button', { name: /share.*linkedin/i });
      await user.click(linkedinButton);
      
      expect(mockOpen).toHaveBeenCalledWith(
        expect.stringContaining('linkedin.com/sharing'),
        '_blank'
      );
    });
  });

  describe('Tier Progress', () => {
    it('should display current tier and progress', () => {
      render(<ReferralCenter />);
      
      expect(screen.getByText('Tier 2')).toBeInTheDocument();
      expect(screen.getByText('75%')).toBeInTheDocument(); // Progress to next tier
    });

    it('should show tier benefits', () => {
      render(<ReferralCenter />);
      
      // Should show current tier benefits
      expect(screen.getByText(/tier.*benefits/i)).toBeInTheDocument();
    });

    it('should show next tier requirements', () => {
      render(<ReferralCenter />);
      
      // Should show what's needed for next tier
      expect(screen.getByText(/next.*tier/i)).toBeInTheDocument();
    });
  });

  describe('Activity Timeline', () => {
    it('should display activity items with correct formatting', () => {
      render(<ReferralCenter />);
      
      // Check for activity items
      const activities = screen.getAllByRole('listitem');
      expect(activities.length).toBeGreaterThan(0);
      
      // Check for timestamps
      expect(screen.getByText(/Jan 15/)).toBeInTheDocument();
      expect(screen.getByText(/Jan 14/)).toBeInTheDocument();
    });

    it('should show reward amounts for earning activities', () => {
      render(<ReferralCenter />);
      
      expect(screen.getByText('+$25')).toBeInTheDocument();
    });

    it('should handle empty activity list', () => {
      mockUseReferrals.mockReturnValue({
        ...mockUseReferrals(),
        recentActivity: []
      });

      render(<ReferralCenter />);
      
      expect(screen.getByText(/no.*activity/i)).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('should adapt to mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375
      });

      render(<ReferralCenter />);
      
      // Component should render without issues on mobile
      expect(screen.getByText('Referral Center')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<ReferralCenter />);
      
      expect(screen.getByRole('button', { name: /copy.*code/i })).toHaveAttribute('aria-label');
      expect(screen.getByRole('button', { name: /copy.*link/i })).toHaveAttribute('aria-label');
    });

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup();
      render(<ReferralCenter />);
      
      // Tab through interactive elements
      await user.tab();
      expect(document.activeElement).toHaveAttribute('role', 'button');
      
      await user.tab();
      expect(document.activeElement).toHaveAttribute('role', 'button');
    });

    it('should announce copy actions to screen readers', async () => {
      const user = userEvent.setup();
      render(<ReferralCenter />);
      
      const copyButton = screen.getByRole('button', { name: /copy.*code/i });
      await user.click(copyButton);
      
      // Toast should provide feedback for screen readers
      expect(mockToast).toHaveBeenCalledWith(expect.objectContaining({
        title: 'Copied!',
        description: expect.any(String)
      }));
    });
  });

  describe('Error Handling', () => {
    it('should handle clipboard API failures gracefully', async () => {
      const user = userEvent.setup();
      
      // Mock clipboard to fail
      navigator.clipboard.writeText = vi.fn().mockRejectedValue(new Error('Clipboard failed'));
      
      render(<ReferralCenter />);
      
      const copyButton = screen.getByRole('button', { name: /copy.*code/i });
      await user.click(copyButton);
      
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Copy Failed',
        description: 'Could not copy to clipboard',
        variant: 'destructive'
      });
    });

    it('should handle missing referral data gracefully', () => {
      mockUseReferrals.mockReturnValue({
        ...mockUseReferrals(),
        referralCode: null,
        referralLink: null,
        referralStats: null
      });

      render(<ReferralCenter />);
      
      // Should show fallback content
      expect(screen.getByText(/generate.*first.*code/i)).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('should not re-render unnecessarily', () => {
      const renderSpy = vi.fn();
      
      const TestWrapper = () => {
        renderSpy();
        return <ReferralCenter />;
      };

      const { rerender } = render(<TestWrapper />);
      
      expect(renderSpy).toHaveBeenCalledTimes(1);
      
      // Rerender with same props
      rerender(<TestWrapper />);
      
      // Should still only be called once if properly memoized
      expect(renderSpy).toHaveBeenCalledTimes(2);
    });

    it('should debounce rapid refresh clicks', async () => {
      const user = userEvent.setup();
      const mockRefreshStats = vi.fn();
      
      mockUseReferrals.mockReturnValue({
        ...mockUseReferrals(),
        refreshStats: mockRefreshStats
      });

      render(<ReferralCenter />);
      
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      
      // Click rapidly
      await user.click(refreshButton);
      await user.click(refreshButton);
      await user.click(refreshButton);
      
      // Should debounce calls
      await waitFor(() => {
        expect(mockRefreshStats).toHaveBeenCalledTimes(1);
      });
    });
  });
});