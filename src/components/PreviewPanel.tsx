
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Eye, MessageCircle, X } from 'lucide-react';
import { Avatar3D } from '@/components/Avatar3D';
import { ChatInterface } from '@/components/ChatInterface';
import { useTranslation } from 'react-i18next';

export function PreviewPanel() {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const { t } = useTranslation();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Preview Header */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            {t('previewPanel.bioLinkPreview')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            {t('previewPanel.previewDescription')}
          </p>
        </CardContent>
      </Card>

      {/* Preview Container */}
      <Card className="glass-card overflow-hidden">
        <div className="relative">
          {/* Simulated Browser Frame */}
          <div className="bg-border/20 px-4 py-2 flex items-center gap-2">
            <div className="flex gap-1">
              <div className="w-3 h-3 rounded-full bg-red-500/60"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-500/60"></div>
              <div className="w-3 h-3 rounded-full bg-green-500/60"></div>
            </div>
            <div className="flex-1 text-center">
              <span className="text-sm text-muted-foreground font-mono">heey.me/johndoe</span>
            </div>
          </div>

          {/* Preview Content */}
          <div className="relative bg-background min-h-[600px] flex flex-col">
            {/* Header */}
            <div className="border-b border-border/50 p-4 text-center">
              <h1 className="text-xl font-semibold">{t('previewPanel.yourAIAssistant')}</h1>
              <p className="text-muted-foreground">{t('previewPanel.chatWithAvatar')}</p>
            </div>

            {/* Avatar Display */}
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="w-full max-w-md h-96">
                <Avatar3D />
              </div>
            </div>

            {/* Chat Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsChatOpen(true)}
              className="fixed right-6 bottom-6 w-14 h-14 bg-primary hover:bg-primary/90 rounded-full flex items-center justify-center shadow-lg z-10"
            >
              <MessageCircle className="h-6 w-6 text-white" />
            </motion.button>

            {/* Chat Sidebar */}
            {isChatOpen && (
              <>
                {/* Overlay */}
                <div 
                  className="fixed inset-0 bg-black/50 z-20"
                  onClick={() => setIsChatOpen(false)}
                />
                
                {/* Chat Panel */}
                <motion.div
                  initial={{ x: '100%' }}
                  animate={{ x: 0 }}
                  exit={{ x: '100%' }}
                  className="fixed right-0 top-0 h-full w-96 bg-background/95 backdrop-blur-xl border-l border-border/50 z-30 flex flex-col"
                >
                  {/* Chat Header */}
                  <div className="p-4 border-b border-border/50 flex items-center justify-between">
                    <h3 className="font-semibold">{t('previewPanel.chatWithAI')}</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsChatOpen(false)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Chat Interface */}
                  <div className="flex-1 p-4">
                    <ChatInterface 
                      requireAuth={false}
                      placeholder={t('previewPanel.placeholder')}
                    />
                  </div>
                </motion.div>
              </>
            )}
          </div>
        </div>
      </Card>
    </motion.div>
  );
}
