// Enhanced Avatar3D Component
// Advanced 3D avatar rendering with room environments and interactions

import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { Canvas, useFrame, useLoader, useThree } from '@react-three/fiber';
import { OrbitControls, Environment, PerspectiveCamera, useGLTF } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { Suspense } from 'react';
import * as THREE from 'three';
import { ErrorBoundary } from './ErrorBoundary';
import type { AvatarData, Room3D, AvatarConfig } from '@/types/avatar';

interface EnhancedAvatar3DProps {
  avatar: AvatarData;
  room?: Room3D;
  config?: AvatarConfig;
  enableOrbitControls?: boolean;
  enableInteractions?: boolean;
  onInteraction?: (type: string, data?: unknown) => void;
  onLoadingStateChange?: (loading: boolean) => void;
  onError?: (error: string) => void;
  className?: string;
  style?: React.CSSProperties;
}

interface ModelProps {
  url: string;
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: number;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

// Avatar Model Component
const AvatarModel: React.FC<ModelProps> = ({ 
  url, 
  position = [0, 0, 0], 
  rotation = [0, 0, 0], 
  scale = 1,
  onLoad,
  onError 
}) => {
  const meshRef = useRef<THREE.Group>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  const { scene, animations } = useGLTF(url, true);
  const { camera } = useThree();

  // Animation mixer for avatar animations
  const mixer = useMemo(() => {
    if (animations && animations.length > 0) {
      const mixer = new THREE.AnimationMixer(scene);
      
      // Play idle animation if available
      const idleAnimation = animations.find(anim => 
        anim.name.toLowerCase().includes('idle')
      );
      
      if (idleAnimation) {
        const action = mixer.clipAction(idleAnimation);
        action.play();
      }
      
      return mixer;
    }
    return null;
  }, [scene, animations]);

  // Update animation mixer
  useFrame((state, delta) => {
    if (mixer) {
      mixer.update(delta);
    }

    // Gentle breathing animation if no animations available
    if (meshRef.current && !mixer) {
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.01;
    }
  });

  useEffect(() => {
    if (scene && !isLoaded) {
      setIsLoaded(true);
      onLoad?.();
      
      // Center the avatar in the view
      const box = new THREE.Box3().setFromObject(scene);
      const center = box.getCenter(new THREE.Vector3());
      scene.position.sub(center);
      
      // Adjust camera position based on avatar bounds
      const size = box.getSize(new THREE.Vector3());
      const maxDim = Math.max(size.x, size.y, size.z);
      const distance = maxDim * 2;
      
      if (camera instanceof THREE.PerspectiveCamera) {
        camera.position.setZ(distance);
        camera.lookAt(center);
      }
    }
  }, [scene, isLoaded, onLoad, camera]);

  useEffect(() => {
    // Error handling for model loading
    const handleError = (error: Error) => {
      console.error('Avatar model loading error:', error);
      onError?.(error);
    };

    // Add error event listeners if needed
    return () => {
      if (mixer) {
        mixer.stopAllAction();
      }
    };
  }, [mixer, onError]);

  if (!scene) return null;

  return (
    <group
      ref={meshRef}
      position={position}
      rotation={rotation}
      scale={scale}
    >
      <primitive object={scene} />
    </group>
  );
};

// Room Environment Component
const RoomEnvironment: React.FC<{ room: Room3D; onLoad?: () => void; onError?: (error: Error) => void }> = ({ 
  room, 
  onLoad, 
  onError 
}) => {
  const { scene } = useGLTF(room.glb_url, true);

  useEffect(() => {
    if (scene) {
      onLoad?.();
      
      // Optimize room for performance
      scene.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          // Enable shadow casting/receiving
          child.castShadow = true;
          child.receiveShadow = true;
          
          // Optimize materials
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => {
                if (mat instanceof THREE.MeshStandardMaterial) {
                  mat.envMapIntensity = 0.8;
                }
              });
            } else if (child.material instanceof THREE.MeshStandardMaterial) {
              child.material.envMapIntensity = 0.8;
            }
          }
        }
      });
    }
  }, [scene, onLoad]);

  if (!scene) return null;

  return <primitive object={scene} />;
};

// Interactive Controls Component
const InteractiveControls: React.FC<{
  onInteraction: (type: string, data?: unknown) => void;
  enableOrbitControls: boolean;
}> = ({ onInteraction, enableOrbitControls }) => {
  const controlsRef = useRef<any>(null);

  const handlePointerMove = useCallback((event: React.PointerEvent) => {
    // Track mouse movement for hover effects
    onInteraction('hover', {
      x: event.clientX,
      y: event.clientY
    });
  }, [onInteraction]);

  const handleClick = useCallback((event: React.MouseEvent) => {
    onInteraction('click', {
      x: event.clientX,
      y: event.clientY
    });
  }, [onInteraction]);

  return (
    <>
      {enableOrbitControls && (
        <OrbitControls
          ref={controlsRef}
          enablePan={false}
          enableZoom={true}
          enableRotate={true}
          minDistance={2}
          maxDistance={10}
          minPolarAngle={Math.PI / 6}
          maxPolarAngle={Math.PI / 2}
          autoRotate={false}
          autoRotateSpeed={0.5}
        />
      )}
      
      {/* Invisible interaction plane */}
      <mesh
        onPointerMove={handlePointerMove}
        onClick={handleClick}
        visible={false}
      >
        <planeGeometry args={[100, 100]} />
        <meshBasicMaterial transparent opacity={0} />
      </mesh>
    </>
  );
};

// Lighting Setup Component
const LightingSetup: React.FC<{ config?: AvatarConfig }> = ({ config }) => {
  const ambientIntensity = config?.lighting?.ambient ?? 0.4;
  const directionalIntensity = config?.lighting?.directional ?? 0.8;

  return (
    <>
      <ambientLight intensity={ambientIntensity} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={directionalIntensity}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
      <pointLight position={[-10, -10, -5]} intensity={0.3} />
    </>
  );
};

// Loading Fallback Component
const LoadingFallback: React.FC = () => (
  <div className="flex items-center justify-center h-full bg-gradient-to-br from-gray-100 to-gray-200">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
      <p className="text-gray-600 text-sm">Loading 3D Avatar...</p>
    </div>
  </div>
);

// Error Fallback Component
const ErrorFallback: React.FC<{ error: string; onRetry?: () => void }> = ({ error, onRetry }) => (
  <div className="flex items-center justify-center h-full bg-gradient-to-br from-red-50 to-red-100">
    <div className="text-center p-6">
      <div className="text-red-500 mb-4">
        <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-gray-800 mb-2">3D Loading Error</h3>
      <p className="text-sm text-gray-600 mb-4">{error}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      )}
    </div>
  </div>
);

// Main Enhanced Avatar3D Component
export const EnhancedAvatar3D: React.FC<EnhancedAvatar3DProps> = ({
  avatar,
  room,
  config,
  enableOrbitControls = true,
  enableInteractions = true,
  onInteraction,
  onLoadingStateChange,
  onError,
  className = '',
  style = {}
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [avatarLoaded, setAvatarLoaded] = useState(false);
  const [roomLoaded, setRoomLoaded] = useState(false);

  // Loading state management
  useEffect(() => {
    const totalItems = room ? 2 : 1; // Avatar + Room (if present)
    const loadedItems = (avatarLoaded ? 1 : 0) + (room && roomLoaded ? 1 : 0);
    const loading = loadedItems < totalItems;
    
    setIsLoading(loading);
    onLoadingStateChange?.(loading);
  }, [avatarLoaded, roomLoaded, room, onLoadingStateChange]);

  // Error handling
  const handleError = useCallback((errorMessage: string) => {
    setError(errorMessage);
    setIsLoading(false);
    onError?.(errorMessage);
  }, [onError]);

  // Retry loading
  const handleRetry = useCallback(() => {
    setError(null);
    setIsLoading(true);
    setAvatarLoaded(false);
    setRoomLoaded(false);
  }, []);

  // Avatar loading handlers
  const handleAvatarLoad = useCallback(() => {
    setAvatarLoaded(true);
  }, []);

  const handleAvatarError = useCallback((error: Error) => {
    handleError(`Failed to load avatar: ${error.message}`);
  }, [handleError]);

  // Room loading handlers
  const handleRoomLoad = useCallback(() => {
    setRoomLoaded(true);
  }, []);

  const handleRoomError = useCallback((error: Error) => {
    handleError(`Failed to load room: ${error.message}`);
  }, [handleError]);

  // Interaction handler
  const handleInteraction = useCallback((type: string, data?: unknown) => {
    if (enableInteractions && onInteraction) {
      onInteraction(type, data);
    }
  }, [enableInteractions, onInteraction]);

  if (error) {
    return <ErrorFallback error={error} onRetry={handleRetry} />;
  }

  return (
    <div 
      className={`relative w-full h-full ${className}`}
      style={style}
    >
      <ErrorBoundary
        fallback={<ErrorFallback error="3D rendering error occurred" onRetry={handleRetry} />}
      >
        <Canvas
          shadows
          camera={{
            position: [0, 0, 5],
            fov: 50,
            near: 0.1,
            far: 1000
          }}
          style={{ background: 'transparent' }}
        >
          <Suspense fallback={null}>
            {/* Environment and Lighting */}
            <LightingSetup config={config} />
            
            {/* Room Environment */}
            {room && (
              <RoomEnvironment
                room={room}
                onLoad={handleRoomLoad}
                onError={handleRoomError}
              />
            )}
            
            {/* Avatar Model */}
            <AvatarModel
              url={avatar.avatar_url}
              position={config?.position ? [config.position.x, config.position.y, config.position.z] : [0, 0, 0]}
              rotation={config?.rotation ? [config.rotation.x, config.rotation.y, config.rotation.z] : [0, 0, 0]}
              scale={config?.scale ?? 1}
              onLoad={handleAvatarLoad}
              onError={handleAvatarError}
            />
            
            {/* Interactive Controls */}
            <InteractiveControls
              onInteraction={handleInteraction}
              enableOrbitControls={enableOrbitControls}
            />
            
            {/* Environment Effects */}
            <Environment preset="studio" />
          </Suspense>
        </Canvas>
      </ErrorBoundary>
      
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 z-10">
          <LoadingFallback />
        </div>
      )}
    </div>
  );
};

// Preload hook for better performance
export const usePreloadAvatarAssets = (avatar: AvatarData, room?: Room3D) => {
  useEffect(() => {
    // Preload avatar model
    if (avatar.avatar_url) {
      useGLTF.preload(avatar.avatar_url);
    }
    
    // Preload room model
    if (room?.glb_url) {
      useGLTF.preload(room.glb_url);
    }
  }, [avatar.avatar_url, room?.glb_url]);
};

export default EnhancedAvatar3D;