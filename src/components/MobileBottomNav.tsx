
import { useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { AppIcon } from '@/components/AppIcon';
import { useTranslation } from 'react-i18next';

const menuItems = [
  { key: 'topics', path: '/topics', icon: 'Message' },
  { key: 'knowledge', path: '/knowledge', icon: 'Lamp' },
  { key: 'avatar', path: '/avatar', icon: 'Person' },
  { key: 'bioLink', path: '/bio-link', icon: 'Send' },
  { key: 'rewards', path: '/rewards', icon: 'Medal' },
];

interface MobileBottomNavProps {
  isHidden?: boolean;
}

export function MobileBottomNav({ isHidden = false }: MobileBottomNavProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <motion.nav
      initial={{ y: 0 }}
      animate={{ y: isHidden ? 100 : 0 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="
        fixed bottom-0 left-0 right-0 z-50
        bg-background/95 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80
        border-t border-white/10 safe-area-bottom
        overflow-hidden py-2
      "
    >
      <div className="w-full px-2">
        <div className="grid grid-cols-5 gap-1 w-full items-center">
          {menuItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <motion.button
                key={item.path}
                onClick={() => navigate(item.path)}
                className={`
                  flex flex-col items-center justify-center
                  py-1 px-2 rounded-lg transition-colors
                  ${isActive
                    ? 'text-primary'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'}
                `}
                whileTap={{ scale: 0.95 }}
                whileHover={{ scale: 1.02 }}
              >
                <motion.div
                  animate={{
                    scale: isActive ? 1.1 : 1,
                    y: isActive ? -1 : 0
                  }}
                  transition={{ duration: 0.2 }}
                  className="mb-1"
                >
                  <AppIcon name={item.icon} alt={t(`navigation.${item.key}`)} className="w-8 h-8" />
                </motion.div>
                <span className="text-xs font-medium leading-tight whitespace-nowrap text-center">
                  {t(`navigation.${item.key}`)}
                </span>
              </motion.button>
            );
          })}
        </div>
      </div>
    </motion.nav>
  );
}
