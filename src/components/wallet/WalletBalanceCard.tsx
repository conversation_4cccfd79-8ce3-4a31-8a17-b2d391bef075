
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { AppIcon } from '@/components/AppIcon';
import { useState, useEffect } from 'react';

interface WalletBalanceCardProps {
  balance: number;
  usdValue: number;
  isConnected: boolean;
  walletAddress?: string;
  onConnect: () => void;
  onDisconnect: () => void;
}

export function WalletBalanceCard({ 
  balance, 
  usdValue, 
  isConnected, 
  walletAddress, 
  onConnect, 
  onDisconnect 
}: WalletBalanceCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const handleConnect = async () => {
    setIsLoading(true);
    try {
      await onConnect();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Card className="p-8 md:p-10 relative overflow-hidden glass-card">
        <div className="absolute top-0 right-0 w-40 h-40 opacity-10">
          <img src="/3D-coin/heycoin-gold.webp" alt="HEEY Coin" className="w-full h-full object-contain" />
        </div>
        
        <CardContent className="p-0 relative z-10">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 md:w-20 md:h-20 rounded-xl bg-[#1DEBF6]/10 backdrop-blur-sm flex items-center justify-center shadow-lg">
                <img src="/3D-coin/heycoin-gold.webp" alt="HEEY" className="w-10 h-10 md:w-12 md:h-12" />
              </div>
              <div>
                <h3 className="heading-sm text-foreground">$HEEY Wallet</h3>
                <p className="body-md text-muted-foreground">Soneium Network</p>
              </div>
            </div>
            
            {isConnected ? (
              <div className="text-right">
                <p className="body-sm text-muted-foreground">Connected</p>
                <p className="body-md font-mono text-gradient">
                  {walletAddress && formatAddress(walletAddress)}
                </p>
              </div>
            ) : null}
          </div>

          {isConnected ? (
            <div className="space-y-6">
              <div className="text-center">
                <motion.div
                  initial={{ scale: 0.9 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.5 }}
                  className="heading-xl mb-3 text-foreground"
                >
                  {balance.toLocaleString()}
                </motion.div>
                <p className="heading-md text-gradient font-semibold mb-2">$HEEY</p>
                <p className="body-lg text-muted-foreground">
                  ≈ ${usdValue.toFixed(2)} USD
                </p>
              </div>

              <div className="flex space-x-4">
                <Button 
                  className="flex-1 h-14 md:h-16 body-lg rounded-xl"
                  onClick={() => {}}
                >
                  <AppIcon name="Send" alt="Send" className="w-5 h-5 mr-3" />
                  Send
                </Button>
                <Button 
                  variant="outline" 
                  className="flex-1 h-14 md:h-16 body-lg rounded-xl"
                  onClick={onDisconnect}
                >
                  <AppIcon name="Cancel" alt="Disconnect" className="w-5 h-5 mr-3" />
                  Disconnect
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center">
              <div className="mb-8">
                <AppIcon name="Bag" alt="Wallet" className="w-20 h-20 md:w-24 md:h-24 mx-auto mb-6 opacity-50" />
                <h4 className="heading-md text-foreground mb-4">Connect Your Wallet</h4>
                <p className="body-lg text-muted-foreground max-w-md mx-auto">
                  Connect your Web3 wallet to view your $HEEY balance and participate in the token sale
                </p>
              </div>
              
              <Button 
                className="w-full h-16 md:h-18 body-lg rounded-xl"
                onClick={handleConnect}
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="w-5 h-5 border-2 border-black/20 border-t-black rounded-full animate-spin mr-3" />
                    Connecting...
                  </div>
                ) : (
                  <>
                    <AppIcon name="Bag" alt="Connect" className="w-5 h-5 mr-3" />
                    Connect Wallet
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
