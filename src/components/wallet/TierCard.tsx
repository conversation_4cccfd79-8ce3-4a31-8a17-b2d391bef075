
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { motion } from 'framer-motion';
import { AppIcon } from '@/components/AppIcon';

interface TierData {
  id: string;
  name: string;
  icon: string;
  minBuy: number;
  maxBuy: number;
  tokenPrice: number;
  bonusPercent: number;
  totalTokens: number;
  soldTokens: number;
  benefits: string[];
  isPopular?: boolean;
  isVip?: boolean;
}

interface TierCardProps {
  tier: TierData;
  onBuy: (tierId: string) => void;
  isConnected: boolean;
}

export function TierCard({ tier, onBuy, isConnected }: TierCardProps) {
  const progressPercent = (tier.soldTokens / tier.totalTokens) * 100;
  const remainingTokens = tier.totalTokens - tier.soldTokens;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      whileHover={{ scale: 1.02 }}
      className="relative h-full"
    >
      <Card className={`glass-card relative overflow-hidden h-full ${
        tier.isPopular ? 'ring-2 ring-[#1DEBF6]/50' : ''
      } ${tier.isVip ? 'ring-2 ring-purple-400/50' : ''}`}>
        {tier.isPopular && (
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-[#1DEBF6] to-[#83FFBC] text-black text-base font-bold text-center py-3 rounded-t-xl shadow-lg">
            🔥 MOST POPULAR
          </div>
        )}
        
        {tier.isVip && (
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-purple-400 to-purple-600 text-white text-base font-bold text-center py-3 rounded-t-xl shadow-lg">
            👑 VIP EXCLUSIVE
          </div>
        )}

        <CardHeader className={`pb-6 ${tier.isPopular || tier.isVip ? 'pt-16' : 'pt-8'} px-8`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className={`w-16 h-16 md:w-18 md:h-18 rounded-xl flex items-center justify-center shadow-lg ${
                tier.isVip ? 'bg-gradient-to-r from-purple-500/20 to-purple-700/20 backdrop-blur-sm' :
                tier.isPopular ? 'bg-[#1DEBF6]/10 backdrop-blur-sm' :
                'bg-blue-500/20 backdrop-blur-sm'
              }`}>
                <AppIcon name={tier.icon} alt={tier.name} className="w-8 h-8 text-gradient" />
              </div>
              <div>
                <CardTitle className="heading-md text-foreground">{tier.name}</CardTitle>
                <p className="body-md text-muted-foreground">Tier {tier.id}</p>
              </div>
            </div>
            
            {tier.bonusPercent > 0 && (
              <Badge className="bg-[#1DEBF6]/10 backdrop-blur-sm text-gradient shadow-lg body-md px-4 py-2 rounded-full">
                +{tier.bonusPercent}% Bonus
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-8 px-8 pb-8">
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <span className="body-md text-muted-foreground">Token Price</span>
              <span className="heading-sm font-bold text-foreground">${tier.tokenPrice}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="body-md text-muted-foreground">Min - Max Buy</span>
              <span className="body-md font-semibold text-gradient">
                ${tier.minBuy} - ${tier.maxBuy}
              </span>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="body-md text-muted-foreground">Progress</span>
              <span className="body-md font-semibold text-foreground">
                {remainingTokens.toLocaleString()} left
              </span>
            </div>
            <Progress value={progressPercent} className="h-3 rounded-full glass-card" />
            <p className="body-sm text-muted-foreground text-right">
              {tier.soldTokens.toLocaleString()} / {tier.totalTokens.toLocaleString()} sold
            </p>
          </div>

          <div className="space-y-4">
            <h4 className="heading-sm font-semibold text-foreground">Benefits:</h4>
            <ul className="space-y-3">
              {tier.benefits.map((benefit, index) => (
                <li key={index} className="body-sm md:body-md text-muted-foreground flex items-center">
                  <div className="w-2 h-2 bg-gradient-to-r from-[#1DEBF6] to-[#83FFBC] rounded-full mr-3 flex-shrink-0 shadow-sm" />
                  {benefit}
                </li>
              ))}
            </ul>
          </div>

          <Button
            className="w-full h-14 md:h-16 body-lg rounded-xl mt-6"
            onClick={() => onBuy(tier.id)}
            disabled={!isConnected || remainingTokens <= 0}
          >
            {!isConnected ? (
              'Connect Wallet First'
            ) : remainingTokens <= 0 ? (
              'Sold Out'
            ) : (
              <>
                <AppIcon name="BagMoney" alt="Buy" className="w-5 h-5 mr-3" />
                Buy $HEEY Tokens
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
}
