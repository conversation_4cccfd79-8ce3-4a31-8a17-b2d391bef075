
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AppIcon } from '@/components/AppIcon';
import { useState } from 'react';
import { motion } from 'framer-motion';

interface PurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  tierName: string;
  tokenPrice: number;
  minBuy: number;
  maxBuy: number;
  bonusPercent: number;
  onConfirm: (amount: number) => void;
}

export function PurchaseModal({
  isOpen,
  onClose,
  tierName,
  tokenPrice,
  minBuy,
  maxBuy,
  bonusPercent,
  onConfirm
}: PurchaseModalProps) {
  const [amount, setAmount] = useState(minBuy);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const tokensReceived = amount / tokenPrice;
  const bonusTokens = tokensReceived * (bonusPercent / 100);
  const totalTokens = tokensReceived + bonusTokens;

  const handleConfirm = async () => {
    setIsProcessing(true);
    try {
      await onConfirm(amount);
      onClose();
    } finally {
      setIsProcessing(false);
    }
  };

  const isValidAmount = amount >= minBuy && amount <= maxBuy;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-white/5 backdrop-blur-xl shadow-2xl max-w-lg rounded-3xl">
        <DialogHeader>
          <DialogTitle className="text-2xl md:text-3xl font-bold text-white flex items-center">
            <img src="/3D-coin/heycoin-gold.webp" alt="HEEY" className="w-8 h-8 mr-3" />
            Buy $HEEY - {tierName}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-8 pt-6">
          <div className="space-y-6">
            <div>
              <Label htmlFor="amount" className="text-lg font-medium text-white mb-3 block">
                Purchase Amount (USD)
              </Label>
              <div className="relative">
                <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-lg text-muted-foreground">
                  $
                </span>
                <Input
                  id="amount"
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(Number(e.target.value))}
                  min={minBuy}
                  max={maxBuy}
                  step={10}
                  className="pl-10 bg-white/10 backdrop-blur-xl shadow-lg h-14 text-lg rounded-2xl text-white"
                />
              </div>
              <p className="text-sm md:text-base text-muted-foreground mt-2">
                Min: ${minBuy} • Max: ${maxBuy}
              </p>
            </div>

            <div className="bg-white/5 backdrop-blur-xl shadow-lg p-6 rounded-2xl space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-base md:text-lg text-muted-foreground">Token Price</span>
                <span className="text-base md:text-lg font-semibold text-white">${tokenPrice}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-base md:text-lg text-muted-foreground">Base Tokens</span>
                <span className="text-base md:text-lg font-semibold text-white">
                  {tokensReceived.toFixed(2)} $HEEY
                </span>
              </div>
              
              {bonusPercent > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-base md:text-lg text-lime-400">Bonus ({bonusPercent}%)</span>
                  <span className="text-base md:text-lg font-semibold text-lime-400">
                    +{bonusTokens.toFixed(2)} $HEEY
                  </span>
                </div>
              )}
              
              <hr className="border-white/10" />
              
              <div className="flex justify-between items-center">
                <span className="text-lg md:text-xl font-semibold text-white">Total Tokens</span>
                <span className="text-xl md:text-2xl font-bold text-lime-400">
                  {totalTokens.toFixed(2)} $HEEY
                </span>
              </div>
            </div>

            <div className="bg-blue-400/10 backdrop-blur-xl shadow-lg rounded-2xl p-4">
              <div className="flex items-start space-x-3">
                <AppIcon name="ShieldCheck" alt="Info" className="w-6 h-6 text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm md:text-base text-blue-300">
                  <p className="font-semibold mb-2">Secure Transaction</p>
                  <p>Your purchase will be processed on the Soneium blockchain. Transaction fees may apply.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex space-x-4">
            <Button
              variant="outline"
              className="flex-1 bg-white/10 backdrop-blur-xl hover:bg-white/20 text-white shadow-lg hover:shadow-xl transition-all duration-300 h-14 text-lg rounded-2xl"
              onClick={onClose}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              className="flex-1 bg-gradient-to-r from-lime-400 to-lime-600 hover:from-lime-500 hover:to-lime-700 text-black font-semibold shadow-lg hover:shadow-xl transition-all duration-300 h-14 text-lg rounded-2xl"
              onClick={handleConfirm}
              disabled={!isValidAmount || isProcessing}
            >
              {isProcessing ? (
                <div className="flex items-center">
                  <div className="w-5 h-5 border-2 border-black/20 border-t-black rounded-full animate-spin mr-3" />
                  Processing...
                </div>
              ) : (
                <>
                  <AppIcon name="Ok" alt="Confirm" className="w-5 h-5 mr-3" />
                  Confirm Purchase
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
