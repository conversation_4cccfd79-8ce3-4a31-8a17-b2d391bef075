
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { AppIcon } from '@/components/AppIcon';
import { cn } from '@/lib/utils';

interface ModernWalletCardProps {
  tokenSymbol: string;
  tokenBalance: number;
  usdEquivalent: number;
  onDeposit: () => void;
  onSend: () => void;
  className?: string;
}

export function ModernWalletCard({
  tokenSymbol,
  tokenBalance,
  usdEquivalent,
  onDeposit,
  onSend,
  className
}: ModernWalletCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className={cn('glass-card overflow-hidden', className)}>
        <CardContent className="p-6">
          {/* Header with Wallet Icon */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary/20 to-lime-400/20 backdrop-blur-sm flex items-center justify-center">
                <img 
                  src="/3D-coin/heycoin-gold.webp" 
                  alt="Wallet" 
                  className="w-7 h-7" 
                />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">My Wallet</h3>
                <p className="text-sm text-muted-foreground">{tokenSymbol}</p>
              </div>
            </div>
          </div>

          {/* Balance Display */}
          <div className="text-center mb-6">
            <div className="text-3xl font-bold text-foreground mb-2">
              {tokenBalance.toLocaleString()}
            </div>
            <div className="text-lg font-medium text-gradient mb-1">
              ${tokenSymbol}
            </div>
            <div className="text-muted-foreground">
              ≈ ${usdEquivalent.toFixed(2)} USD
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <Button 
              onClick={onDeposit}
              className="flex-1 h-12 rounded-xl"
              size="lg"
            >
              <AppIcon name="Download" alt="Deposit" className="w-4 h-4 mr-2" />
              Deposit
            </Button>
            <Button 
              onClick={onSend}
              variant="outline"
              className="flex-1 h-12 rounded-xl"
              size="lg"
            >
              <AppIcon name="Send" alt="Send" className="w-4 h-4 mr-2" />
              Send
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
