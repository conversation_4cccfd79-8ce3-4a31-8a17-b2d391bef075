
import { Logo } from './Logo';
import { LanguageSwitcher } from './LanguageSwitcher';
import { UserProfileMenu } from './UserProfileMenu';
import { Button } from './ui/button';
import { AppIcon } from './AppIcon';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useHeaderVisibility } from '@/contexts/HeaderVisibilityContext';
import { cn } from '@/lib/utils';
import walletIcon from '/3D-coin/heey-wallet-icon.webp';

export function MobileHeader() {
  const navigate = useNavigate();
  const { isGlobalHeaderHidden } = useHeaderVisibility();

  return (
    <motion.header
      animate={{ 
        transform: isGlobalHeaderHidden ? 'translateY(-100%)' : 'translateY(0)',
        opacity: isGlobalHeaderHidden ? 0 : 1
      }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className={cn(
        'glass-panel border-b border-white/10 h-16 flex items-center justify-between px-4 sticky top-0 z-40',
        'transition-all duration-300'
      )}
    >
      <div className="flex items-center">
        <Logo />
      </div>

      <div className="flex items-center space-x-2">
        <LanguageSwitcher className="bg-transparent p-0 h-8 w-8" />
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full p-0 h-8 w-8 bg-transparent"
          onClick={() => navigate('/wallet')}
        >
          <img src={walletIcon} alt="Wallet" className="h-full w-full animate-pulse" />
        </Button>
        <UserProfileMenu />
      </div>
    </motion.header>
  );
}
