
import React, { useState, useCallback, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, X, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { AvatarPreview } from './AvatarPreview';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';

interface AvatarCreatorProps {
  isOpen: boolean;
  onClose: () => void;
  onAvatarCreated: (avatarUrl: string) => void;
  currentAvatarUrl?: string;
}

interface RPMEvent {
  eventName: string;
  url?: string;
  error?: string;
  data?: Record<string, unknown>;
}

export function AvatarCreator({ 
  isOpen, 
  onClose, 
  onAvatarCreated, 
  currentAvatarUrl 
}: AvatarCreatorProps) {
  const [avatarUrl, setAvatarUrl] = useState<string>(currentAvatarUrl || '');
  const [isLoading, setIsLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const { toast } = useToast();
  const { t } = useTranslation();

  // Ready Player Me configuration
  const RPM_SUBDOMAIN = import.meta.env.VITE_READY_PLAYER_ME_SUBDOMAIN || 'demo';
  const RPM_URL = `https://${RPM_SUBDOMAIN}.readyplayer.me/avatar?frameApi&language=vi&clearCache=true&bodyType=fullbody&quickStart=true`;

  const validateAvatarUrl = (url: string): boolean => {
    if (!url) return false;
    try {
      const urlObj = new URL(url);
      return urlObj.hostname === 'models.readyplayer.me' && url.endsWith('.glb');
    } catch {
      return false;
    }
  };

  const handleRPMMessage = useCallback(async (event: MessageEvent) => {
    // Only process messages from Ready Player Me
    if (!event.origin.includes('readyplayer.me')) return;

    console.log('RPM Event received:', event.data);
    
    const rpmEvent: RPMEvent = event.data;

    try {
      switch (rpmEvent.eventName) {
        case 'v1.avatar.exported':
          if (rpmEvent.url && validateAvatarUrl(rpmEvent.url)) {
            await handleAvatarExport(rpmEvent.url);
          } else {
            throw new Error('Invalid avatar URL received');
          }
          break;

        case 'v1.frame.ready':
          console.log('RPM Frame ready');
          setError(null);
          break;

        case 'v1.user.set':
          console.log('RPM User set:', rpmEvent.data);
          break;

        case 'v1.avatar.draft':
          console.log('RPM Avatar draft created');
          break;

        case 'v1.error':
          throw new Error(rpmEvent.error || 'Ready Player Me error occurred');

        default:
          console.log('Unhandled RPM event:', rpmEvent.eventName);
      }
    } catch (err) {
      console.error('Error handling RPM message:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setIsLoading(false);
    }
  }, []);

  const handleAvatarExport = useCallback(async (url: string) => {
    console.log('Avatar exported:', url);
    setIsLoading(true);
    setError(null);
    
    try {
      // Validate the GLB URL
      if (!validateAvatarUrl(url)) {
        throw new Error('Invalid avatar URL format');
      }

      // Test if the GLB file is accessible
      const response = await fetch(url, { method: 'HEAD' });
      if (!response.ok) {
        throw new Error('Avatar file is not accessible');
      }

      setAvatarUrl(url);
      setShowPreview(true);
      
      // Save avatar URL to localStorage for now
      localStorage.setItem('userAvatarUrl', url);
      
      onAvatarCreated(url);
      
      toast({
        title: t('toasts.avatarCreated'),
        description: t('toasts.avatarCreatedDesc'),
      });
      
      setRetryCount(0); // Reset retry count on success
      
    } catch (err) {
      console.error('Error saving avatar:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to save avatar';
      setError(errorMessage);
      
      // Implement retry logic
      if (retryCount < 3) {
        setRetryCount(prev => prev + 1);
        setTimeout(() => handleAvatarExport(url), 2000 * (retryCount + 1));
        return;
      }
      
      toast({
        title: t('toasts.avatarError'),
        description: t('toasts.avatarErrorDesc'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [onAvatarCreated, toast, retryCount]);

  useEffect(() => {
    if (isOpen) {
      window.addEventListener('message', handleRPMMessage);
      return () => {
        window.removeEventListener('message', handleRPMMessage);
      };
    }
  }, [isOpen, handleRPMMessage]);

  const handleCreateNew = () => {
    setShowPreview(false);
    setAvatarUrl('');
    setError(null);
    setRetryCount(0);
    
    // Reload iframe to start fresh
    if (iframeRef.current) {
      iframeRef.current.src = RPM_URL;
    }
  };

  const handleClose = () => {
    setShowPreview(false);
    setError(null);
    setRetryCount(0);
    onClose();
  };

  const handleRetry = () => {
    setError(null);
    setRetryCount(0);
    if (iframeRef.current) {
      iframeRef.current.src = RPM_URL;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl h-[80vh] glass-panel border-white/20">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span className="brand-gradient bg-clip-text text-transparent">
              Create Your 3D Avatar
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 relative">
          <AnimatePresence mode="wait">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="absolute top-0 left-0 right-0 z-10 p-4 bg-destructive/10 border border-destructive/20 rounded-lg mb-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="w-4 h-4 text-destructive" />
                    <div>
                      <p className="text-sm text-destructive font-medium">{error}</p>
                      {retryCount > 0 && (
                        <p className="text-xs text-destructive/70">
                          Retry attempt: {retryCount}/3
                        </p>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRetry}
                    className="text-destructive border-destructive/20"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Retry
                  </Button>
                </div>
              </motion.div>
            )}

            {isLoading && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-20"
              >
                <div className="text-center">
                  <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    {retryCount > 0 ? `Retrying... (${retryCount}/3)` : 'Processing your avatar...'}
                  </p>
                </div>
              </motion.div>
            )}

            {!showPreview ? (
              <motion.div
                key="creator"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="h-full"
              >
                <iframe
                  ref={iframeRef}
                  src={RPM_URL}
                  style={{ 
                    width: '100%', 
                    height: '100%',
                    border: 'none',
                    borderRadius: '8px'
                  }}
                  allow="camera *; microphone *; fullscreen"
                  loading="lazy"
                />
                
                {/* RPM Instructions */}
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-background/90 backdrop-blur-sm rounded-lg p-3 text-center">
                    <p className="text-sm text-muted-foreground">
                      Customize your avatar and click "Export" when ready
                    </p>
                  </div>
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="preview"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="h-full flex flex-col"
              >
                <div className="flex-1 relative">
                  <AvatarPreview avatarUrl={avatarUrl} />
                </div>
                
                <div className="pt-4 flex justify-center space-x-4">
                  <Button
                    onClick={handleCreateNew}
                    variant="outline"
                    className="glass-button"
                  >
                    Create New Avatar
                  </Button>
                  
                  <Button
                    onClick={handleClose}
                    className="brand-gradient hover:opacity-90 text-white"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Use This Avatar
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </DialogContent>
    </Dialog>
  );
}
