
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

export function Logo({ size = 'md', showText = true }: LogoProps) {
  const navigate = useNavigate();
  
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  };

  const textSizeClasses = {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl'
  };

  const handleClick = () => {
    navigate('/topics');
  };

  return (
    <motion.div 
      className="flex items-center gap-3 cursor-pointer"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      onClick={handleClick}
    >
      <div className={`${sizeClasses[size]} relative`}>
        <img 
          src="/lovable-uploads/4fd12ff6-660e-4077-8399-cc47ddfdbf40.png" 
          alt="Heey Logo" 
          className="w-full h-full object-contain filter drop-shadow-lg animate-float"
        />
      </div>
      {showText && (
        <motion.h1 
          className={`${textSizeClasses[size]} font-bold brand-gradient bg-clip-text text-transparent`}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          Heey
        </motion.h1>
      )}
    </motion.div>
  );
}
