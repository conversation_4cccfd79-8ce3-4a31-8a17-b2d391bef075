
import { memo } from 'react';

interface Particle {
  top: string;
  left: string;
  size: number;
  opacity: number;
  duration: number;
  delay: number;
  pulse: boolean;
}

interface OptimizedParticlesProps {
  particles: Particle[];
}

export const OptimizedParticles = memo(({ particles }: OptimizedParticlesProps) => {
  return (
    <>
      {particles.map((p, i) => (
        <div
          key={`p${i}`}
          className="particle pointer-events-none absolute rounded-full bg-white/80 will-change-transform"
          style={{
            top: p.top,
            left: p.left,
            width: `${p.size}rem`,
            height: `${p.size}rem`,
            animation: p.pulse 
              ? `particlePulse ${p.duration}s ease-in-out infinite`
              : `particleFade ${p.duration}s ease-in-out infinite`,
            animationDelay: `${p.delay}s`,
            contentVisibility: 'auto',
            containIntrinsicSize: `${p.size}rem ${p.size}rem`
          }}
        />
      ))}
    </>
  );
});

OptimizedParticles.displayName = 'OptimizedParticles';
