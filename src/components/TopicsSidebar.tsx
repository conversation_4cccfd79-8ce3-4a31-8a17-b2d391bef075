
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Clock, User, X, Plus } from 'lucide-react';
import { AppIcon } from '@/components/AppIcon';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

interface Topic {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: string;
  visitorName: string;
  messageCount: number;
}

interface TopicsSidebarProps {
  topics: Topic[];
  selectedTopic: string | null;
  onSelectTopic: (topicId: string) => void;
  onClose?: () => void;
  onNewChat?: () => void;
  className?: string;
}

export function TopicsSidebar({
  topics,
  selectedTopic,
  onSelectTopic,
  onClose,
  onNewChat,
  className
}: TopicsSidebarProps) {
  const { t } = useTranslation();
  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="p-4 glass-header border-b border-border/50">
        <div className="flex items-center justify-between gap-2">
          <h2 className="heading-md flex items-center gap-2">
            <AppIcon name="Conversation" alt={t('topics.chatTopics')} className="h-10 w-10 text-primary" />
            {t('topics.chatTopics')}
          </h2>
          <div className="flex items-center gap-1">
            {onNewChat && (
              <Button
                variant="outline"
                size="sm"
                onClick={onNewChat}
                className="glass-button"
                title={t('dashboard.actions.newChat')}
              >
                <Plus className="h-4 w-4 mr-1" />
                {t('dashboard.actions.newChat')}
              </Button>
            )}
            {onClose && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="md:hidden"
                title={t('common.close')}
              >
                <X className="h-10 w-10" />
              </Button>
            )}
          </div>
        </div>
        <p className="body-sm text-muted-foreground mt-1">
          {t('topics.conversationsCount', { count: topics.length })}
        </p>
      </div>

      {/* Topics List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3 custom-scrollbar">
        {topics.map((topic, index) => (
          <motion.div
            key={topic.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <Card
              className={cn(
                "glass-card cursor-pointer transition-all duration-200 hover:glass-modal",
                selectedTopic === topic.id && "ring-2 ring-primary/50 glass-modal"
              )}
              onClick={() => onSelectTopic(topic.id)}
            >
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="heading-sm line-clamp-1 flex-1">
                    {topic.title}
                  </h3>
                  <span className="body-sm text-muted-foreground ml-2 flex-shrink-0">
                    {topic.messageCount}
                  </span>
                </div>
                
                <p className="body-sm text-muted-foreground line-clamp-2 mb-3">
                  {topic.lastMessage}
                </p>
                
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    <span className="truncate max-w-20">{topic.visitorName}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{topic.timestamp}</span>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}

        {topics.length === 0 && (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <AppIcon name="Conversation" alt="No conversations" className="h-12 w-12 text-muted-foreground/50 mb-4" />
            <h3 className="heading-sm text-muted-foreground mb-2">
              {t('topics.noConversations')}
            </h3>
            <p className="body-sm text-muted-foreground">
              {t('topics.noConversationsDescription')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
