
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';

interface RewardsSection {
  id: string;
  title: string;
  description: string;
  icon: string;
}

interface RewardsSidebarProps {
  sections: RewardsSection[];
  selectedSection: string | null;
  onSelectSection: (sectionId: string) => void;
  onClose?: () => void;
  className?: string;
}

export function RewardsSidebar({
  sections,
  selectedSection,
  onSelectSection,
  onClose,
  className
}: RewardsSidebarProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();

  const handleSectionClick = (section: RewardsSection) => {
    navigate('/rewards');
    onSelectSection(section.id);
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Enhanced Header with improved glassmorphism */}
      <div className="p-4 bg-[#001B23]/80 backdrop-blur-lg shadow-lg shadow-black/20">
        <div className="flex items-center justify-between">
          <h2 className="heading-md flex items-center gap-2">
            <AppIcon name="Diamond" alt="Rewards" className="h-10 w-10 text-gradient" />
            Rewards Center
          </h2>
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="md:hidden"
            >
              <X className="h-10 w-10" />
            </Button>
          )}
        </div>
        <p className="body-sm text-muted-foreground mt-1">
          Earn $HEEY through tasks, referrals, and more
        </p>
      </div>

      {/* Enhanced Sections List with improved glass cards */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3 custom-scrollbar">
        {sections.map((section, index) => (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <Card
              className={cn(
                "bg-[#001B23]/60 backdrop-blur-lg shadow-xl shadow-black/30 rounded-xl cursor-pointer transition-all duration-200 hover:bg-[#001B23]/75 hover:shadow-2xl hover:shadow-black/35",
                selectedSection === section.id && "ring-2 ring-[#1DEBF6]/50 bg-[#001B23]/75 shadow-2xl shadow-black/35"
              )}
              onClick={() => handleSectionClick(section)}
            >
              <div className="p-4">
                <div className="flex items-center gap-3 mb-2">
                  <AppIcon name={section.icon} alt={section.title} className="h-10 w-10 text-gradient" />
                  <h3 className="heading-sm">
                    {section.title}
                  </h3>
                </div>
                
                <p className="body-sm text-muted-foreground">
                  {section.description}
                </p>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
