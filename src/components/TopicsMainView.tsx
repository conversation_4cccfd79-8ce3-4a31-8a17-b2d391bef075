
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { User, Clock, Hash } from 'lucide-react';
import { AppIcon } from '@/components/AppIcon';
import { useTranslation } from 'react-i18next';

interface Topic {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: string;
  visitorName: string;
  messageCount: number;
}

interface TopicsMainViewProps {
  selectedTopic: Topic | undefined;
}

export function TopicsMainView({ selectedTopic }: TopicsMainViewProps) {
  const { t } = useTranslation();
  if (!selectedTopic) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-md"
        >
          <AppIcon name="Conversation" alt={t('topics.selectConversation')} className="h-16 w-16 text-muted-foreground/50 mx-auto mb-6" />
          <h2 className="heading-lg mb-3 text-muted-foreground">
            {t('topics.selectConversation')}
          </h2>
          <p className="body-md text-muted-foreground">
            {t('topics.chooseTopic')}
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-4 md:p-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-4xl mx-auto"
      >
        {/* Topic Header */}
        <Card className="glass-card mb-6">
          <div className="p-6">
            <div className="flex items-start justify-between mb-4">
              <h1 className="heading-lg flex-1">
                {selectedTopic.title}
              </h1>
              <div className="flex items-center gap-2 text-muted-foreground ml-4">
                <Hash className="h-4 w-4" />
                <span className="body-sm">{selectedTopic.messageCount} {t('dashboard.stats.conversations').toLowerCase()}</span>
              </div>
            </div>
            
            <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span className="body-sm">{selectedTopic.visitorName}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span className="body-sm">{t('topics.lastActive', { time: selectedTopic.timestamp })}</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Topic Preview */}
        <Card className="glass-card">
          <div className="p-6">
            <h3 className="heading-sm mb-4 text-muted-foreground">
              {t('topics.latestMessage')}
            </h3>
            <div className="glass-panel p-4 rounded-2xl">
              <p className="body-md text-foreground">
                {selectedTopic.lastMessage}
              </p>
            </div>
            
            <div className="mt-6 text-center">
              <p className="body-sm text-muted-foreground">
                {t('topics.readOnly')}
              </p>
            </div>
          </div>
        </Card>
      </motion.div>
    </div>
  );
}
