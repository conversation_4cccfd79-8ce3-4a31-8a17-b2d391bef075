
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

interface FirstTimeVisitorFormProps {
  isOpen: boolean;
  onSubmit: (name: string, linkedin: string) => void;
}

export function FirstTimeVisitorForm({ isOpen, onSubmit }: FirstTimeVisitorFormProps) {
  const [name, setName] = useState('');
  const [linkedin, setLinkedin] = useState('');

  if (!isOpen) return null;

  return (
    <motion.div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <motion.div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
      <motion.div initial={{ scale: 0.9, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} className="relative z-10 w-full max-w-sm">
        <Card className="glass-panel">
          <CardHeader>
            <CardTitle className="text-lg">Let's Get to Know You ✨</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">What's your name?</Label>
              <Input id="name" value={name} onChange={(e) => setName(e.target.value)} className="glass-button mt-2" placeholder="Your awesome name" />
            </div>
            <div>
              <Label htmlFor="linkedin">LinkedIn (if you're feeling fancy)</Label>
              <Input id="linkedin" value={linkedin} onChange={(e) => setLinkedin(e.target.value)} className="glass-button mt-2" placeholder="linkedin.com/in/yourname" />
            </div>
            <Button onClick={() => onSubmit(name, linkedin)} className="w-full brand-gradient text-white">
              Let's Gooo! 🚀
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
