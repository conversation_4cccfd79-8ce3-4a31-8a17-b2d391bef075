
import { memo } from 'react';

interface LightweightImageMarqueeProps {
  images: string[];
  direction?: 'left' | 'right';
  speed?: number;
}

export const LightweightImageMarquee = memo(({ 
  images, 
  direction = 'left', 
  speed = 20 
}: LightweightImageMarqueeProps) => {
  // Create a seamless loop by duplicating images
  const duplicatedImages = [...images, ...images];
  
  return (
    <div className="overflow-hidden w-full">
      <div
        className={`lightweight-marquee flex items-center gap-4 whitespace-nowrap ${
          direction === 'left' ? 'animate-marquee-left' : 'animate-marquee-right'
        }`}
        style={{
          animationDuration: `${speed}s`,
        }}
      >
        {duplicatedImages.map((src, i) => (
          <img
            key={`${src}-${i}`}
            src={src}
            alt="avatar"
            loading="lazy"
            decoding="async"
            width={96}
            height={96}
            className="w-24 h-24 shrink-0 rounded-full object-cover"
          />
        ))}
      </div>
    </div>
  );
});

LightweightImageMarquee.displayName = 'LightweightImageMarquee';
