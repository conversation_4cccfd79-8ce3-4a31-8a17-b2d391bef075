
import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage?: number;
  fps?: number;
}

interface PerformanceMonitorProps {
  onMetrics?: (metrics: PerformanceMetrics) => void;
  enableLogging?: boolean;
}

export function PerformanceMonitor({ 
  onMetrics, 
  enableLogging = false 
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    // FPS monitoring
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        
        setMetrics(prev => ({
          ...prev!,
          fps
        }));
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };

    // Performance metrics collection
    const collectMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
      const renderTime = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
      
      // Memory usage (if supported)
      let memoryUsage: number | undefined;
      if ('memory' in performance) {
        const memory = (performance as Performance & { memory: { usedJSHeapSize: number } }).memory;
        memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
      }

      const newMetrics: PerformanceMetrics = {
        loadTime,
        renderTime,
        memoryUsage,
        fps: 0
      };

      setMetrics(newMetrics);
      onMetrics?.(newMetrics);

      if (enableLogging) {
        console.group('🔍 Performance Metrics');
        console.log('Load Time:', `${loadTime.toFixed(2)}ms`);
        console.log('Render Time:', `${renderTime.toFixed(2)}ms`);
        if (memoryUsage) {
          console.log('Memory Usage:', `${memoryUsage.toFixed(2)}MB`);
        }
        console.groupEnd();
      }
    };

    // Collect initial metrics
    if (document.readyState === 'complete') {
      collectMetrics();
    } else {
      window.addEventListener('load', collectMetrics);
    }

    // Start FPS monitoring
    measureFPS();

    // Performance observer for additional metrics
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (enableLogging) {
            console.log(`Performance entry: ${entry.name} - ${entry.duration}ms`);
          }
        });
      });

      try {
        observer.observe({ entryTypes: ['measure', 'navigation'] });
      } catch (error) {
        console.warn('PerformanceObserver not fully supported:', error);
      }

      return () => {
        observer.disconnect();
        cancelAnimationFrame(animationId);
        window.removeEventListener('load', collectMetrics);
      };
    }

    return () => {
      cancelAnimationFrame(animationId);
      window.removeEventListener('load', collectMetrics);
    };
  }, [onMetrics, enableLogging]);

  // Monitor long tasks
  useEffect(() => {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 50) { // Tasks longer than 50ms
            if (enableLogging) {
              console.warn(`🐌 Long task detected: ${entry.duration}ms`);
            }
          }
        });
      });

      try {
        observer.observe({ entryTypes: ['longtask'] });
        return () => observer.disconnect();
      } catch (error) {
        console.warn('Long task monitoring not supported:', error);
      }
    }
  }, [enableLogging]);

  // Don't render anything in production
  if (!enableLogging || process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs font-mono z-50">
      {metrics && (
        <div className="space-y-1">
          <div>Load: {metrics.loadTime.toFixed(1)}ms</div>
          <div>Render: {metrics.renderTime.toFixed(1)}ms</div>
          {metrics.memoryUsage && (
            <div>Memory: {metrics.memoryUsage.toFixed(1)}MB</div>
          )}
          {metrics.fps !== undefined && (
            <div>FPS: {metrics.fps}</div>
          )}
        </div>
      )}
    </div>
  );
}
