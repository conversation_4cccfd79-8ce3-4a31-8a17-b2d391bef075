
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { PageHero } from '@/components/PageHero';
import { SetLinksPanel } from '@/components/SetLinksPanel';
import { PreviewPanel } from '@/components/PreviewPanel';
import { SocialSharePanel } from '@/components/SocialSharePanel';
import { RoomSelectionPanel } from '@/components/RoomSelectionPanel';
import { AppIcon } from '@/components/AppIcon';
import { useTranslation } from 'react-i18next';

interface BioLinksMainViewProps {
  selectedSection: string | null;
}

export function BioLinksMainView({ selectedSection }: BioLinksMainViewProps) {
  const { t } = useTranslation();
  
  if (!selectedSection) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-md"
        >
          <AppIcon name="Arrow" alt="Select" className="h-16 w-16 text-muted-foreground/50 mx-auto mb-6" />
          <h2 className="heading-lg mb-3 text-muted-foreground">
            Select a section
          </h2>
          <p className="body-md text-muted-foreground">
            Choose from the sidebar to get started
          </p>
        </motion.div>
      </div>
    );
  }

  const getSectionConfig = () => {
    switch (selectedSection) {
      case 'set-links':
        return {
          iconName: 'Link',
          title: 'Set Links',
          description: 'Create and manage your bio links collection'
        };
      case 'preview':
        return {
          iconName: 'PlayCircle',
          title: 'Preview',
          description: 'Preview how your bio links will appear to visitors'
        };
      case 'social-share':
        return {
          iconName: 'Share',
          title: 'Social Share',
          description: 'Share your bio links across social media platforms'
        };
      default:
        return null;
    }
  };

  const sectionConfig = getSectionConfig();

  const renderSection = () => {
    switch (selectedSection) {
      case 'set-links':
        return <SetLinksPanel />;
      case 'preview':
        return <PreviewPanel />;
      case 'social-share':
        return <SocialSharePanel />;
      default:
        return null;
    }
  };

  return (
    <div className="flex-1 p-4 md:p-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-4xl mx-auto"
      >
        {sectionConfig && (
          <div className="mb-8">
            <PageHero
              iconName={sectionConfig.iconName}
              title={sectionConfig.title}
              description={sectionConfig.description}
            />
          </div>
        )}
        {renderSection()}
      </motion.div>
    </div>
  );
}
