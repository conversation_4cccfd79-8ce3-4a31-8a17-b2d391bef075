
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AppIcon } from '@/components/AppIcon';
import { PaymentMethodSelector } from './PaymentMethodSelector';
import { subscriptionPlans } from './subscriptionData';

interface SubscriptionPlansProps {
  currentPlan?: string;
  onPlanSelect?: (planId: string, paymentMethod: string) => void;
  className?: string;
}

export function SubscriptionPlans({ 
  currentPlan = null, 
  onPlanSelect,
  className = "" 
}: SubscriptionPlansProps) {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const handlePlanSelection = (planId: string, paymentMethod: string) => {
    onPlanSelect?.(planId, paymentMethod);
    setSelectedPlan(null); // Reset selection after processing
  };

  const handleSubscribeClick = (planId: string) => {
    setSelectedPlan(planId);
  };

  const isCurrentPlan = (planId: string) => currentPlan === planId;

  return (
    <div className={`space-y-8 ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {subscriptionPlans.map((plan, index) => (
          <motion.div
            key={plan.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            className="h-full"
          >
            <Card className={`relative glass-card h-full flex flex-col ${
              isCurrentPlan(plan.id) ? 'ring-2 ring-primary/50' : ''
            }`}>
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-to-r from-[#1DEBF6] to-[#83FFBC] text-background px-4 py-1 text-sm font-medium">
                    Most Popular
                  </Badge>
                </div>
              )}
              
              {isCurrentPlan(plan.id) && (
                <div className="absolute -top-3 right-4">
                  <Badge className="bg-green-500 text-white px-3 py-1 text-sm font-medium">
                    Current Plan
                  </Badge>
                </div>
              )}

              <CardHeader className="pb-4">
                <div className="space-y-2">
                  <CardTitle className="text-xl font-bold">{plan.name}</CardTitle>
                  <p className="text-muted-foreground text-sm">{plan.tagline}</p>
                  <div className="flex items-baseline space-x-1">
                    <span className="text-3xl font-bold text-gradient">
                      ${plan.price}
                    </span>
                    <span className="text-muted-foreground">/month</span>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="flex-1 flex flex-col justify-between">
                <div className="space-y-4 mb-6">
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-3">
                        <AppIcon name="Ok" alt="Feature" className="w-4 h-4 text-primary flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-auto">
                  {isCurrentPlan(plan.id) ? (
                    <Button 
                      className="w-full" 
                      variant="outline" 
                      disabled
                    >
                      <AppIcon name="Ok" alt="Current" className="w-4 h-4 mr-2" />
                      Current Plan
                    </Button>
                  ) : (
                    <Button 
                      className="w-full" 
                      onClick={() => handleSubscribeClick(plan.id)}
                      variant={plan.popular ? "default" : "outline"}
                    >
                      <AppIcon name="Crown" alt="Upgrade" className="w-4 h-4 mr-2" />
                      {currentPlan ? 'Upgrade Now' : 'Subscribe'}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {selectedPlan && (
        <PaymentMethodSelector
          planId={selectedPlan}
          planName={subscriptionPlans.find(p => p.id === selectedPlan)?.name || ''}
          planPrice={subscriptionPlans.find(p => p.id === selectedPlan)?.price || 0}
          onPaymentSelect={handlePlanSelection}
          onCancel={() => setSelectedPlan(null)}
        />
      )}
    </div>
  );
}
