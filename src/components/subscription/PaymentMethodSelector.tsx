
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AppIcon } from '@/components/AppIcon';
import { StripeCheckoutMock } from './StripeCheckoutMock';
import { HeeyWalletCheckoutMock } from './HeeyWalletCheckoutMock';

interface PaymentMethodSelectorProps {
  planId: string;
  planName: string;
  planPrice: number;
  onPaymentSelect: (planId: string, paymentMethod: string) => void;
  onCancel: () => void;
}

export function PaymentMethodSelector({
  planId,
  planName,
  planPrice,
  onPaymentSelect,
  onCancel
}: PaymentMethodSelectorProps) {
  const [selectedMethod, setSelectedMethod] = useState<string>('stripe');

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
      onClick={onCancel}
    >
      <Card 
        className="glass-card w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold">Complete Your Subscription</CardTitle>
              <p className="text-muted-foreground mt-1">
                {planName} - <span className="text-gradient font-semibold">${planPrice}/month</span>
              </p>
            </div>
            <Button variant="ghost" size="sm" onClick={onCancel}>
              <AppIcon name="Cancel" alt="Close" className="w-5 h-5" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <Tabs value={selectedMethod} onValueChange={setSelectedMethod}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="stripe" className="flex items-center space-x-2">
                <AppIcon name="Card" alt="Stripe" className="w-4 h-4" />
                <span>Pay with Stripe</span>
              </TabsTrigger>
              <TabsTrigger value="heey-wallet" className="flex items-center space-x-2">
                <img src="/3D-coin/heey-wallet-icon.webp" alt="Heey Wallet" className="w-4 h-4" />
                <span>Pay with Heey Wallet</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="stripe" className="mt-6">
              <StripeCheckoutMock
                planId={planId}
                planName={planName}
                planPrice={planPrice}
                onPaymentComplete={(planId) => onPaymentSelect(planId, 'stripe')}
              />
            </TabsContent>

            <TabsContent value="heey-wallet" className="mt-6">
              <HeeyWalletCheckoutMock
                planId={planId}
                planName={planName}
                planPrice={planPrice}
                onPaymentComplete={(planId) => onPaymentSelect(planId, 'heey-wallet')}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </motion.div>
  );
}
