
export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  tagline: string;
  features: string[];
  popular?: boolean;
}

export const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: 'personal-brand-promoter',
    name: 'Personal Brand Promoter',
    price: 8.5,
    tagline: 'Perfect for growing your personal online presence',
    features: [
      'Up to 5 AI avatars',
      'Basic customization options',
      'Standard support',
      'Monthly content templates',
      'Basic analytics dashboard'
    ]
  },
  {
    id: 'social-media-builder',
    name: 'Social Media Builder',
    price: 14.5,
    tagline: 'Ideal for managing 1-2 active content channels',
    features: [
      'Up to 15 AI avatars',
      'Advanced customization',
      'Priority support',
      'Multi-platform scheduling',
      'Enhanced analytics'
    ],
    popular: true
  },
  {
    id: 'influencer-kols',
    name: 'Influencer or KOLs',
    price: 24.5,
    tagline: 'For key opinion leaders managing multiple platforms',
    features: [
      'Up to 50 AI avatars',
      'Premium customization',
      'White-label options',
      'Advanced collaboration tools',
      'Professional analytics suite'
    ]
  },
  {
    id: 'project-owner',
    name: 'Project Owner',
    price: 38.5,
    tagline: 'For professional teams and startup founders',
    features: [
      'Unlimited AI avatars',
      'Full customization control',
      'Dedicated account manager',
      'Team collaboration features',
      'Enterprise analytics & reporting'
    ]
  }
];
