
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AppIcon } from '@/components/AppIcon';

interface StripeCheckoutMockProps {
  planId: string;
  planName: string;
  planPrice: number;
  onPaymentComplete: (planId: string) => void;
}

export function StripeCheckoutMock({
  planId,
  planName,
  planPrice,
  onPaymentComplete
}: StripeCheckoutMockProps) {
  const [isProcessing, setIsProcessing] = useState(false);

  const handlePayment = async () => {
    setIsProcessing(true);
    
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      onPaymentComplete(planId);
    }, 2000);
  };

  return (
    <Card className="glass-card">
      <CardContent className="p-6 space-y-4">
        <div className="flex items-center space-x-3 mb-4">
          <AppIcon name="Card" alt="Stripe" className="w-6 h-6 text-primary" />
          <div>
            <h3 className="font-semibold">Stripe Checkout</h3>
            <p className="text-sm text-muted-foreground">Secure payment processing</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4">
            <div className="flex justify-between items-center">
              <span className="text-sm">Subscription</span>
              <span className="font-semibold">{planName}</span>
            </div>
            <div className="flex justify-between items-center mt-2">
              <span className="text-sm">Monthly charge</span>
              <span className="font-semibold text-gradient">${planPrice}</span>
            </div>
          </div>

          <div className="space-y-3">
            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-3">
              <p className="text-sm text-muted-foreground mb-2">Card Information</p>
              <div className="space-y-2">
                <div className="bg-white/10 backdrop-blur-sm rounded px-3 py-2 text-sm">
                  **** **** **** 4242 (Demo Card)
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="bg-white/10 backdrop-blur-sm rounded px-3 py-2 text-sm">
                    12/28
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded px-3 py-2 text-sm">
                    ***
                  </div>
                </div>
              </div>
            </div>

            <Button 
              className="w-full" 
              onClick={handlePayment}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <AppIcon name="ShieldCheck" alt="Secure" className="w-4 h-4 mr-2" />
                  Pay ${planPrice}/month
                </>
              )}
            </Button>
          </div>
        </div>

        <p className="text-xs text-muted-foreground text-center">
          This is a demo checkout. No actual payment will be processed.
        </p>
      </CardContent>
    </Card>
  );
}
