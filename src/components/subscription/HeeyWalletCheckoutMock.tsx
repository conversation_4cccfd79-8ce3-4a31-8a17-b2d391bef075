
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AppIcon } from '@/components/AppIcon';

interface HeeyWalletCheckoutMockProps {
  planId: string;
  planName: string;
  planPrice: number;
  onPaymentComplete: (planId: string) => void;
}

export function HeeyWalletCheckoutMock({
  planId,
  planName,
  planPrice,
  onPaymentComplete
}: HeeyWalletCheckoutMockProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const mockWalletBalance = 125.50;

  const handlePayment = async () => {
    setIsProcessing(true);
    
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      onPaymentComplete(planId);
    }, 1500);
  };

  return (
    <Card className="glass-card">
      <CardContent className="p-6 space-y-4">
        <div className="flex items-center space-x-3 mb-4">
          <img src="/3D-coin/heey-wallet-icon.webp" alt="Heey Wallet" className="w-6 h-6" />
          <div>
            <h3 className="font-semibold">Heey Wallet</h3>
            <p className="text-sm text-muted-foreground">Pay with your Heey balance</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4">
            <div className="flex justify-between items-center">
              <span className="text-sm">Subscription</span>
              <span className="font-semibold">{planName}</span>
            </div>
            <div className="flex justify-between items-center mt-2">
              <span className="text-sm">Monthly charge</span>
              <span className="font-semibold text-gradient">${planPrice}</span>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <img src="/3D-coin/heycoin-gold.webp" alt="Heey Coin" className="w-8 h-8" />
                <div>
                  <p className="font-semibold">Wallet Balance</p>
                  <p className="text-sm text-muted-foreground">Available funds</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-bold text-lg text-gradient">${mockWalletBalance}</p>
                <p className="text-xs text-muted-foreground">
                  {mockWalletBalance >= planPrice ? '✓ Sufficient balance' : '⚠ Insufficient balance'}
                </p>
              </div>
            </div>
          </div>

          <Button 
            className="w-full" 
            onClick={handlePayment}
            disabled={isProcessing || mockWalletBalance < planPrice}
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                Processing...
              </>
            ) : mockWalletBalance < planPrice ? (
              <>
                <AppIcon name="Cancel" alt="Insufficient" className="w-4 h-4 mr-2" />
                Insufficient Balance
              </>
            ) : (
              <>
                <img src="/3D-coin/heycoin-gold.webp" alt="Pay" className="w-4 h-4 mr-2" />
                Pay ${planPrice}/month
              </>
            )}
          </Button>

          {mockWalletBalance < planPrice && (
            <Button variant="outline" className="w-full">
              <AppIcon name="BagMoney" alt="Add funds" className="w-4 h-4 mr-2" />
              Add Funds to Wallet
            </Button>
          )}
        </div>

        <p className="text-xs text-muted-foreground text-center">
          This is a demo wallet. No actual payment will be processed.
        </p>
      </CardContent>
    </Card>
  );
}
