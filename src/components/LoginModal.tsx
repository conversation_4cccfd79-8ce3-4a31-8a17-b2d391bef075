
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2, Chrome } from 'lucide-react';
import { Logo } from './Logo';
import { useTranslation } from 'react-i18next';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export function LoginModal({ isOpen, onClose, onSuccess }: LoginModalProps) {
  const [loading, setLoading] = useState(false);
  const { signInWithProvider } = useAuth();
  const { t } = useTranslation();

  const handleGoogleSignIn = async () => {
    setLoading(true);
    const { error } = await signInWithProvider('google');
    
    if (!error) {
      onSuccess?.();
      onClose();
    }
    
    setLoading(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="glass-panel border-white/20 max-w-md">
        <DialogHeader>
          <div className="flex justify-center mb-4">
            <Logo size="md" />
          </div>
          <DialogTitle className="brand-gradient bg-clip-text text-transparent text-center text-2xl">
            {t('loginModal.title')}
          </DialogTitle>
          <DialogDescription className="text-center text-muted-foreground">
            {t('loginModal.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <Button
            onClick={handleGoogleSignIn}
            disabled={loading}
            className="w-full bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 flex items-center justify-center space-x-3 py-6 text-base font-medium shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Chrome className="w-5 h-5" />
            )}
            <span>
              {loading ? t('loginModal.signingIn') : t('loginModal.signInWithGoogle')}
            </span>
          </Button>

          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              {t('loginModal.termsText')}{' '}
              <a href="#" className="underline hover:text-primary">{t('loginModal.termsOfService')}</a>
              {' '}and{' '}
              <a href="#" className="underline hover:text-primary">{t('loginModal.privacyPolicy')}</a>
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
