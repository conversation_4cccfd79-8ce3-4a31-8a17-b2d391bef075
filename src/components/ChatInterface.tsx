
// Chat Interface Component
// Real-time chat interface with AI responses and visitor management

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Send, Smile, Paperclip, MoreVertical, User, Bot, X, Loader } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { chatService } from '@/lib/chatService';
import { createAIService, defaultAIConfig } from '@/lib/aiService';
import { chatValidator } from '@/lib/chatValidation';
import type { 
  ChatMessage, 
  ChatSession, 
  Visitor,
  ChatEvent,
  AIPersonality 
} from '@/types/chat';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface ChatInterfaceProps {
  profileOwnerId?: string;
  profileOwnerName?: string;
  profileOwnerAvatar?: string;
  aiPersonality?: AIPersonality;
  onClose?: () => void;
  className?: string;
  requireAuth?: boolean;
  isLoggedIn?: boolean;
  onAuthRequired?: () => void;
  placeholder?: string;
}

interface ChatState {
  session: ChatSession | null;
  messages: ChatMessage[];
  visitor: Visitor | null;
  isLoading: boolean;
  isTyping: boolean;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  profileOwnerId,
  profileOwnerName = 'Assistant',
  profileOwnerAvatar,
  aiPersonality,
  onClose,
  className = '',
  requireAuth = false,
  isLoggedIn = false,
  onAuthRequired,
  placeholder
}) => {
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [currentMessage, setCurrentMessage] = useState('');
  const [visitorInfo, setVisitorInfo] = useState({
    displayName: '',
    email: ''
  });

  // Determine owner id (allow preview/demo usage without explicit id)
  const ownerId = useMemo(() => profileOwnerId || 'demo', [profileOwnerId]);
  const [showVisitorForm, setShowVisitorForm] = useState(true);

  const [chatState, setChatState] = useState<ChatState>({
    session: null,
    messages: [],
    visitor: null,
    isLoading: false,
    isTyping: false,
    connectionStatus: 'disconnected'
  });

  // Initialize AI service
  const aiService = useMemo(() => {
    const config = {
      ...defaultAIConfig,
      personality: aiPersonality || defaultAIConfig.personality
    };
    return createAIService(config);
  }, [aiPersonality]);

  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Load messages for current session
  const loadMessages = useCallback(async (chatId: string) => {
    try {
      const result = await chatService.getChatMessages(chatId);
      if (result.success && result.data) {
        setChatState(prev => ({ ...prev, messages: result.data || [] }));
        setTimeout(scrollToBottom, 100);
      }
    } catch (error) {
      console.error('Failed to load messages:', error);
    }
  }, [scrollToBottom]);

  // Send welcome message from AI
  const sendAIWelcomeMessage = useCallback(async (chatId: string, visitorName: string) => {
    try {
      const welcomeMessages = aiPersonality?.conversation_starters || [
        `Hi ${visitorName}! Welcome to my page. How can I help you today?`,
        `Hello ${visitorName}! Thanks for visiting. What would you like to know?`,
        `Hey there ${visitorName}! I'm here to help. What's on your mind?`
      ];

      const welcomeMessage = welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)];

      await chatService.sendMessage(
        chatId,
        'ai-assistant',
        'ai',
        welcomeMessage
      );

      // Refresh messages
      await loadMessages(chatId);
    } catch (error) {
      console.error('Failed to send welcome message:', error);
    }
  }, [aiPersonality, loadMessages]);

  // Initialize chat session
  const initializeChat = useCallback(async () => {
    if (!visitorInfo.displayName.trim()) return;

    setChatState(prev => ({ ...prev, isLoading: true, connectionStatus: 'connecting' }));

    try {
      // Create or update visitor
      const visitorResult = await chatService.createOrUpdateVisitor(ownerId, {
        display_name: visitorInfo.displayName,
        email: visitorInfo.email || undefined,
        metadata: {
          source: 'direct',
          device_type: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
          browser: navigator.userAgent.split(' ').pop()?.split('/')[0]
        }
      });

      if (!visitorResult.success || !visitorResult.data) {
        throw new Error(visitorResult.error?.message || 'Failed to create visitor');
      }

      const visitor = visitorResult.data;

      // Create chat session
      const sessionResult = await chatService.createChatSession(ownerId, visitor.id);
      
      if (!sessionResult.success || !sessionResult.data) {
        throw new Error(sessionResult.error?.message || 'Failed to create chat session');
      }

      const session = sessionResult.data;

      setChatState(prev => ({
        ...prev,
        session,
        visitor,
        connectionStatus: 'connected',
        isLoading: false
      }));

      setShowVisitorForm(false);

      // Send welcome message
      await sendAIWelcomeMessage(session.id, visitor.display_name || 'there');

      toast({
        title: 'Chat started',
        description: `Welcome to the chat, ${visitor.display_name}!`
      });
    } catch (error) {
      console.error('Chat initialization error:', error);
      setChatState(prev => ({ 
        ...prev, 
        isLoading: false, 
        connectionStatus: 'disconnected' 
      }));
      toast({
        title: 'Connection failed',
        description: error instanceof Error ? error.message : 'Failed to start chat',
        variant: 'destructive'
      });
    }
  }, [ownerId, visitorInfo, toast, sendAIWelcomeMessage]);

  // Generate AI response
  const generateAIResponse = useCallback(async (chatId: string, userMessage: string) => {
    setChatState(prev => ({ ...prev, isTyping: true }));

    try {
      // Build chat context
      const contextResult = await chatService.buildChatContext(chatState.session?.session_id || '');
      if (!contextResult.success || !contextResult.data) {
        throw new Error('Failed to build chat context');
      }

      // Generate AI response
      const aiResponse = await aiService.generateResponse(userMessage, contextResult.data);
      
      if (!aiResponse.success || !aiResponse.data) {
        throw new Error('Failed to generate AI response');
      }

      // Send AI message
      await chatService.sendMessage(
        chatId,
        'ai-assistant',
        'ai',
        aiResponse.data.content
      );

      // Refresh messages
      await loadMessages(chatId);

      // Extract and save topics if AI response indicates new topics
      try {
        const topicsResult = await aiService.extractTopicsFromConversation([
          { 
            id: 'temp', 
            chat_id: chatId, 
            sender_type: 'visitor', 
            sender_id: chatState.visitor?.id || '', 
            content: userMessage,
            message_type: 'text',
            created_at: new Date().toISOString()
          }
        ]);

        if (topicsResult.success && topicsResult.data) {
          for (const topic of topicsResult.data) {
            await chatService.saveChatTopic(
              chatId,
              ownerId,
              topic,
              'neutral',
              0.8
            );
          }
        }
      } catch (topicError) {
        console.warn('Failed to extract topics:', topicError);
      }

    } catch (error) {
      console.error('AI response generation error:', error);
      
      // Send fallback message
      await chatService.sendMessage(
        chatId,
        'ai-assistant',
        'ai',
        "I'm sorry, I'm having trouble responding right now. Could you try asking again?"
      );
      
      await loadMessages(chatId);
    } finally {
      setChatState(prev => ({ ...prev, isTyping: false }));
    }
  }, [chatState.session, chatState.visitor, ownerId, aiService, loadMessages]);

  // Send message
  const sendMessage = useCallback(async () => {
    if (!currentMessage.trim() || !chatState.session || !chatState.visitor) return;

    const messageContent = currentMessage.trim();
    setCurrentMessage('');

    // Validate message
    const validation = chatValidator.validateMessage({
      content: messageContent,
      chat_id: chatState.session.id,
      sender_id: chatState.visitor.id,
      sender_type: 'visitor'
    }, chatState.visitor.id);

    if (!validation.isValid) {
      toast({
        title: 'Message error',
        description: validation.errors.join(', '),
        variant: 'destructive'
      });
      return;
    }

    if (validation.warnings.length > 0) {
      toast({
        title: 'Message warning',
        description: validation.warnings.join(', '),
        variant: 'default'
      });
    }

    const finalContent = validation.filtered_content || messageContent;

    try {
      // Send user message
      const messageResult = await chatService.sendMessage(
        chatState.session.id,
        chatState.visitor.id,
        'visitor',
        finalContent
      );

      if (!messageResult.success) {
        throw new Error(messageResult.error?.message || 'Failed to send message');
      }

      // Refresh messages
      await loadMessages(chatState.session.id);

      // Generate AI response
      await generateAIResponse(chatState.session.id, finalContent);

    } catch (error) {
      console.error('Failed to send message:', error);
      toast({
        title: 'Send failed',
        description: error instanceof Error ? error.message : 'Failed to send message',
        variant: 'destructive'
      });
    }
  }, [currentMessage, chatState.session, chatState.visitor, toast, loadMessages, generateAIResponse]);

  // Handle enter key
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  }, [sendMessage]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [currentMessage]);

  // Format message timestamp
  const formatTimestamp = useCallback((timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  }, []);

  // Close chat
  const handleClose = useCallback(async () => {
    if (chatState.session) {
      try {
        await chatService.endChatSession(chatState.session.session_id);
      } catch (error) {
        console.error('Failed to end chat session:', error);
      }
    }
    onClose?.();
  }, [chatState.session, onClose]);

  // Render visitor info form
  const renderVisitorForm = () => (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-center">Start a conversation</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium">Your name *</label>
          <Input
            placeholder="Enter your name"
            value={visitorInfo.displayName}
            onChange={(e) => setVisitorInfo(prev => ({ ...prev, displayName: e.target.value }))}
            onKeyPress={(e) => e.key === 'Enter' && initializeChat()}
          />
        </div>
        <div>
          <label className="text-sm font-medium">Email (optional)</label>
          <Input
            type="email"
            placeholder="Enter your email"
            value={visitorInfo.email}
            onChange={(e) => setVisitorInfo(prev => ({ ...prev, email: e.target.value }))}
            onKeyPress={(e) => e.key === 'Enter' && initializeChat()}
          />
        </div>
        <Button 
          onClick={initializeChat}
          disabled={!visitorInfo.displayName.trim() || chatState.isLoading}
          className="w-full"
        >
          {chatState.isLoading ? <Loader className="w-4 h-4 mr-2 animate-spin" /> : null}
          Start Chat
        </Button>
      </CardContent>
    </Card>
  );

  // Render message bubble
  const renderMessage = useCallback((message: ChatMessage) => {
    const isAI = message.sender_type === 'ai';
    const isUser = message.sender_type === 'visitor';

    return (
      <div
        key={message.id}
        className={cn(
          "flex gap-2 mb-4",
          isUser ? "justify-end" : "justify-start"
        )}
      >
        {!isUser && (
          <Avatar className="w-8 h-8 flex-shrink-0">
            <AvatarImage src={profileOwnerAvatar} />
            <AvatarFallback>
              {isAI ? <Bot className="w-4 h-4" /> : <User className="w-4 h-4" />}
            </AvatarFallback>
          </Avatar>
        )}
        
        <div className={cn(
          "max-w-[80%] rounded-lg px-3 py-2",
          isUser 
            ? "bg-blue-500 text-white" 
            : "bg-gray-100 text-gray-900"
        )}>
          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
          <p className={cn(
            "text-xs mt-1",
            isUser ? "text-blue-100" : "text-gray-500"
          )}>
            {formatTimestamp(message.created_at)}
            {isAI && <Badge variant="secondary" className="ml-2 text-xs">AI</Badge>}
          </p>
        </div>

        {isUser && (
          <Avatar className="w-8 h-8 flex-shrink-0">
            <AvatarFallback>
              <User className="w-4 h-4" />
            </AvatarFallback>
          </Avatar>
        )}
      </div>
    );
  }, [profileOwnerAvatar, formatTimestamp]);

  // Authentication gate
  if (requireAuth && !isLoggedIn) {
    return (
      <div className={cn("flex items-center justify-center min-h-[300px] p-4", className)}>
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center">Sign in to start chatting</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button className="w-full" onClick={onAuthRequired}>Sign in</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (showVisitorForm) {
    return (
      <div className={cn("flex items-center justify-center min-h-[400px] p-4", className)}>
        {renderVisitorForm()}
      </div>
    );
  }

  return (
    <Card className={cn("flex flex-col h-[600px]", className)}>
      {/* Header */}
      <CardHeader className="flex-shrink-0 pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="w-10 h-10">
              <AvatarImage src={profileOwnerAvatar} />
              <AvatarFallback><User className="w-5 h-5" /></AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold">{profileOwnerName}</h3>
              <div className="flex items-center gap-2">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  chatState.connectionStatus === 'connected' ? "bg-green-500" :
                  chatState.connectionStatus === 'connecting' ? "bg-yellow-500" : "bg-red-500"
                )} />
                <span className="text-sm text-gray-500 capitalize">
                  {chatState.connectionStatus}
                </span>
              </div>
            </div>
          </div>
          
          <Button variant="ghost" size="sm" onClick={handleClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>

      <Separator />

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-1">
          {chatState.messages.map(renderMessage)}
          
          {/* Typing indicator */}
          {chatState.isTyping && (
            <div className="flex gap-2 mb-4">
              <Avatar className="w-8 h-8 flex-shrink-0">
                <AvatarImage src={profileOwnerAvatar} />
                <AvatarFallback><Bot className="w-4 h-4" /></AvatarFallback>
              </Avatar>
              <div className="bg-gray-100 rounded-lg px-3 py-2">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-75" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-150" />
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      <Separator />

      {/* Input */}
      <div className="flex-shrink-0 p-4">
        <div className="flex gap-2">
          <Textarea
            ref={textareaRef}
            placeholder={placeholder || "Type your message..."}
            value={currentMessage}
            onChange={(e) => setCurrentMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            className="resize-none min-h-[40px] max-h-[120px]"
            rows={1}
            disabled={chatState.connectionStatus !== 'connected'}
          />
          <Button
            onClick={sendMessage}
            disabled={!currentMessage.trim() || chatState.connectionStatus !== 'connected'}
            size="icon"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ChatInterface;
