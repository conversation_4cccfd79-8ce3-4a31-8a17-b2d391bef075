
import { memo } from 'react';

interface OptimizedImageMarqueeProps {
  images: string[];
  direction?: 'left' | 'right';
  speed?: number;
}

export const OptimizedImageMarquee = memo(({ 
  images, 
  direction = 'left', 
  speed = 20 
}: OptimizedImageMarqueeProps) => {
  // Duplicate images for seamless loop
  const duplicatedImages = [...images, ...images];
  
  return (
    <div className="overflow-hidden w-full">
      <div
        className={`optimized-marquee flex items-center gap-4 whitespace-nowrap will-change-transform ${
          direction === 'left' ? 'animate-scroll-left' : 'animate-scroll-right'
        }`}
        style={{
          animationDuration: `${speed}s`,
        }}
      >
        {duplicatedImages.map((src, i) => (
          <img
            key={`${src}-${i}`}
            src={src}
            alt="avatar"
            loading="lazy"
            decoding="async"
            width={96}
            height={96}
            className="w-24 h-24 shrink-0 rounded-full object-cover"
            style={{
              contentVisibility: 'auto',
              containIntrinsicSize: '96px 96px'
            }}
          />
        ))}
      </div>
    </div>
  );
});

OptimizedImageMarquee.displayName = 'OptimizedImageMarquee';
