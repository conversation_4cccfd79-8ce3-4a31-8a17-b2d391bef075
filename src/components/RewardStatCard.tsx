
import { ReactNode } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface RewardStatCardProps {
  icon: ReactNode;
  value: ReactNode;
  label: string;
  className?: string;
}

export function RewardStatCard({ icon, value, label, className }: RewardStatCardProps) {
  return (
    <Card className={cn('glass-card text-center overflow-hidden', className)}>
      <CardContent className="pt-8 pb-6 px-4">
        <div className="w-16 h-16 primary-gradient/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-4 p-2">
          {icon}
        </div>
        <div className="text-3xl font-bold mb-2 text-foreground">{value}</div>
        <p className="text-sm text-muted-foreground">{label}</p>
      </CardContent>
    </Card>
  );
}
