
import { motion } from 'framer-motion';
import { Sparkles, Save, RefreshCw, Download, Share } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AvatarPreview } from '@/components/AvatarPreview';
import { useTranslation } from 'react-i18next';

interface AvatarPreviewSectionProps {
  avatarUrl: string;
  onSave: () => void;
  onCreateNew: () => void;
}

export function AvatarPreviewSection({ avatarUrl, onSave, onCreateNew }: AvatarPreviewSectionProps) {
  const { t } = useTranslation();

  return (
    <div className="grid gap-8 lg:grid-cols-3">
      {/* Avatar Preview */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6 }}
        className="lg:col-span-2"
      >
        <div className="glass-card rounded-2xl p-6 shadow-2xl">
          <div className="mb-4 flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            <h2 className="text-xl font-semibold">{t('avatarPreview.yourAvatar')}</h2>
          </div>
          <div className="aspect-square w-full rounded-xl bg-gradient-to-br from-muted/50 to-muted/20 border border-border/30">
            <AvatarPreview avatarUrl={avatarUrl} />
          </div>
        </div>
      </motion.div>

      {/* Avatar Info Panel */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="space-y-6"
      >
        <div className="glass-card rounded-2xl p-6 shadow-2xl">
          <div className="mb-6">
            <h3 className="mb-2 text-lg font-semibold">{t('avatarPreview.avatarReady')}</h3>
            <p className="text-sm text-muted-foreground">
              {t('avatarPreview.readyDescription')}
            </p>
          </div>

          <div className="mb-6 space-y-4">
            <div className="rounded-lg p-4 bg-background/20 backdrop-blur-sm">
              <div className="mb-2 flex items-center justify-between">
                <span className="text-sm font-medium">{t('avatarPreview.quality')}</span>
                <span className="text-sm font-medium text-primary">{t('avatarPreview.excellent')}</span>
              </div>
              <div className="h-2 w-full rounded-full bg-muted/60">
                <div className="h-2 w-5/6 rounded-full bg-gradient-to-r from-primary to-primary/80"></div>
              </div>
            </div>

            <div className="rounded-lg p-4 bg-background/20 backdrop-blur-sm">
              <div className="mb-2 flex items-center justify-between">
                <span className="text-sm font-medium">{t('avatarPreview.uniqueness')}</span>
                <span className="text-sm font-medium text-primary">{t('avatarPreview.perfect')}</span>
              </div>
              <div className="h-2 w-full rounded-full bg-muted/60">
                <div className="h-2 w-full rounded-full bg-gradient-to-r from-primary to-primary/80"></div>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <Button
              onClick={onSave}
              className="w-full gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 shadow-lg"
              size="lg"
            >
              <Save className="h-4 w-4" />
              {t('avatarPreview.saveAvatar')}
            </Button>
            
            <Button
              onClick={onCreateNew}
              variant="outline"
              className="w-full gap-2 glass-button"
            >
              <RefreshCw className="h-4 w-4" />
              {t('avatarPreview.createNew')}
            </Button>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex-1 gap-2 glass-button">
                <Download className="h-4 w-4" />
                {t('avatarPreview.export')}
              </Button>
              <Button variant="outline" size="sm" className="flex-1 gap-2 glass-button">
                <Share className="h-4 w-4" />
                {t('avatarPreview.share')}
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
