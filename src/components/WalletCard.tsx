
import { Card, CardContent } from '@/components/ui/card';
import { Coins } from 'lucide-react';
import { cn } from '@/lib/utils';

interface WalletCardProps {
  balance: number;
  className?: string;
}

export function WalletCard({ balance, className }: WalletCardProps) {
  return (
    <Card className={cn('glass-card text-center', className)}>
      <CardContent className="pt-6 pb-6">
        <div className="w-12 h-12 primary-gradient/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-4">
          <Coins className="w-6 h-6 text-gradient" />
        </div>
        <div className="text-2xl font-bold mb-1 text-foreground">{balance}</div>
        <p className="text-sm text-muted-foreground">$HEEY</p>
      </CardContent>
    </Card>
  );
}
