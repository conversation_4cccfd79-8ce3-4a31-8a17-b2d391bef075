
import { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AudioControlsProps {
  onPlayingChange: (isPlaying: boolean) => void;
  audioRef: React.RefObject<HTMLAudioElement>;
}

export function AudioControls({ onPlayingChange, audioRef }: AudioControlsProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.7);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handlePlay = () => {
      setIsPlaying(true);
      onPlayingChange(true);
    };

    const handlePause = () => {
      setIsPlaying(false);
      onPlayingChange(false);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      onPlayingChange(false);
    };

    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [onPlayingChange]);

  const togglePlayback = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play().catch(console.error);
    }
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
  };

  return (
    <div className="glass-panel rounded-2xl p-4 space-y-4">
      <div className="flex items-center space-x-4">
        <Button
          onClick={togglePlayback}
          size="lg"
          className="btn-primary rounded-full w-14 h-14"
        >
          {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
        </Button>
        
        <div className="flex items-center space-x-2 flex-1">
          <Volume2 className="w-5 h-5 text-muted-foreground" />
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={volume}
            onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
            className="flex-1 h-2 bg-white/10 rounded-lg appearance-none cursor-pointer"
          />
        </div>
      </div>
      
      <p className="text-sm text-muted-foreground text-center">
        {isPlaying ? 'Avatar is speaking...' : 'Click play to hear the avatar speak'}
      </p>
    </div>
  );
}
