
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useRTL } from '@/hooks/useRTL';
import { useNavigate } from 'react-router-dom';

export function WelcomeSection() {
  const { t } = useTranslation();
  const { isRTL } = useRTL();
  const navigate = useNavigate();

  return (
    <div className={`min-h-screen mobile-full-screen flex items-center justify-center p-8 ${isRTL ? 'rtl' : ''}`}>
      <div className="max-w-4xl mx-auto text-center space-y-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-6"
        >
          <h1 className="heading-xl text-gradient">
            {t('welcome.title')}
          </h1>
          <h2 className="heading-md text-muted-foreground">
            {t('welcome.subtitle')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            {t('welcome.description')}
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className={`flex flex-wrap justify-center gap-8 ${isRTL ? 'space-x-reverse' : ''}`}
        >
          <div className="text-center space-y-2">
            <div className="w-12 h-12 bg-lime-500/20 rounded-full flex items-center justify-center mx-auto">
              <span className="text-2xl">🤖</span>
            </div>
            <h3 className="font-semibold">{t('welcome.features.aiAvatar')}</h3>
          </div>
          <div className="text-center space-y-2">
            <div className="w-12 h-12 bg-lime-500/20 rounded-full flex items-center justify-center mx-auto">
              <span className="text-2xl">💬</span>
            </div>
            <h3 className="font-semibold">{t('welcome.features.smartConversations')}</h3>
          </div>
          <div className="text-center space-y-2">
            <div className="w-12 h-12 bg-lime-500/20 rounded-full flex items-center justify-center mx-auto">
              <span className="text-2xl">✨</span>
            </div>
            <h3 className="font-semibold">{t('welcome.features.personalBrand')}</h3>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className={`flex flex-col sm:flex-row gap-4 justify-center ${isRTL ? 'space-x-reverse' : ''}`}
        >
          <Button
            onClick={() => navigate('/login')}
            size="lg"
            className="btn-primary text-lg px-8 py-4 rounded-xl"
          >
            {t('welcome.getStarted')}
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="text-lg px-8 py-4 rounded-xl glass-button"
          >
            {t('welcome.learnMore')}
          </Button>
        </motion.div>
      </div>
    </div>
  );
}
