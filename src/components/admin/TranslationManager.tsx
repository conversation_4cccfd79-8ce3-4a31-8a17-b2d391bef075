// Translation Manager Component
// Admin interface for managing translations and language content

import React, { useState, useEffect } from 'react';
import { 
  Languages, 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  Search,
  Filter,
  Download,
  Upload,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Globe
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { languageManager, getCurrentLanguage, getAvailableLanguages } from '@/lib/languageManager';
import { translationValidator, validateTranslation, validateLanguageQuality } from '@/lib/translationValidation';
import type { 
  LanguageQualityReport, 
  TranslationIssue, 
  TranslationContext 
} from '@/lib/translationValidation';

interface TranslationEntry {
  key: string;
  value: string;
  context?: string;
  interpolations?: string[];
  pluralization?: Record<string, string>;
}

interface LanguageStats {
  code: string;
  name: string;
  totalKeys: number;
  translatedKeys: number;
  missingKeys: string[];
  completeness: number;
  qualityScore: number;
  lastUpdated: Date;
}

export const TranslationManager: React.FC = () => {
  const { toast } = useToast();
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [translations, setTranslations] = useState<Map<string, TranslationEntry>>(new Map());
  const [filteredTranslations, setFilteredTranslations] = useState<TranslationEntry[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'missing' | 'warnings' | 'errors'>('all');
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [editingValue, setEditingValue] = useState('');
  const [editingContext, setEditingContext] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [languageStats, setLanguageStats] = useState<Map<string, LanguageStats>>(new Map());
  const [qualityReport, setQualityReport] = useState<LanguageQualityReport | null>(null);
  const [newKeyName, setNewKeyName] = useState('');
  const [showAddKey, setShowAddKey] = useState(false);

  const availableLanguages = getAvailableLanguages();

  // Load translations for selected language
  useEffect(() => {
    loadTranslations();
  }, [selectedLanguage]);

  // Filter translations based on search and filter type
  useEffect(() => {
    filterTranslations();
  }, [translations, searchQuery, filterType, qualityReport]);

  const loadTranslations = async (): Promise<void> => {
    setIsLoading(true);
    try {
      await languageManager.loadTranslations(selectedLanguage);
      const translationsMap = languageManager.translations.get(selectedLanguage) || new Map();
      setTranslations(new Map(translationsMap));
      
      // Generate quality report
      const originalTranslations = languageManager.translations.get('en') || new Map();
      const translationValues = new Map(
        Array.from(translationsMap.entries()).map(([key, translation]) => [key, translation.value])
      );
      const originalValues = new Map(
        Array.from(originalTranslations.entries()).map(([key, translation]) => [key, translation.value])
      );
      
      const report = validateLanguageQuality(translationValues, selectedLanguage, originalValues);
      setQualityReport(report);
    } catch (error) {
      toast({
        title: 'Failed to load translations',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterTranslations = (): void => {
    const translationArray = Array.from(translations.values());
    let filtered = translationArray;

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(translation => 
        translation.key.toLowerCase().includes(query) ||
        translation.value.toLowerCase().includes(query) ||
        (translation.context && translation.context.toLowerCase().includes(query))
      );
    }

    // Apply type filter
    if (filterType !== 'all' && qualityReport) {
      switch (filterType) {
        case 'missing':
          const missingKeys = new Set(qualityReport.issues
            .filter(issue => issue.type === 'error' && issue.message.includes('empty'))
            .map(issue => issue.key)
          );
          filtered = filtered.filter(translation => missingKeys.has(translation.key));
          break;
        case 'warnings':
          const warningKeys = new Set(qualityReport.issues
            .filter(issue => issue.type === 'warning')
            .map(issue => issue.key)
          );
          filtered = filtered.filter(translation => warningKeys.has(translation.key));
          break;
        case 'errors':
          const errorKeys = new Set(qualityReport.issues
            .filter(issue => issue.type === 'error')
            .map(issue => issue.key)
          );
          filtered = filtered.filter(translation => errorKeys.has(translation.key));
          break;
      }
    }

    setFilteredTranslations(filtered);
  };

  const handleEditTranslation = (key: string): void => {
    const translation = translations.get(key);
    if (translation) {
      setEditingKey(key);
      setEditingValue(translation.value);
      setEditingContext(translation.context || '');
    }
  };

  const handleSaveTranslation = async (): Promise<void> => {
    if (!editingKey) return;

    try {
      await languageManager.updateTranslation(
        selectedLanguage,
        editingKey,
        editingValue,
        editingContext || undefined
      );

      // Update local state
      const updatedTranslation = translations.get(editingKey);
      if (updatedTranslation) {
        updatedTranslation.value = editingValue;
        updatedTranslation.context = editingContext || undefined;
        setTranslations(new Map(translations));
      }

      setEditingKey(null);
      setEditingValue('');
      setEditingContext('');

      toast({
        title: 'Translation updated',
        description: `Successfully updated translation for "${editingKey}"`
      });

      // Reload quality report
      await loadTranslations();
    } catch (error) {
      toast({
        title: 'Failed to update translation',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive'
      });
    }
  };

  const handleCancelEdit = (): void => {
    setEditingKey(null);
    setEditingValue('');
    setEditingContext('');
  };

  const handleAddNewKey = async (): Promise<void> => {
    if (!newKeyName.trim()) return;

    try {
      await languageManager.updateTranslation(
        selectedLanguage,
        newKeyName,
        '',
        ''
      );

      setNewKeyName('');
      setShowAddKey(false);
      await loadTranslations();

      toast({
        title: 'Translation key added',
        description: `Created new translation key "${newKeyName}"`
      });
    } catch (error) {
      toast({
        title: 'Failed to add translation key',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive'
      });
    }
  };

  const getTranslationIssues = (key: string): TranslationIssue[] => {
    if (!qualityReport) return [];
    return qualityReport.issues.filter(issue => issue.key === key);
  };

  const getIssueIcon = (type: 'error' | 'warning' | 'info') => {
    switch (type) {
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'warning': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'info': return <CheckCircle className="w-4 h-4 text-blue-500" />;
    }
  };

  const validateCurrentTranslation = (key: string, value: string): TranslationIssue[] => {
    const translation = translations.get(key);
    const context: TranslationContext = {
      key,
      languageCode: selectedLanguage,
      interpolations: translation?.interpolations,
      originalValue: languageManager.translations.get('en')?.get(key)?.value
    };

    const result = validateTranslation(value, context);
    return [
      ...result.errors.map(error => ({
        key,
        type: 'error' as const,
        rule: 'validation',
        message: error
      })),
      ...result.warnings.map(warning => ({
        key,
        type: 'warning' as const,
        rule: 'validation',
        message: warning
      }))
    ];
  };

  const exportTranslations = (): void => {
    const exportData = Object.fromEntries(
      Array.from(translations.entries()).map(([key, translation]) => [key, translation.value])
    );

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `translations-${selectedLanguage}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const importTranslations = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const content = e.target?.result as string;
        const importedTranslations = JSON.parse(content);

        for (const [key, value] of Object.entries(importedTranslations)) {
          if (typeof value === 'string') {
            await languageManager.updateTranslation(selectedLanguage, key, value);
          }
        }

        await loadTranslations();
        toast({
          title: 'Translations imported',
          description: `Successfully imported ${Object.keys(importedTranslations).length} translations`
        });
      } catch (error) {
        toast({
          title: 'Import failed',
          description: 'Invalid translation file format',
          variant: 'destructive'
        });
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Languages className="w-6 h-6" />
          <h1 className="text-2xl font-bold">Translation Manager</h1>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availableLanguages.map(lang => (
                <SelectItem key={lang.code} value={lang.code}>
                  <div className="flex items-center gap-2">
                    <span>{lang.flag}</span>
                    <span>{lang.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="translations" className="space-y-4">
        <TabsList>
          <TabsTrigger value="translations">Translations</TabsTrigger>
          <TabsTrigger value="quality">Quality Report</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
        </TabsList>

        {/* Translations Tab */}
        <TabsContent value="translations" className="space-y-4">
          {/* Controls */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search translations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="max-w-md"
                  />
                </div>
                <Select value={filterType} onValueChange={setFilterType as (value: string) => void}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="missing">Missing</SelectItem>
                    <SelectItem value="warnings">Warnings</SelectItem>
                    <SelectItem value="errors">Errors</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  onClick={() => setShowAddKey(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Key
                </Button>
                <Button
                  variant="outline"
                  onClick={exportTranslations}
                  className="flex items-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Export
                </Button>
                <div className="relative">
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Upload className="w-4 h-4" />
                    Import
                  </Button>
                  <input
                    type="file"
                    accept=".json"
                    onChange={importTranslations}
                    className="absolute inset-0 opacity-0 cursor-pointer"
                  />
                </div>
              </div>

              {/* Add New Key Modal */}
              {showAddKey && (
                <div className="border rounded-lg p-4 mb-4 bg-gray-50">
                  <h3 className="font-medium mb-2">Add New Translation Key</h3>
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="translation.key.name"
                      value={newKeyName}
                      onChange={(e) => setNewKeyName(e.target.value)}
                      className="flex-1"
                    />
                    <Button onClick={handleAddNewKey} size="sm">
                      <Plus className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setShowAddKey(false)} 
                      size="sm"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Translations List */}
          <div className="space-y-2">
            {isLoading ? (
              <Card>
                <CardContent className="flex items-center justify-center h-32">
                  <div className="text-gray-500">Loading translations...</div>
                </CardContent>
              </Card>
            ) : (
              filteredTranslations.map(translation => {
                const issues = getTranslationIssues(translation.key);
                const isEditing = editingKey === translation.key;
                
                return (
                  <Card key={translation.key}>
                    <CardContent className="pt-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                              {translation.key}
                            </code>
                            {issues.map((issue, index) => (
                              <div key={index} className="flex items-center gap-1">
                                {getIssueIcon(issue.type)}
                                <span className="text-xs text-gray-600">{issue.message}</span>
                              </div>
                            ))}
                          </div>
                          
                          {isEditing ? (
                            <div className="space-y-2">
                              <Textarea
                                value={editingValue}
                                onChange={(e) => setEditingValue(e.target.value)}
                                className="min-h-20"
                                placeholder="Translation value..."
                              />
                              <Input
                                value={editingContext}
                                onChange={(e) => setEditingContext(e.target.value)}
                                placeholder="Context (optional)..."
                              />
                              {editingValue && (
                                <div className="space-y-1">
                                  {validateCurrentTranslation(translation.key, editingValue).map((issue, index) => (
                                    <div key={index} className="flex items-center gap-1 text-sm">
                                      {getIssueIcon(issue.type)}
                                      <span className="text-gray-600">{issue.message}</span>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ) : (
                            <div>
                              <p className="text-gray-900">{translation.value || <em className="text-gray-400">No translation</em>}</p>
                              {translation.context && (
                                <p className="text-sm text-gray-600 mt-1">Context: {translation.context}</p>
                              )}
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          {isEditing ? (
                            <>
                              <Button onClick={handleSaveTranslation} size="sm">
                                <Save className="w-4 h-4" />
                              </Button>
                              <Button onClick={handleCancelEdit} variant="outline" size="sm">
                                <X className="w-4 h-4" />
                              </Button>
                            </>
                          ) : (
                            <Button 
                              onClick={() => handleEditTranslation(translation.key)} 
                              variant="outline" 
                              size="sm"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>
        </TabsContent>

        {/* Quality Report Tab */}
        <TabsContent value="quality" className="space-y-4">
          {qualityReport && (
            <>
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Translations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{qualityReport.totalTranslations}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Valid Translations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">{qualityReport.validTranslations}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Completeness</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{qualityReport.completeness.toFixed(1)}%</div>
                    <Progress value={qualityReport.completeness} className="mt-2" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Quality Score</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{qualityReport.qualityScore.toFixed(1)}</div>
                    <Progress value={qualityReport.qualityScore} className="mt-2" />
                  </CardContent>
                </Card>
              </div>

              {/* Issues */}
              <Card>
                <CardHeader>
                  <CardTitle>Issues</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {qualityReport.issues.length === 0 ? (
                      <p className="text-gray-500">No issues found!</p>
                    ) : (
                      qualityReport.issues.map((issue, index) => (
                        <div key={index} className="flex items-start gap-2 p-2 rounded border">
                          {getIssueIcon(issue.type)}
                          <div className="flex-1">
                            <code className="text-sm font-mono">{issue.key}</code>
                            <p className="text-sm text-gray-600">{issue.message}</p>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Recommendations */}
              {qualityReport.recommendations.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-1">
                      {qualityReport.recommendations.map((recommendation, index) => (
                        <li key={index} className="text-sm text-gray-700">
                          • {recommendation}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </TabsContent>

        {/* Statistics Tab */}
        <TabsContent value="statistics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Language Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {availableLanguages.map(lang => (
                  <div key={lang.code} className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg">{lang.flag}</span>
                      <h3 className="font-medium">{lang.name}</h3>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Completeness:</span>
                        <span>{lang.completeness}%</span>
                      </div>
                      <Progress value={lang.completeness} className="h-2" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TranslationManager;