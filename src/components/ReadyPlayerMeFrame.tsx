
import { useRef, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

interface RPMEvent {
  eventName: string;
  url?: string;
  error?: string;
  data?: Record<string, unknown>;
}

interface ReadyPlayerMeFrameProps {
  onAvatarExported: (url: string) => void;
  onError: (error: string) => void;
  onLoading: (loading: boolean) => void;
  className?: string;
}

export function ReadyPlayerMeFrame({ 
  onAvatarExported, 
  onError, 
  onLoading,
  className = ""
}: ReadyPlayerMeFrameProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const { toast } = useToast();

  const RPM_SUBDOMAIN = import.meta.env.VITE_READY_PLAYER_ME_SUBDOMAIN || 'demo';
  const RPM_URL = `https://${RPM_SUBDOMAIN}.readyplayer.me/avatar?frameApi&language=vi&clearCache=true&bodyType=fullbody&quickStart=true`;

  const validateAvatarUrl = (url: string): boolean => {
    if (!url) return false;
    try {
      const urlObj = new URL(url);
      return urlObj.hostname === 'models.readyplayer.me' && url.endsWith('.glb');
    } catch {
      return false;
    }
  };

  const handleRPMMessage = useCallback(async (event: MessageEvent) => {
    if (!event.origin.includes('readyplayer.me')) return;

    console.log('RPM Event received:', event.data);
    const rpmEvent: RPMEvent = event.data;

    try {
      switch (rpmEvent.eventName) {
        case 'v1.avatar.exported':
          if (rpmEvent.url && validateAvatarUrl(rpmEvent.url)) {
            onLoading(true);
            
            // Validate avatar file accessibility
            const response = await fetch(rpmEvent.url, { method: 'HEAD' });
            if (!response.ok) {
              throw new Error('Avatar file is not accessible');
            }
            
            onAvatarExported(rpmEvent.url);
            onLoading(false);
          } else {
            throw new Error('Invalid avatar URL received');
          }
          break;

        case 'v1.frame.ready':
          console.log('RPM Frame ready');
          onError('');
          break;

        case 'v1.user.set':
          console.log('RPM User set:', rpmEvent.data);
          break;

        case 'v1.avatar.draft':
          console.log('RPM Avatar draft created');
          break;

        case 'v1.error':
          throw new Error(rpmEvent.error || 'Ready Player Me error occurred');

        default:
          console.log('Unhandled RPM event:', rpmEvent.eventName);
      }
    } catch (err) {
      console.error('Error handling RPM message:', err);
      onError(err instanceof Error ? err.message : 'Unknown error occurred');
      onLoading(false);
    }
  }, [onAvatarExported, onError, onLoading]);

  useEffect(() => {
    window.addEventListener('message', handleRPMMessage);
    const iframe = iframeRef.current;

    const subscribe = () => {
      const events = [
        'v1.frame.ready',
        'v1.avatar.exported',
        'v1.user.set',
        'v1.avatar.draft',
        'v1.error',
      ];
      events.forEach((eventName) => {
        iframe?.contentWindow?.postMessage(
          {
            target: 'readyplayerme',
            type: 'subscribe',
            eventName,
          },
          '*'
        );
      });
    };

    if (iframe) {
      iframe.addEventListener('load', subscribe);
    }

    return () => {
      if (iframe) {
        iframe.removeEventListener('load', subscribe);
      }
      window.removeEventListener('message', handleRPMMessage);
    };
  }, [handleRPMMessage]);

  return (
    <iframe
      ref={iframeRef}
      src={RPM_URL}
      className={`w-full h-full border-none ${className}`}
      allow="camera *; microphone *; fullscreen"
      loading="lazy"
      title="Ready Player Me Avatar Creator"
      style={{ 
        width: '100%',
        height: '100%',
        border: 'none'
      }}
    />
  );
}
