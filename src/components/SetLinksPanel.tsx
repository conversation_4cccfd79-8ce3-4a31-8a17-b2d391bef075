
import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Check, X, Link, AlertCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { bioLinksService } from '@/lib/biolinksService';
import * as biolinksValidation from '@/lib/biolinksValidation';
import { useAuth } from '@/contexts/AuthContext';

export function SetLinksPanel() {
  const [linkInput, setLinkInput] = useState('');
  const [isChecking, setIsChecking] = useState(false);
  const [availability, setAvailability] = useState<'available' | 'taken' | null>(null);
  const [currentLink] = useState('heey.me/johndoe');
  const { t } = useTranslation();
  const { user } = useAuth();
  const [suggestions, setSuggestions] = useState<string[]>([]);

  const checkAvailability = async () => {
    if (!linkInput.trim()) return;
    
    setIsChecking(true);
    try {
      // Local validation first
      const validation = biolinksValidation.validateUsername(linkInput);
      if (!validation.isValid) {
        setAvailability(null);
        const suggestions = biolinksValidation.generateUsernameSuggestions
          ? biolinksValidation.generateUsernameSuggestions(linkInput)
          : [];
        setSuggestions(suggestions);
        setIsChecking(false);
        return;
      }

      // Check in DB by trying to load the page
      const page = await bioLinksService.getPage(linkInput, false, false);
      if (page.success) {
        setAvailability('taken');
      } else {
        setAvailability('available');
      }
    } finally {
      setIsChecking(false);
    }
  };

  const handleSave = () => {
    if (!user?.id || !linkInput.trim() || availability !== 'available') return;
    bioLinksService.createPage(user.id, linkInput, user.email || user.id)
      .then((res) => {
        // no-op for now; UI feedback handled elsewhere
      })
      .catch(() => {});
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Current Link Display */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link className="h-5 w-5" />
            {t('setLinksPanel.yourBioLink')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="glass-panel p-4 rounded-lg">
            <p className="font-mono text-lg text-primary">{currentLink}</p>
            <p className="text-sm text-muted-foreground mt-2">
              {t('setLinksPanel.currentBioLink')}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Link Configuration */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle>{t('setLinksPanel.configure')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="bio-link">{t('setLinksPanel.customPath')}</Label>
            <div className="flex items-center space-x-2">
              <div className="flex items-center glass-panel rounded-lg px-3 py-2 flex-1">
                <span className="text-muted-foreground mr-2">heey.me/</span>
                <Input
                  id="bio-link"
                  placeholder={t('setLinksPanel.customPath')}
                  value={linkInput}
                  onChange={(e) => {
                    setLinkInput(e.target.value);
                    setAvailability(null);
                  }}
                  className="border-none bg-transparent p-0 focus-visible:ring-0"
                />
              </div>
              <Button 
                onClick={checkAvailability}
                disabled={!linkInput.trim() || isChecking}
                variant="outline"
              >
                {isChecking ? t('setLinksPanel.checking') : t('setLinksPanel.check')}
              </Button>
            </div>
          </div>

          {/* Availability Status */}
          {availability && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className={`flex items-center gap-2 p-3 rounded-lg ${
                availability === 'available' 
                  ? 'bg-green-500/10 text-green-400' 
                  : 'bg-red-500/10 text-red-400'
              }`}
            >
              {availability === 'available' ? (
                <>
                  <Check className="h-4 w-4" />
                  <span>{t('setLinksPanel.available', { link: linkInput })}</span>
                </>
              ) : (
                <>
                  <X className="h-4 w-4" />
                  <span>{t('setLinksPanel.taken', { link: linkInput })}</span>
                </>
              )}
            </motion.div>
          )}

          {/* Save Button */}
          <Button 
            onClick={handleSave}
            disabled={!linkInput.trim() || availability !== 'available'}
            className="w-full bg-primary hover:bg-primary/90"
          >
            {t('setLinksPanel.saveBioLink')}
          </Button>

          {suggestions.length > 0 && (
            <div className="text-sm text-muted-foreground">
              <div className="mb-1">{t('setLinksPanel.suggestions') || 'Suggestions:'}</div>
              <div className="flex flex-wrap gap-2">
                {suggestions.map(s => (
                  <button
                    key={s}
                    type="button"
                    onClick={() => { setLinkInput(s); setAvailability(null); }}
                    className="px-2 py-1 rounded-md bg-muted hover:bg-muted/80"
                  >
                    {s}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Info */}
          <div className="flex items-start gap-2 p-3 glass-panel rounded-lg">
            <AlertCircle className="h-4 w-4 text-primary mt-0.5" />
            <div className="text-sm text-muted-foreground">
              <p className="font-medium text-foreground">{t('setLinksPanel.important')}</p>
              <p>{t('setLinksPanel.oneLink')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
