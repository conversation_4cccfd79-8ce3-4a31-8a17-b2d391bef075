
import { motion } from 'framer-motion';
import { AppIcon } from '@/components/AppIcon';

interface PageHeroProps {
  iconName: string;
  title: string;
  description: string;
  iconAlt?: string;
}

export function PageHero({ iconName, title, description, iconAlt }: PageHeroProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="text-center mb-12"
    >
      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
        <AppIcon name={iconName} alt={iconAlt || title} className="w-8 h-8 text-primary" />
      </div>
      <h1 className="heading-xl mb-4 text-gradient">
        {title}
      </h1>
      <p className="body-lg text-muted-foreground max-w-2xl mx-auto">
        {description}
      </p>
    </motion.div>
  );
}
