
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2, Chrome } from 'lucide-react';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AuthModal({ isOpen, onClose }: AuthModalProps) {
  const [loading, setLoading] = useState(false);
  const { signInWithProvider } = useAuth();

  const handleGoogleSignIn = async () => {
    setLoading(true);
    const { error } = await signInWithProvider('google');
    
    if (!error) {
      onClose();
    }
    
    setLoading(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="glass-panel border-white/20 max-w-md">
        <DialogHeader>
          <DialogTitle className="brand-gradient bg-clip-text text-transparent text-center text-2xl">
            Welcome to the Future 🚀
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="text-center space-y-2">
            <p className="text-muted-foreground">
              Sign in with your Google account and let's create magic
            </p>
            <p className="text-sm text-muted-foreground/80">
              Your AI avatar is waiting to slay ✨
            </p>
          </div>

          <Button
            onClick={handleGoogleSignIn}
            disabled={loading}
            className="w-full bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 flex items-center justify-center space-x-3 py-6 text-base font-medium shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Chrome className="w-5 h-5" />
            )}
            <span>
              {loading ? 'Getting you in...' : 'Let\'s Gooo with Google 🔥'}
            </span>
          </Button>

          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              By continuing, you agree to our{' '}
              <a href="#" className="underline hover:text-primary">Terms of Service</a>
              {' '}and{' '}
              <a href="#" className="underline hover:text-primary">Privacy Policy</a>
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
