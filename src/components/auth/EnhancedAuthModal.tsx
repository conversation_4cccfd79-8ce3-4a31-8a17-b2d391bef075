// Enhanced Authentication Modal
// Advanced authentication UI with MFA, security options, and session management

import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Shield, Smartphone, Mail, Key, AlertTriangle, Check, X, Loader, Clock } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useEnhancedAuth } from '@/hooks/useEnhancedAuth';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface EnhancedAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'signin' | 'signup' | 'mfa' | 'security';
  redirectUrl?: string;
}

interface FormData {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  agreeToTerms: boolean;
  rememberDevice: boolean;
}

interface MFAFormData {
  code: string;
  backupCode: string;
}

export const EnhancedAuthModal: React.FC<EnhancedAuthModalProps> = ({
  isOpen,
  onClose,
  defaultTab = 'signin',
  redirectUrl
}) => {
  const { toast } = useToast();
  const {
    loading,
    isAuthenticated,
    requiresMFA,
    mfaVerified,
    mfaChallenge,
    deviceInfo,
    securityLevel,
    signInWithEmail,
    signInWithProvider,
    signUp,
    initiateMFA,
    verifyMFA,
    updateSecurityLevel
  } = useEnhancedAuth();

  const [activeTab, setActiveTab] = useState(defaultTab);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [countdown, setCountdown] = useState(0);

  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    agreeToTerms: false,
    rememberDevice: false
  });

  const [mfaFormData, setMFAFormData] = useState<MFAFormData>({
    code: '',
    backupCode: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle authentication success
  useEffect(() => {
    if (isAuthenticated && mfaVerified) {
      onClose();
      if (redirectUrl) {
        window.location.href = redirectUrl;
      }
    }
  }, [isAuthenticated, mfaVerified, onClose, redirectUrl]);

  // Handle MFA challenge countdown
  useEffect(() => {
    if (mfaChallenge) {
      const interval = setInterval(() => {
        const timeLeft = Math.floor((mfaChallenge.expiresAt.getTime() - Date.now()) / 1000);
        setCountdown(Math.max(0, timeLeft));
        
        if (timeLeft <= 0) {
          clearInterval(interval);
          setActiveTab('signin');
          toast({
            title: 'MFA Challenge Expired',
            description: 'Please sign in again to receive a new verification code.',
            variant: 'destructive'
          });
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [mfaChallenge, toast]);

  // Switch to MFA tab when required
  useEffect(() => {
    if (requiresMFA && !mfaVerified) {
      setActiveTab('mfa');
    }
  }, [requiresMFA, mfaVerified]);

  const validateForm = (tab: string): boolean => {
    const newErrors: Record<string, string> = {};

    if (tab === 'signin') {
      if (!formData.email) newErrors.email = 'Email is required';
      if (!formData.password) newErrors.password = 'Password is required';
    }

    if (tab === 'signup') {
      if (!formData.email) newErrors.email = 'Email is required';
      if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {
        newErrors.email = 'Invalid email format';
      }
      if (!formData.password) newErrors.password = 'Password is required';
      if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters';
      }
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
      if (!formData.fullName) newErrors.fullName = 'Full name is required';
      if (!formData.agreeToTerms) newErrors.agreeToTerms = 'You must agree to the terms';
    }

    if (tab === 'mfa') {
      if (!mfaFormData.code && !mfaFormData.backupCode) {
        newErrors.code = 'Verification code is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignIn = async () => {
    if (!validateForm('signin')) return;

    setIsSubmitting(true);
    try {
      await signInWithEmail(formData.email, formData.password, {
        rememberDevice: formData.rememberDevice
      });

      toast({
        title: 'Sign In Successful',
        description: 'Welcome back!'
      });

    } catch (error) {
      toast({
        title: 'Sign In Failed',
        description: error instanceof Error ? error.message : 'Please check your credentials',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSignUp = async () => {
    if (!validateForm('signup')) return;

    setIsSubmitting(true);
    try {
      await signUp(formData.email, formData.password, {
        full_name: formData.fullName
      });

      toast({
        title: 'Account Created',
        description: 'Please check your email to verify your account.'
      });

    } catch (error) {
      toast({
        title: 'Sign Up Failed',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOAuthSignIn = async (provider: 'google' | 'github' | 'facebook' | 'apple') => {
    try {
      await signInWithProvider(provider);
    } catch (error) {
      toast({
        title: 'OAuth Sign In Failed',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive'
      });
    }
  };

  const handleMFAVerification = async () => {
    if (!validateForm('mfa')) return;

    setIsSubmitting(true);
    try {
      const code = mfaFormData.code || mfaFormData.backupCode;
      await verifyMFA(code);

      toast({
        title: 'MFA Verification Successful',
        description: 'You are now securely signed in.'
      });

    } catch (error) {
      toast({
        title: 'MFA Verification Failed',
        description: error instanceof Error ? error.message : 'Invalid verification code',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendMFA = async () => {
    try {
      if (mfaChallenge) {
        await initiateMFA(mfaChallenge.type);
        toast({
          title: 'Verification Code Sent',
          description: 'A new verification code has been sent.'
        });
      }
    } catch (error) {
      toast({
        title: 'Failed to Resend Code',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive'
      });
    }
  };

  const formatCountdown = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getSecurityLevelColor = (level: string): string => {
    switch (level) {
      case 'basic': return 'bg-yellow-100 text-yellow-800';
      case 'enhanced': return 'bg-blue-100 text-blue-800';
      case 'maximum': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isOpen) return null;

  return (
    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm\">
      <Card className=\"w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\">
        <CardHeader className=\"space-y-1\">
          <div className=\"flex items-center justify-between\">
            <CardTitle className=\"text-2xl font-bold\">
              {activeTab === 'signin' && 'Welcome Back'}
              {activeTab === 'signup' && 'Create Account'}
              {activeTab === 'mfa' && 'Verify Identity'}
              {activeTab === 'security' && 'Security Settings'}
            </CardTitle>
            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>
              <X className=\"w-4 h-4\" />
            </Button>
          </div>
          
          {/* Security Level Indicator */}
          {isAuthenticated && (
            <div className=\"flex items-center gap-2\">
              <Shield className=\"w-4 h-4\" />
              <Badge className={getSecurityLevelColor(securityLevel)}>
                {securityLevel.toUpperCase()} Security
              </Badge>
              {deviceInfo && (
                <Badge variant=\"outline\">
                  {deviceInfo.trusted ? 'Trusted Device' : 'New Device'}
                </Badge>
              )}
            </div>
          )}
        </CardHeader>

        <CardContent className=\"space-y-6\">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className=\"grid w-full grid-cols-2\">
              <TabsTrigger value=\"signin\" disabled={requiresMFA && !mfaVerified}>
                Sign In
              </TabsTrigger>
              <TabsTrigger value=\"signup\" disabled={requiresMFA && !mfaVerified}>
                Sign Up
              </TabsTrigger>
            </TabsList>

            {/* Sign In Tab */}
            <TabsContent value=\"signin\" className=\"space-y-4\">
              <div className=\"space-y-2\">
                <Input
                  type=\"email\"
                  placeholder=\"Email address\"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && <p className=\"text-sm text-red-500\">{errors.email}</p>}
              </div>

              <div className=\"space-y-2\">
                <div className=\"relative\">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder=\"Password\"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    className={errors.password ? 'border-red-500' : ''}
                  />
                  <Button
                    type=\"button\"
                    variant=\"ghost\"
                    size=\"sm\"
                    className=\"absolute right-0 top-0 h-full px-3\"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}
                  </Button>
                </div>
                {errors.password && <p className=\"text-sm text-red-500\">{errors.password}</p>}
              </div>

              <div className=\"flex items-center space-x-2\">
                <input
                  type=\"checkbox\"
                  id=\"remember-device\"
                  checked={formData.rememberDevice}
                  onChange={(e) => setFormData(prev => ({ ...prev, rememberDevice: e.target.checked }))}
                  className=\"rounded border-gray-300\"
                />
                <label htmlFor=\"remember-device\" className=\"text-sm text-gray-600\">
                  Trust this device for 30 days
                </label>
              </div>

              <Button
                onClick={handleSignIn}
                disabled={isSubmitting || loading}
                className=\"w-full\"
              >
                {isSubmitting || loading ? (
                  <Loader className=\"w-4 h-4 mr-2 animate-spin\" />
                ) : null}
                Sign In
              </Button>

              <div className=\"relative\">
                <div className=\"absolute inset-0 flex items-center\">
                  <Separator />
                </div>
                <div className=\"relative flex justify-center text-xs uppercase\">
                  <span className=\"bg-white px-2 text-gray-500\">Or continue with</span>
                </div>
              </div>

              <div className=\"grid grid-cols-2 gap-3\">
                <Button
                  variant=\"outline\"
                  onClick={() => handleOAuthSignIn('google')}
                  disabled={loading}
                >
                  Google
                </Button>
                <Button
                  variant=\"outline\"
                  onClick={() => handleOAuthSignIn('github')}
                  disabled={loading}
                >
                  GitHub
                </Button>
              </div>
            </TabsContent>

            {/* Sign Up Tab */}
            <TabsContent value=\"signup\" className=\"space-y-4\">
              <div className=\"space-y-2\">
                <Input
                  type=\"text\"
                  placeholder=\"Full name\"
                  value={formData.fullName}
                  onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                  className={errors.fullName ? 'border-red-500' : ''}
                />
                {errors.fullName && <p className=\"text-sm text-red-500\">{errors.fullName}</p>}
              </div>

              <div className=\"space-y-2\">
                <Input
                  type=\"email\"
                  placeholder=\"Email address\"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && <p className=\"text-sm text-red-500\">{errors.email}</p>}
              </div>

              <div className=\"space-y-2\">
                <div className=\"relative\">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder=\"Password\"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    className={errors.password ? 'border-red-500' : ''}
                  />
                  <Button
                    type=\"button\"
                    variant=\"ghost\"
                    size=\"sm\"
                    className=\"absolute right-0 top-0 h-full px-3\"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}
                  </Button>
                </div>
                {errors.password && <p className=\"text-sm text-red-500\">{errors.password}</p>}
              </div>

              <div className=\"space-y-2\">
                <div className=\"relative\">
                  <Input
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder=\"Confirm password\"
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    className={errors.confirmPassword ? 'border-red-500' : ''}
                  />
                  <Button
                    type=\"button\"
                    variant=\"ghost\"
                    size=\"sm\"
                    className=\"absolute right-0 top-0 h-full px-3\"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}
                  </Button>
                </div>
                {errors.confirmPassword && <p className=\"text-sm text-red-500\">{errors.confirmPassword}</p>}
              </div>

              <div className=\"space-y-2\">
                <div className=\"flex items-center space-x-2\">
                  <input
                    type=\"checkbox\"
                    id=\"agree-terms\"
                    checked={formData.agreeToTerms}
                    onChange={(e) => setFormData(prev => ({ ...prev, agreeToTerms: e.target.checked }))}
                    className=\"rounded border-gray-300\"
                  />
                  <label htmlFor=\"agree-terms\" className=\"text-sm text-gray-600\">
                    I agree to the{' '}
                    <a href=\"/terms\" className=\"text-blue-600 hover:underline\">
                      Terms of Service
                    </a>{' '}
                    and{' '}
                    <a href=\"/privacy\" className=\"text-blue-600 hover:underline\">
                      Privacy Policy
                    </a>
                  </label>
                </div>
                {errors.agreeToTerms && <p className=\"text-sm text-red-500\">{errors.agreeToTerms}</p>}
              </div>

              <Button
                onClick={handleSignUp}
                disabled={isSubmitting || loading}
                className=\"w-full\"
              >
                {isSubmitting || loading ? (
                  <Loader className=\"w-4 h-4 mr-2 animate-spin\" />
                ) : null}
                Create Account
              </Button>

              <div className=\"relative\">
                <div className=\"absolute inset-0 flex items-center\">
                  <Separator />
                </div>
                <div className=\"relative flex justify-center text-xs uppercase\">
                  <span className=\"bg-white px-2 text-gray-500\">Or continue with</span>
                </div>
              </div>

              <div className=\"grid grid-cols-2 gap-3\">
                <Button
                  variant=\"outline\"
                  onClick={() => handleOAuthSignIn('google')}
                  disabled={loading}
                >
                  Google
                </Button>
                <Button
                  variant=\"outline\"
                  onClick={() => handleOAuthSignIn('github')}
                  disabled={loading}
                >
                  GitHub
                </Button>
              </div>
            </TabsContent>
          </Tabs>

          {/* MFA Challenge */}
          {activeTab === 'mfa' && mfaChallenge && (
            <div className=\"space-y-4\">
              <Alert>
                <Shield className=\"w-4 h-4\" />
                <AlertDescription>
                  Enter the verification code sent to your {mfaChallenge.type === 'sms' ? 'phone' : 'email'}.
                </AlertDescription>
              </Alert>

              {countdown > 0 && (
                <div className=\"flex items-center justify-center gap-2 text-sm text-gray-600\">
                  <Clock className=\"w-4 h-4\" />
                  <span>Code expires in {formatCountdown(countdown)}</span>
                </div>
              )}

              <div className=\"space-y-2\">
                <Input
                  type=\"text\"
                  placeholder=\"Enter 6-digit code\"
                  value={mfaFormData.code}
                  onChange={(e) => setMFAFormData(prev => ({ ...prev, code: e.target.value }))}
                  className={cn(\"text-center tracking-wider\", errors.code ? 'border-red-500' : '')}
                  maxLength={6}
                />
                {errors.code && <p className=\"text-sm text-red-500\">{errors.code}</p>}
              </div>

              <Button
                onClick={handleMFAVerification}
                disabled={isSubmitting || loading}
                className=\"w-full\"
              >
                {isSubmitting || loading ? (
                  <Loader className=\"w-4 h-4 mr-2 animate-spin\" />
                ) : null}
                Verify Code
              </Button>

              <div className=\"text-center\">
                <Button
                  variant=\"ghost\"
                  size=\"sm\"
                  onClick={handleResendMFA}
                  disabled={countdown > 240} // Allow resend after 1 minute
                >
                  Resend Code
                </Button>
              </div>

              <Separator />

              <div className=\"space-y-2\">
                <Input
                  type=\"text\"
                  placeholder=\"Or use backup code\"
                  value={mfaFormData.backupCode}
                  onChange={(e) => setMFAFormData(prev => ({ ...prev, backupCode: e.target.value }))}
                  className=\"text-center\"
                />
                <p className=\"text-xs text-gray-500 text-center\">
                  Use one of your backup codes if you can't access your device
                </p>
              </div>
            </div>
          )}

          {/* Security Level Upgrade */}
          {isAuthenticated && mfaVerified && (
            <div className=\"space-y-4\">
              <Alert>
                <Shield className=\"w-4 h-4\" />
                <AlertDescription>
                  Enhance your account security by enabling additional protection features.
                </AlertDescription>
              </Alert>

              <div className=\"grid gap-2\">
                <Button
                  variant=\"outline\"
                  onClick={() => updateSecurityLevel('enhanced')}
                  className=\"flex items-center justify-between\"
                >
                  <span>Enhanced Security</span>
                  <Badge className=\"bg-blue-100 text-blue-800\">Recommended</Badge>
                </Button>
                <Button
                  variant=\"outline\"
                  onClick={() => updateSecurityLevel('maximum')}
                  className=\"flex items-center justify-between\"
                >
                  <span>Maximum Security</span>
                  <Badge className=\"bg-green-100 text-green-800\">Advanced</Badge>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedAuthModal;