// Security Dashboard Component
// Comprehensive security management interface for users

import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Smartphone, 
  Key, 
  Eye, 
  Clock, 
  MapPin, 
  Monitor, 
  AlertTriangle, 
  Check, 
  X, 
  MoreVertical,
  Download,
  QrCode,
  Trash2,
  RefreshCw
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { useEnhancedAuth } from '@/hooks/useEnhancedAuth';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface SecurityDashboardProps {
  className?: string;
}

interface SecurityScore {
  overall: number;
  factors: {
    mfa: number;
    passwordStrength: number;
    deviceSecurity: number;
    sessionSecurity: number;
    activityMonitoring: number;
  };
}

export const SecurityDashboard: React.FC<SecurityDashboardProps> = ({
  className = ''
}) => {
  const { toast } = useToast();
  const {
    user,
    securityLevel,
    deviceInfo,
    activeSessions,
    securityEvents,
    requiresMFA,
    mfaVerified,
    getActiveSessions,
    revokeSession,
    updateSecurityLevel,
    changePassword,
    initiateMFA
  } = useEnhancedAuth();

  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [mfaEnabled, setMfaEnabled] = useState(requiresMFA);
  const [securityScore, setSecurityScore] = useState<SecurityScore>({
    overall: 65,
    factors: {
      mfa: requiresMFA ? 100 : 0,
      passwordStrength: 75,
      deviceSecurity: deviceInfo?.trusted ? 100 : 50,
      sessionSecurity: 80,
      activityMonitoring: 90
    }
  });

  // Load security data on mount
  useEffect(() => {
    const loadSecurityData = async () => {
      try {
        await getActiveSessions();
        calculateSecurityScore();
      } catch (error) {
        console.error('Failed to load security data:', error);
      }
    };

    if (user) {
      loadSecurityData();
    }
  }, [user, getActiveSessions]);

  // Calculate overall security score
  const calculateSecurityScore = () => {
    const factors = securityScore.factors;
    const weights = {
      mfa: 0.3,
      passwordStrength: 0.2,
      deviceSecurity: 0.2,
      sessionSecurity: 0.15,
      activityMonitoring: 0.15
    };

    const overall = Math.round(
      factors.mfa * weights.mfa +
      factors.passwordStrength * weights.passwordStrength +
      factors.deviceSecurity * weights.deviceSecurity +
      factors.sessionSecurity * weights.sessionSecurity +
      factors.activityMonitoring * weights.activityMonitoring
    );

    setSecurityScore(prev => ({ ...prev, overall }));
  };

  const getSecurityLevelColor = (level: string): string => {
    switch (level) {
      case 'basic': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'enhanced': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'maximum': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number): string => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'mobile': return <Smartphone className=\"w-4 h-4\" />;
      case 'tablet': return <Smartphone className=\"w-4 h-4\" />;
      default: return <Monitor className=\"w-4 h-4\" />;
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'login_success': return <Check className=\"w-4 h-4 text-green-500\" />;
      case 'login_failed': return <X className=\"w-4 h-4 text-red-500\" />;
      case 'mfa_success': return <Shield className=\"w-4 h-4 text-green-500\" />;
      case 'suspicious_activity': return <AlertTriangle className=\"w-4 h-4 text-red-500\" />;
      default: return <Eye className=\"w-4 h-4 text-gray-500\" />;
    }
  };

  const handleMFAToggle = async (enabled: boolean) => {
    try {
      setIsLoading(true);
      
      if (enabled) {
        await initiateMFA('totp');
        setShowQRCode(true);
      } else {
        // Disable MFA logic would go here
        setMfaEnabled(false);
      }

      toast({
        title: enabled ? 'MFA Enabled' : 'MFA Disabled',
        description: enabled ? 'Multi-factor authentication has been enabled.' : 'Multi-factor authentication has been disabled.'
      });

    } catch (error) {
      toast({
        title: 'MFA Toggle Failed',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = async () => {
    if (newPassword !== confirmPassword) {
      toast({
        title: 'Password Mismatch',
        description: 'New passwords do not match',
        variant: 'destructive'
      });
      return;
    }

    if (newPassword.length < 8) {
      toast({
        title: 'Password Too Short',
        description: 'Password must be at least 8 characters',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsLoading(true);
      await changePassword(newPassword);
      setNewPassword('');
      setConfirmPassword('');
    } catch (error) {
      toast({
        title: 'Password Change Failed',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSessionRevoke = async (sessionId: string) => {
    try {
      await revokeSession(sessionId);
    } catch (error) {
      toast({
        title: 'Session Revoke Failed',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive'
      });
    }
  };

  const handleSecurityLevelChange = async (level: 'basic' | 'enhanced' | 'maximum') => {
    try {
      setIsLoading(true);
      await updateSecurityLevel(level);
    } catch (error) {
      toast({
        title: 'Security Level Update Failed',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <Card className={className}>
        <CardContent className=\"flex items-center justify-center h-64\">
          <p className=\"text-gray-500\">Please sign in to view security settings</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn(\"space-y-6\", className)}>
      {/* Security Score Overview */}
      <Card>
        <CardHeader>
          <CardTitle className=\"flex items-center gap-2\">
            <Shield className=\"w-5 h-5\" />
            Security Score
          </CardTitle>
        </CardHeader>
        <CardContent className=\"space-y-4\">
          <div className=\"flex items-center justify-between\">
            <div>
              <div className={cn(\"text-3xl font-bold\", getScoreColor(securityScore.overall))}>
                {securityScore.overall}%
              </div>
              <p className=\"text-sm text-gray-600\">Overall Security</p>
            </div>
            <div className=\"flex-1 max-w-xs mx-4\">
              <Progress 
                value={securityScore.overall} 
                className=\"h-2\"
              />
            </div>
            <Badge className={getSecurityLevelColor(securityLevel)}>
              {securityLevel.toUpperCase()}
            </Badge>
          </div>

          <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">
            {Object.entries(securityScore.factors).map(([key, value]) => (
              <div key={key} className=\"text-center\">
                <div className={cn(\"text-sm font-medium\", getScoreColor(value))}>
                  {value}%
                </div>
                <div className=\"text-xs text-gray-500 capitalize\">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className=\"grid w-full grid-cols-4\">
          <TabsTrigger value=\"overview\">Overview</TabsTrigger>
          <TabsTrigger value=\"sessions\">Sessions</TabsTrigger>
          <TabsTrigger value=\"activity\">Activity</TabsTrigger>
          <TabsTrigger value=\"settings\">Settings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value=\"overview\" className=\"space-y-4\">
          <div className=\"grid gap-4 md:grid-cols-2\">
            {/* Multi-Factor Authentication */}
            <Card>
              <CardHeader>
                <CardTitle className=\"flex items-center gap-2\">
                  <Key className=\"w-4 h-4\" />
                  Multi-Factor Authentication
                </CardTitle>
              </CardHeader>
              <CardContent className=\"space-y-4\">
                <div className=\"flex items-center justify-between\">
                  <div>
                    <p className=\"font-medium\">
                      {mfaEnabled ? 'Enabled' : 'Disabled'}
                    </p>
                    <p className=\"text-sm text-gray-600\">
                      {mfaEnabled 
                        ? 'Your account is protected with 2FA' 
                        : 'Add an extra layer of security'
                      }
                    </p>
                  </div>
                  <Switch
                    checked={mfaEnabled}
                    onCheckedChange={handleMFAToggle}
                    disabled={isLoading}
                  />
                </div>
                
                {mfaEnabled && (
                  <div className=\"space-y-2\">
                    <Button
                      variant=\"outline\"
                      size=\"sm\"
                      onClick={() => setShowQRCode(!showQRCode)}
                    >
                      <QrCode className=\"w-4 h-4 mr-2\" />
                      Show QR Code
                    </Button>
                    <Button
                      variant=\"outline\"
                      size=\"sm\"
                    >
                      <Download className=\"w-4 h-4 mr-2\" />
                      Backup Codes
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Password Security */}
            <Card>
              <CardHeader>
                <CardTitle className=\"flex items-center gap-2\">
                  <Key className=\"w-4 h-4\" />
                  Password Security
                </CardTitle>
              </CardHeader>
              <CardContent className=\"space-y-4\">
                <div>
                  <p className=\"font-medium\">Strong Password</p>
                  <p className=\"text-sm text-gray-600\">
                    Last changed 30 days ago
                  </p>
                </div>
                
                <div className=\"space-y-2\">
                  <input
                    type=\"password\"
                    placeholder=\"New password\"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className=\"w-full px-3 py-2 border rounded-md\"
                  />
                  <input
                    type=\"password\"
                    placeholder=\"Confirm password\"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className=\"w-full px-3 py-2 border rounded-md\"
                  />
                  <Button
                    size=\"sm\"
                    onClick={handlePasswordChange}
                    disabled={isLoading || !newPassword || !confirmPassword}
                  >
                    Update Password
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Device Information */}
            <Card>
              <CardHeader>
                <CardTitle className=\"flex items-center gap-2\">
                  {deviceInfo && getDeviceIcon(deviceInfo.type)}
                  Current Device
                </CardTitle>
              </CardHeader>
              <CardContent>
                {deviceInfo ? (
                  <div className=\"space-y-2\">
                    <div className=\"flex items-center justify-between\">
                      <span className=\"font-medium\">{deviceInfo.name}</span>
                      <Badge variant={deviceInfo.trusted ? 'default' : 'secondary'}>
                        {deviceInfo.trusted ? 'Trusted' : 'New'}
                      </Badge>
                    </div>
                    <p className=\"text-sm text-gray-600\">{deviceInfo.os}</p>
                    <p className=\"text-sm text-gray-600\">{deviceInfo.browser}</p>
                    <p className=\"text-sm text-gray-600\">
                      Last used: {formatDate(deviceInfo.lastUsed)}
                    </p>
                  </div>
                ) : (
                  <p className=\"text-gray-500\">Device information not available</p>
                )}
              </CardContent>
            </Card>

            {/* Security Recommendations */}
            <Card>
              <CardHeader>
                <CardTitle className=\"flex items-center gap-2\">
                  <AlertTriangle className=\"w-4 h-4\" />
                  Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent className=\"space-y-3\">
                {!mfaEnabled && (
                  <Alert>
                    <AlertTriangle className=\"w-4 h-4\" />
                    <AlertDescription>
                      Enable two-factor authentication for better security.
                    </AlertDescription>
                  </Alert>
                )}
                
                {securityLevel === 'basic' && (
                  <Alert>
                    <Shield className=\"w-4 h-4\" />
                    <AlertDescription>
                      Upgrade to Enhanced Security for additional protection.
                    </AlertDescription>
                  </Alert>
                )}

                {securityScore.overall < 70 && (
                  <Alert>
                    <AlertTriangle className=\"w-4 h-4\" />
                    <AlertDescription>
                      Your security score is below 70%. Consider enabling more security features.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Active Sessions Tab */}
        <TabsContent value=\"sessions\" className=\"space-y-4\">
          <Card>
            <CardHeader>
              <CardTitle className=\"flex items-center justify-between\">
                <span className=\"flex items-center gap-2\">
                  <Monitor className=\"w-4 h-4\" />
                  Active Sessions ({activeSessions.length})
                </span>
                <Button
                  variant=\"outline\"
                  size=\"sm\"
                  onClick={getActiveSessions}
                >
                  <RefreshCw className=\"w-4 h-4 mr-2\" />
                  Refresh
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className=\"space-y-4\">
                {activeSessions.map((session) => (
                  <div
                    key={session.id}
                    className=\"flex items-center justify-between p-4 border rounded-lg\"
                  >
                    <div className=\"flex items-center gap-3\">
                      {getDeviceIcon('desktop')}
                      <div>
                        <p className=\"font-medium\">{session.userAgent}</p>
                        <p className=\"text-sm text-gray-600\">{session.ipAddress}</p>
                        <p className=\"text-sm text-gray-600\">
                          Last active: {formatDate(session.lastActivity)}
                        </p>
                      </div>
                    </div>
                    
                    <div className=\"flex items-center gap-2\">
                      {session.id === user.id && (
                        <Badge>Current</Badge>
                      )}
                      <Button
                        variant=\"outline\"
                        size=\"sm\"
                        onClick={() => handleSessionRevoke(session.id)}
                        disabled={session.id === user.id}
                      >
                        <Trash2 className=\"w-4 h-4\" />
                      </Button>
                    </div>
                  </div>
                ))}
                
                {activeSessions.length === 0 && (
                  <p className=\"text-center text-gray-500 py-8\">
                    No active sessions found
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Activity Tab */}
        <TabsContent value=\"activity\" className=\"space-y-4\">
          <Card>
            <CardHeader>
              <CardTitle className=\"flex items-center gap-2\">
                <Eye className=\"w-4 h-4\" />
                Recent Security Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className=\"space-y-4\">
                {securityEvents.slice(0, 10).map((event, index) => (
                  <div key={index} className=\"flex items-center gap-3 p-3 border-l-2 border-gray-200\">
                    {getEventIcon(event.type)}
                    <div className=\"flex-1\">
                      <p className=\"font-medium capitalize\">
                        {event.type.replace(/_/g, ' ')}
                      </p>
                      <p className=\"text-sm text-gray-600\">
                        {formatDate(event.timestamp)}
                      </p>
                    </div>
                    <Badge 
                      variant={
                        event.riskLevel === 'high' ? 'destructive' :
                        event.riskLevel === 'medium' ? 'default' : 'secondary'
                      }
                    >
                      {event.riskLevel}
                    </Badge>
                  </div>
                ))}
                
                {securityEvents.length === 0 && (
                  <p className=\"text-center text-gray-500 py-8\">
                    No security events recorded
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings Tab */}
        <TabsContent value=\"settings\" className=\"space-y-4\">
          <Card>
            <CardHeader>
              <CardTitle>Security Level</CardTitle>
            </CardHeader>
            <CardContent className=\"space-y-4\">
              <div className=\"grid gap-4\">
                <div 
                  className={cn(
                    \"p-4 border rounded-lg cursor-pointer transition-colors\",
                    securityLevel === 'basic' ? 'border-blue-500 bg-blue-50' : 'hover:bg-gray-50'
                  )}
                  onClick={() => handleSecurityLevelChange('basic')}
                >
                  <div className=\"flex items-center justify-between\">
                    <div>
                      <h3 className=\"font-medium\">Basic Security</h3>
                      <p className=\"text-sm text-gray-600\">
                        Standard protection with essential security features
                      </p>
                    </div>
                    {securityLevel === 'basic' && <Check className=\"w-5 h-5 text-blue-500\" />}
                  </div>
                </div>

                <div 
                  className={cn(
                    \"p-4 border rounded-lg cursor-pointer transition-colors\",
                    securityLevel === 'enhanced' ? 'border-blue-500 bg-blue-50' : 'hover:bg-gray-50'
                  )}
                  onClick={() => handleSecurityLevelChange('enhanced')}
                >
                  <div className=\"flex items-center justify-between\">
                    <div>
                      <h3 className=\"font-medium\">Enhanced Security</h3>
                      <p className=\"text-sm text-gray-600\">
                        Advanced protection with MFA and monitoring
                      </p>
                    </div>
                    {securityLevel === 'enhanced' && <Check className=\"w-5 h-5 text-blue-500\" />}
                  </div>
                </div>

                <div 
                  className={cn(
                    \"p-4 border rounded-lg cursor-pointer transition-colors\",
                    securityLevel === 'maximum' ? 'border-blue-500 bg-blue-50' : 'hover:bg-gray-50'
                  )}
                  onClick={() => handleSecurityLevelChange('maximum')}
                >
                  <div className=\"flex items-center justify-between\">
                    <div>
                      <h3 className=\"font-medium\">Maximum Security</h3>
                      <p className=\"text-sm text-gray-600\">
                        Highest level protection with all security features
                      </p>
                    </div>
                    {securityLevel === 'maximum' && <Check className=\"w-5 h-5 text-blue-500\" />}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SecurityDashboard;