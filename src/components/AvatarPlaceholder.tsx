
import { motion } from 'framer-motion';
import { User } from 'lucide-react';

export function AvatarPlaceholder() {
  return (
    <motion.div
      className="relative w-64 h-64 mx-auto"
      animate={{
        rotateY: [0, 360],
      }}
      transition={{
        duration: 20,
        repeat: Infinity,
        ease: "linear"
      }}
    >
      <div className="glass-panel w-full h-full rounded-full flex items-center justify-center relative overflow-hidden">
        {/* Gradient overlay */}
        <div className="absolute inset-0 brand-gradient opacity-20 rounded-full" />
        
        {/* Avatar icon */}
        <User className="w-24 h-24 text-primary relative z-10" />
        
        {/* Animated rings */}
        <motion.div
          className="absolute inset-0 border-2 border-primary/30 rounded-full"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute inset-0 border-2 border-primary/20 rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.5
          }}
        />
      </div>
    </motion.div>
  );
}
