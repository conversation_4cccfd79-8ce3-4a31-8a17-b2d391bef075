
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TaskCenter } from '@/components/TaskCenter';
import { ReferralCenter } from '@/components/ReferralCenter';
import { AppIcon } from '@/components/AppIcon';
import { RewardStatCard } from '@/components/RewardStatCard';
import { SubscriptionPlans } from '@/components/subscription/SubscriptionPlans';
import { PageHero } from '@/components/PageHero';
import { useToast } from '@/hooks/use-toast';
import { Coins, Gift, Crown, TrendingUp } from 'lucide-react';
import { useState } from 'react';

interface RewardsMainViewProps {
  selectedSection: string | null;
}

const userStats = {
  totalCredits: 485,
  earnedThisMonth: 120,
  pendingCredits: 75,
  lifetimeEarned: 1250
};

const recentActivity = [
  { type: 'earned', amount: 25, description: 'Friend signup bonus', time: '2 hours ago' },
  { type: 'earned', amount: 10, description: 'Daily login streak', time: '1 day ago' },
  { type: 'earned', amount: 50, description: 'Pro subscription bonus', time: '3 days ago' },
  { type: 'redeemed', amount: -30, description: 'Premium avatar unlock', time: '5 days ago' },
];

// Section configurations for hero sections
const sectionConfigs = {
  overview: {
    iconName: 'Diamond',
    title: 'Overview',
    description: 'View your $HEEY balance, recent activity, and subscription plans'
  },
  tasks: {
    iconName: 'Pig',
    title: 'Tasks',
    description: 'Complete tasks to earn $HEEY tokens and unlock rewards'
  },
  referrals: {
    iconName: 'Send',
    title: 'Referrals',
    description: 'Invite friends and earn bonus rewards for each successful referral'
  },
  activity: {
    iconName: 'Chart',
    title: 'Activity',
    description: 'View your transaction history and track your $HEEY earnings'
  }
};

export function RewardsMainView({ selectedSection }: RewardsMainViewProps) {
  const [currentPlan, setCurrentPlan] = useState<string | null>('social-media-builder'); // Mock current plan
  const { toast } = useToast();

  const handlePlanSelection = (planId: string, paymentMethod: string) => {
    // Mock subscription logic
    setCurrentPlan(planId);
    
    toast({
      title: "Subscription Updated!",
      description: `Successfully subscribed to ${planId.replace('-', ' ')} via ${paymentMethod}`,
    });
  };

  if (!selectedSection) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-md"
        >
          <AppIcon name="Diamond" alt="Select" className="h-16 w-16 text-muted-foreground/50 mx-auto mb-6" />
          <h2 className="heading-lg mb-3 text-muted-foreground">
            Select a Section
          </h2>
          <p className="body-md text-muted-foreground">
            Choose a section from the sidebar to get started
          </p>
        </motion.div>
      </div>
    );
  }

  const sectionConfig = sectionConfigs[selectedSection as keyof typeof sectionConfigs];

  const renderSection = () => {
    switch (selectedSection) {
      case 'overview':
        return (
          <div className="space-y-8">
            {/* Credit Balance Overview */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-6"
            >
              <RewardStatCard
                icon={<img src="/3D-coin/heycoin-gold.webp" alt="HEEY Coin" className="w-10 h-10 object-contain" />}
                value={userStats.totalCredits}
                label="Available $HEEY"
              />

              <RewardStatCard
                icon={<TrendingUp className="w-8 h-8 text-primary" />}
                value={userStats.earnedThisMonth}
                label="This Month"
              />

              <RewardStatCard
                icon={<Gift className="w-8 h-8 text-primary" />}
                value={userStats.pendingCredits}
                label="Pending"
              />

              <RewardStatCard
                icon={<Crown className="w-8 h-8 text-primary" />}
                value={userStats.lifetimeEarned}
                label="Lifetime Total"
              />
            </motion.div>

            {/* Subscription Plans */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="glass-card shadow-xl rounded-3xl overflow-hidden">
                <CardHeader className="pb-6 px-8 pt-8">
                  <CardTitle className="flex items-center space-x-4 text-2xl font-bold">
                    <Crown className="w-6 h-6 text-primary" />
                    <span>Subscription Plans</span>
                  </CardTitle>
                  <CardDescription className="text-lg text-muted-foreground">
                    Choose the perfect plan for your needs and unlock premium features
                  </CardDescription>
                </CardHeader>
                <CardContent className="px-8 pb-8">
                  <SubscriptionPlans
                    currentPlan={currentPlan}
                    onPlanSelect={handlePlanSelection}
                  />
                </CardContent>
              </Card>
            </motion.div>
          </div>
        );
      case 'tasks':
        return <TaskCenter />;
      case 'referrals':
        return <ReferralCenter />;
      case 'activity':
        return (
          <div className="space-y-6">
            <div className="space-y-6">
              {recentActivity.map((activity, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="p-6 flex items-center justify-between rounded-2xl bg-background/20 backdrop-blur-sm"
                >
                    <div className="flex items-center space-x-6">
                      <div className={`w-16 h-16 rounded-xl flex items-center justify-center ${
                        activity.type === 'earned' ? 'bg-green-500/10' : 'bg-red-500/10'
                      }`}>
                        {activity.type === 'earned' ? (
                          <img src="/3D-coin/heycoin-gold.webp" alt="HEEY Coin" className="w-10 h-10 object-contain" />
                        ) : (
                          <AppIcon name="Gift" alt="Gift" className="w-8 h-8 text-red-400" />
                        )}
                      </div>
                      <div>
                        <p className="font-semibold text-xl text-foreground">{activity.description}</p>
                        <p className="text-lg text-muted-foreground">{activity.time}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {activity.type === 'earned' && (
                        <img src="/3D-coin/heycoin-gold.webp" alt="HEEY Coin" className="w-8 h-8 object-contain" />
                      )}
                      <div
                        className={`px-4 py-2 text-lg font-semibold rounded-xl ${
                          activity.type === 'earned' 
                            ? 'text-green-400 bg-green-500/10' 
                            : 'text-red-400 bg-red-500/10'
                        }`}
                      >
                        {activity.type === 'earned' ? '+' : ''}{Math.abs(activity.amount)} $HEEY
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex-1 p-4 md:p-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-7xl mx-auto"
      >
        {/* Dynamic Page Hero */}
        {sectionConfig && (
          <PageHero
            iconName={sectionConfig.iconName}
            title={sectionConfig.title}
            description={sectionConfig.description}
            iconAlt={sectionConfig.title}
          />
        )}
        
        {/* Section Content */}
        {renderSection()}
      </motion.div>
    </div>
  );
}
