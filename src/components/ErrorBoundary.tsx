
import React from 'react';
import i18n from '@/i18n/config';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="min-h-screen mobile-full-screen bg-background flex items-center justify-center">
          <div className="text-center p-8">
            <h2 className="text-2xl font-bold text-foreground mb-4">{i18n.t('errorBoundary.title')}</h2>
            <p className="text-muted-foreground mb-6">{i18n.t('errorBoundary.description')}</p>
            <button
              onClick={() => this.setState({ hasError: false })}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:opacity-90 transition-opacity"
            >
              {i18n.t('errorBoundary.tryAgain')}
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
