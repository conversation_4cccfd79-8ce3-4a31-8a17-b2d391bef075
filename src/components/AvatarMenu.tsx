
import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { User, Settings, LogOut, Crown, Palette, Brain, Link, Globe } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface User {
  email: string;
  username: string;
  avatarUrl?: string;
}

interface AvatarMenuProps {
  user: User | null;
  onLogout: () => void;
}

export function AvatarMenu({ user, onLogout }: AvatarMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const menuRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const menuItems = [
    {
      icon: User,
      label: t('navigation.avatar'),
      action: () => {
        navigate('/topics');
        setIsOpen(false);
      }
    },
    {
      icon: Palette,
      label: t('dashboard.actions.createAvatar'),
      action: () => {
        navigate('/avatar');
        setIsOpen(false);
      }
    },
    {
      icon: Brain,
      label: t('dashboard.actions.trainAI'),
      action: () => {
        navigate('/knowledge');
        setIsOpen(false);
      }
    },
    {
      icon: Link,
      label: t('navigation.bioLink'),
      action: () => {
        navigate('/biolink');
        setIsOpen(false);
      }
    },
    {
      icon: Globe,
      label: t('dashboard.actions.viewNetwork'),
      action: () => {
        navigate('/network');
        setIsOpen(false);
      }
    },
    {
      icon: Crown,
      label: t('navigation.upgrade'),
      action: () => {
        navigate('/upgrade');
        setIsOpen(false);
      }
    },
    {
      icon: Settings,
      label: t('common.settings'),
      action: () => {
        navigate('/payment-settings');
        setIsOpen(false);
      }
    }
  ];

  if (!user) return null;

  return (
    <div className="relative" ref={menuRef}>
      <Button
        variant="ghost"
        className="h-12 w-12 rounded-full p-0 glass-button"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Avatar className="h-10 w-10">
          <AvatarImage src={user.avatarUrl} alt={user.username} />
          <AvatarFallback className="bg-primary/20 text-primary font-medium">
            {user.username?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase()}
          </AvatarFallback>
        </Avatar>
      </Button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 top-14 z-50 w-72 glass-modal p-4 shadow-2xl"
          >
            {/* User Info */}
            <div className="p-4 mb-3">
              <div className="flex items-center space-x-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={user.avatarUrl} alt={user.username} />
                  <AvatarFallback className="bg-primary/20 text-primary font-medium">
                    {user.username?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="heading-sm truncate">
                    {user.username || 'User'}
                  </p>
                  <p className="body-sm text-muted-foreground truncate">
                    {user.email}
                  </p>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="space-y-2">
              {menuItems.map((item, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  className="w-full justify-start h-12 rounded-2xl glass-button touch-target"
                  onClick={item.action}
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  <span className="body-md">{item.label}</span>
                </Button>
              ))}
              
              <div className="h-px bg-background/20 my-3" />
              
              <Button
                variant="ghost"
                className="w-full justify-start h-12 rounded-2xl glass-button text-destructive hover:text-destructive touch-target"
                onClick={() => {
                  onLogout();
                  setIsOpen(false);
                }}
              >
                <LogOut className="mr-3 h-5 w-5" />
                <span className="body-md">{t('common.logout')}</span>
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
