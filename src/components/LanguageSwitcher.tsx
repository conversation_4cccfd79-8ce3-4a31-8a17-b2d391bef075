
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { languages } from '@/i18n/config';
import { ChevronDown } from 'lucide-react';

export interface LanguageSwitcherProps {
  className?: string;
}

export function LanguageSwitcher({ className }: LanguageSwitcherProps = {}) {
  const { i18n, t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const currentLanguage = languages[i18n.language as keyof typeof languages] || languages.en;

  const handleLanguageChange = (langCode: string) => {
    i18n.changeLanguage(langCode);
    localStorage.setItem('i18nextLng', langCode);
    // Update document direction for RTL languages
    document.documentElement.dir = languages[langCode as keyof typeof languages].dir;
    document.documentElement.lang = langCode;
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'h-8 w-8 sm:w-auto px-0 sm:px-3 flex items-center sm:space-x-2 rounded-full sm:rounded-3xl bg-gray-900 sm:bg-transparent sm:glass-button',
            className
          )}
        >
          <span className="text-lg">{currentLanguage.flag}</span>
          <span className="hidden sm:inline-block text-sm">
            {currentLanguage.name}
          </span>
          <ChevronDown className="hidden sm:block h-3 w-3 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48 glass-panel">
        <DropdownMenuLabel className="text-xs text-muted-foreground">
          {t('language.selectLanguage')}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {Object.entries(languages).map(([code, lang]) => (
          <DropdownMenuItem
            key={code}
            onClick={() => handleLanguageChange(code)}
            className={`flex items-center space-x-3 cursor-pointer ${
              i18n.language === code ? 'bg-muted/50' : ''
            }`}
          >
            <span className="text-lg">{lang.flag}</span>
            <span className="flex-1">{lang.name}</span>
            {i18n.language === code && (
              <div className="w-2 h-2 bg-primary rounded-full" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
