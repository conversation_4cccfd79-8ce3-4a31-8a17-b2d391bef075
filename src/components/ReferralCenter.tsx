
import { motion } from 'framer-motion';
import { Copy, Share2, Users, TrendingUp, Award, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { RewardStatCard } from '@/components/RewardStatCard';
import { createReferralService } from '@/lib/referralService';
import { useAuth } from '@/contexts/AuthContext';

const referralStats = {
  totalReferrals: 12,
  activeReferrals: 8,
  totalEarned: 240,
  pendingCredits: 50
};

const referralTiers = [
  { threshold: 5, reward: 25, label: 'Bronze Referrer', achieved: true },
  { threshold: 15, reward: 75, label: 'Silver Referrer', achieved: false },
  { threshold: 50, reward: 200, label: 'Gold Referrer', achieved: false },
  { threshold: 100, reward: 500, label: 'Diamond Referrer', achieved: false }
];

export function ReferralCenter() {
  const { user } = useAuth();
  const [referralLink, setReferralLink] = useState('');
  const { toast } = useToast();
  const referralService = createReferralService();

  useEffect(() => {
    let mounted = true;
    (async () => {
      if (!user?.id) return;
      // Attempt to fetch or create a referral link for the user
      const createRes = await referralService.createReferral(user.id);
      if (mounted && createRes.success && createRes.data) {
        const code = createRes.data.referral_code;
        setReferralLink(`${window.location.origin}/ref/${code}`);
      }
    })();
    return () => { mounted = false; };
  }, [user?.id]);

  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralLink);
    toast({
      title: "Copied!",
      description: "Referral link copied to clipboard",
    });
  };

  const shareReferralLink = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Join Heey.me',
        text: 'Create your AI avatar and bio link!',
        url: referralLink,
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Referral Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <RewardStatCard
          icon={<Users className="w-5 h-5 text-primary" />}
          value={referralStats.totalReferrals}
          label="Total Referrals"
          className="rounded-3xl"
        />

        <RewardStatCard
          icon={<TrendingUp className="w-5 h-5 text-primary" />}
          value={referralStats.activeReferrals}
          label="Active Users"
          className="rounded-3xl"
        />

        <RewardStatCard
          icon={<Award className="w-5 h-5 text-primary" />}
          value={referralStats.totalEarned}
          label="Total Earned"
          className="rounded-3xl"
        />

        <RewardStatCard
          icon={<TrendingUp className="w-5 h-5 text-primary" />}
          value={referralStats.pendingCredits}
          label="Pending"
          className="rounded-3xl"
        />
      </div>

      {/* Referral Link */}
      <Card className="glass-card rounded-3xl">
        <CardHeader>
          <CardTitle>Your Referral Link</CardTitle>
          <CardDescription>
            Share this link and earn $25 for each signup + $50 when they subscribe to Pro
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            <Input 
              value={referralLink} 
              readOnly 
              className="flex-1 bg-background/20"
            />
            <Button onClick={copyReferralLink} variant="outline" size="icon">
              <Copy className="w-4 h-4" />
            </Button>
            <Button onClick={shareReferralLink} variant="outline" size="icon">
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Referral Tiers */}
      <Card className="glass-card rounded-3xl">
        <CardHeader>
          <CardTitle>Referral Tiers</CardTitle>
          <CardDescription>
            Unlock bigger rewards as you refer more users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {referralTiers.map((tier, index) => (
              <div
                key={index}
                className={`flex items-center justify-between p-3 rounded-xl bg-background/20 backdrop-blur-sm ${
                  tier.achieved ? 'bg-green-500/10' : ''
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    tier.achieved ? 'bg-green-500' : 'bg-muted'
                  }`}>
                    {tier.achieved ? (
                      <Check className="w-4 h-4 text-white" />
                    ) : (
                      <span className="text-xs font-medium">{tier.threshold}</span>
                    )}
                  </div>
                  <div>
                    <p className="font-medium">{tier.label}</p>
                    <p className="text-sm text-muted-foreground">
                      {tier.threshold} referrals required
                    </p>
                  </div>
                </div>
                <Badge variant={tier.achieved ? "default" : "secondary"}>
                  {'+' + tier.reward + ' $HEEY'}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
