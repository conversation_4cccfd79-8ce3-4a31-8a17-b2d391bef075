
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useProfile } from '@/hooks/useProfile';
import { AvatarCreator } from '@/components/AvatarCreator';
import { User, Globe, Lock, Loader2 } from 'lucide-react';

export function ProfileSetup() {
  const { profile, updateProfile, isUpdating } = useProfile();
  const [showAvatarCreator, setShowAvatarCreator] = useState(false);
  const [formData, setFormData] = useState({
    username: profile?.username || '',
    display_name: profile?.display_name || '',
    bio: profile?.bio || '',
    is_public: profile?.is_public ?? true,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfile(formData);
  };

  const handleAvatarCreated = (avatarUrl: string) => {
    updateProfile({ avatar_url: avatarUrl });
    setShowAvatarCreator(false);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="heading-xl brand-gradient bg-clip-text text-transparent mb-2">
          Complete Your Profile
        </h1>
        <p className="body-lg text-muted-foreground">
          Set up your digital identity and create your 3D avatar
        </p>
      </motion.div>

      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Profile Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder="Your unique username"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="display_name">Display Name</Label>
                <Input
                  id="display_name"
                  name="display_name"
                  value={formData.display_name}
                  onChange={handleInputChange}
                  placeholder="Your display name"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="bio">Bio</Label>
              <Textarea
                id="bio"
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                placeholder="Tell visitors about yourself..."
                rows={3}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="flex items-center gap-2">
                  {formData.is_public ? <Globe className="h-4 w-4" /> : <Lock className="h-4 w-4" />}
                  Public Profile
                </Label>
                <p className="text-sm text-muted-foreground">
                  Allow others to view your profile and chat with your avatar
                </p>
              </div>
              <Switch
                checked={formData.is_public}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, is_public: checked }))
                }
              />
            </div>

            <Button 
              type="submit" 
              className="w-full brand-gradient hover:opacity-90"
              disabled={isUpdating}
            >
              {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Profile
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card className="glass-card">
        <CardHeader>
          <CardTitle>3D Avatar</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {profile?.avatar_url ? (
            <div className="text-center space-y-4">
              <div className="w-24 h-24 mx-auto rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                <User className="h-12 w-12 text-white" />
              </div>
              <p className="text-sm text-muted-foreground">Avatar created successfully!</p>
              <Button
                onClick={() => setShowAvatarCreator(true)}
                variant="outline"
                className="glass-button"
              >
                Update Avatar
              </Button>
            </div>
          ) : (
            <div className="text-center space-y-4">
              <div className="w-24 h-24 mx-auto rounded-full bg-muted flex items-center justify-center">
                <User className="h-12 w-12 text-muted-foreground" />
              </div>
              <Button
                onClick={() => setShowAvatarCreator(true)}
                className="brand-gradient hover:opacity-90"
              >
                Create 3D Avatar
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <AvatarCreator
        isOpen={showAvatarCreator}
        onClose={() => setShowAvatarCreator(false)}
        onAvatarCreated={handleAvatarCreated}
        currentAvatarUrl={profile?.avatar_url}
      />
    </div>
  );
}
