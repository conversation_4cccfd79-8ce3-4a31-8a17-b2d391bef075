import { ChatInterface } from '@/components/ChatInterface';
import type { AIPersonality } from '@/types/chat';

interface ChatWindowProps {
  isLoggedIn: boolean;
  onAuthRequired: () => void;
  profileOwnerId?: string;
  profileOwnerName?: string;
  profileOwnerAvatar?: string;
  aiPersonality?: AIPersonality;
  requireAuth?: boolean;
  placeholder?: string;
  className?: string;
}

export function ChatWindow({ 
  isLoggedIn, 
  onAuthRequired,
  profileOwnerId,
  profileOwnerName,
  profileOwnerAvatar,
  aiPersonality,
  requireAuth = true,
  placeholder,
  className
}: ChatWindowProps) {
  return (
    <div className={className ?? "w-full max-h-[50vh] h-96"}>
      <ChatInterface
        requireAuth={requireAuth}
        isLoggedIn={isLoggedIn}
        onAuthRequired={onAuthRequired}
        profileOwnerId={profileOwnerId}
        profileOwnerName={profileOwnerName}
        profileOwnerAvatar={profileOwnerAvatar}
        aiPersonality={aiPersonality}
        placeholder={placeholder}
      />
    </div>
  );
}
