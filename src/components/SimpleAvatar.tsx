
import { useEffect } from 'react';
import { loadModelViewer } from './AvatarViewer';

export function SimpleAvatar() {
  useEffect(() => {
    loadModelViewer();
  }, []);
  
  return (
    <model-viewer
      src="/assets/avatar.glb"
      camera-controls
      auto-rotate
      ar
      loading="lazy"
      reveal="interaction"
      style={{ width: '100%', height: '100%' }}
    ></model-viewer>
  );
}
