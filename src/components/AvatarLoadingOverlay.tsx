
import { motion } from 'framer-motion';

interface AvatarLoadingOverlayProps {
  isVisible: boolean;
}

export function AvatarLoadingOverlay({ isVisible }: AvatarLoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="absolute inset-0 z-20 flex items-center justify-center backdrop-blur-sm bg-background/80"
    >
      <div className="glass-card rounded-xl p-8 text-center shadow-xl">
        <div className="mx-auto mb-4 h-16 w-16 relative">
          <div className="absolute inset-0 rounded-full border-4 border-primary/20"></div>
          <div className="absolute inset-0 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
        <h3 className="mb-2 text-lg font-semibold">Creating Avatar</h3>
        <p className="text-sm text-muted-foreground">Please wait while we generate your avatar...</p>
      </div>
    </motion.div>
  );
}
