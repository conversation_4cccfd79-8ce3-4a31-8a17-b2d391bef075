import { motion } from 'framer-motion';
import { ReadyPlayerMeService } from '@/lib/readyPlayerMeService';

export function AvatarDisplay() {
  const rpm = new ReadyPlayerMeService(import.meta.env.VITE_READY_PLAYER_ME_SUBDOMAIN || 'heey');
  const url = rpm.generateAvatarURL({ bodyType: 'fullbody', quickStart: true });
  return (
    <motion.iframe
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
      src={url}
      className="w-full h-full border-none"
      title="Avatar"
      allow="camera *; microphone *; clipboard-write"
      sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-storage-access-by-user-activation"
    />
  );
}
