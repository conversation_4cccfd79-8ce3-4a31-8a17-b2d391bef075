
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Share2, Copy, ExternalLink } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';

export function SocialSharePanel() {
  const { toast } = useToast();
  const { t } = useTranslation();
  const bioLink = 'heey.me/johndoe';

  const socialPlatforms = [
    {
      name: 'Facebook',
      color: 'bg-blue-600 hover:bg-blue-700',
      shareUrl: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(`https://${bioLink}`)}`
    },
    {
      name: 'Twitter',
      color: 'bg-sky-500 hover:bg-sky-600',
      shareUrl: `https://twitter.com/intent/tweet?url=${encodeURIComponent(`https://${bioLink}`)}&text=${encodeURIComponent('Check out my AI assistant!')}`
    },
    {
      name: 'LinkedIn',
      color: 'bg-blue-700 hover:bg-blue-800',
      shareUrl: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(`https://${bioLink}`)}`
    },
    {
      name: 'WhatsApp',
      color: 'bg-green-600 hover:bg-green-700',
      shareUrl: `https://wa.me/?text=${encodeURIComponent(`Check out my AI assistant: https://${bioLink}`)}`
    },
    {
      name: 'Telegram',
      color: 'bg-blue-500 hover:bg-blue-600',
      shareUrl: `https://t.me/share/url?url=${encodeURIComponent(`https://${bioLink}`)}&text=${encodeURIComponent('Check out my AI assistant!')}`
    }
  ];

  const copyToClipboard = () => {
    navigator.clipboard.writeText(`https://${bioLink}`);
    toast({
      title: t('socialSharePanel.linkCopied'),
      description: t('socialSharePanel.linkCopiedDesc'),
    });
  };

  const handleShare = (url: string) => {
    window.open(url, '_blank', 'width=600,height=400');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Bio Link Display */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            {t('socialSharePanel.shareYourBioLink')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="glass-panel p-6 rounded-lg text-center">
            <h3 className="text-xl font-semibold mb-2">{t('socialSharePanel.yourBioLink')}</h3>
            <div className="flex items-center justify-center gap-3 mb-4">
              <span className="font-mono text-lg text-primary bg-primary/10 px-4 py-2 rounded-lg">
                {bioLink}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={copyToClipboard}
                className="flex items-center gap-2"
              >
                <Copy className="h-4 w-4" />
                {t('socialSharePanel.copy')}
              </Button>
            </div>
            <Button
              variant="outline"
              onClick={() => window.open(`https://${bioLink}`, '_blank')}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              {t('socialSharePanel.visitLink')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Social Share Buttons */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle>{t('socialSharePanel.shareOnSocial')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {socialPlatforms.map((platform, index) => (
              <motion.div
                key={platform.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Button
                  onClick={() => handleShare(platform.shareUrl)}
                  className={`w-full ${platform.color} text-white border-none`}
                  size="lg"
                >
                  {t('socialSharePanel.shareOn', { platform: platform.name })}
                </Button>
              </motion.div>
            ))}
          </div>
          
          <div className="mt-6 p-4 glass-panel rounded-lg">
            <p className="text-sm text-muted-foreground text-center">
              {t('socialSharePanel.shareDescription')}
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
