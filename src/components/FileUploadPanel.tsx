
import { motion } from 'framer-motion';
import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, File, X, FileText } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface FileUploadPanelProps {
  uploadedFiles: File[];
  onFilesChange: (files: File[]) => void;
}

export function FileUploadPanel({ uploadedFiles, onFilesChange }: FileUploadPanelProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const { t } = useTranslation();

  const handleFileUpload = (files: FileList | null) => {
    if (!files) return;
    
    const newFiles = Array.from(files).filter(file => 
      file.type === 'application/pdf' || file.type.startsWith('text/')
    );
    
    onFilesChange([...uploadedFiles, ...newFiles]);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const removeFile = (index: number) => {
    const newFiles = uploadedFiles.filter((_, i) => i !== index);
    onFilesChange(newFiles);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="h-full"
    >
      <Card className="glass-panel h-full">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-5 h-5" />
            <span>{t('fileUploadPanel.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              isDragOver 
                ? 'border-primary bg-primary/10' 
                : 'border-white/20 hover:border-white/40'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <Upload className="w-8 h-8 mx-auto mb-3 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-2">
              {t('fileUploadPanel.drop')}
            </p>
            <Button
              variant="outline"
              className="glass-button"
              onClick={() => document.getElementById('file-upload')?.click()}
            >
              {t('fileUploadPanel.chooseFiles')}
            </Button>
            <input
              id="file-upload"
              type="file"
              multiple
              accept=".pdf,.txt,.md"
              className="hidden"
              onChange={(e) => handleFileUpload(e.target.files)}
            />
          </div>

          {/* Uploaded Files List */}
          {uploadedFiles.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">{t('fileUploadPanel.uploadedFiles', { count: uploadedFiles.length })}</h4>
              <div className="max-h-32 overflow-y-auto space-y-2">
                {uploadedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 glass-button rounded-lg"
                  >
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <File className="w-4 h-4 text-primary flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{file.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="text-muted-foreground hover:text-destructive"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Info */}
          <div className="p-3 glass-button rounded-lg">
            <h4 className="font-medium mb-2 text-sm">{t('fileUploadPanel.supportedFormats')}</h4>
            <p className="text-xs text-muted-foreground">
              {t('fileUploadPanel.formatDescription')}
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
