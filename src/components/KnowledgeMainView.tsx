
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { PageHero } from '@/components/PageHero';
import { FileUploadPanel } from '@/components/FileUploadPanel';
import { SocialLinksPanel } from '@/components/SocialLinksPanel';
import { ChatPersonalization } from '@/components/ChatPersonalization';
import { AppIcon } from '@/components/AppIcon';
import { useTranslation } from 'react-i18next';

interface KnowledgeMainViewProps {
  selectedSection: string | null;
  uploadedFiles: File[];
  onFilesChange: (files: File[]) => void;
  socialLinks: Record<string, unknown>;
  onSocialLinksChange: (links: Record<string, unknown>) => void;
  chatSettings: Record<string, unknown>;
  onChatSettingsChange: (settings: Record<string, unknown>) => void;
}

export function KnowledgeMainView({
  selectedSection, 
  uploadedFiles,
  onFilesChange,
  socialLinks,
  onSocialLinksChange,
  chatSettings,
  onChatSettingsChange
}: KnowledgeMainViewProps) {
  const { t } = useTranslation();

  if (!selectedSection) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-md"
        >
          <AppIcon name="Upload" alt="Upload" className="h-16 w-16 text-muted-foreground/50 mx-auto mb-6" />
          <h2 className="heading-lg mb-3 text-muted-foreground">
            Select an option from the sidebar
          </h2>
          <p className="body-md text-muted-foreground">
            Choose from avatar, documents, social links, or personality settings
          </p>
        </motion.div>
      </div>
    );
  }

  const getSectionConfig = () => {
    switch (selectedSection) {
      case 'documents':
        return {
          iconName: 'Document',
          title: 'Document Upload',
          description: 'Upload documents to enhance your AI knowledge base'
        };
    case 'social':
      return {
        iconName: 'Mail',
        title: 'Social Links',
        description: 'Connect your social media profiles and websites'
      };
    case 'personality':
      return {
        iconName: 'Palette',
        title: 'Personality Settings',
        description: 'Customize your AI personality and conversation style'
      };
      default:
        return null;
    }
  };

  const sectionConfig = getSectionConfig();

  const renderSectionContent = () => {
    switch (selectedSection) {
      case 'documents':
        return (
          <Card className="glass-card w-full">
            <CardContent className="p-6">
              <FileUploadPanel 
                uploadedFiles={uploadedFiles}
                onFilesChange={onFilesChange}
              />
            </CardContent>
          </Card>
        );
      
      case 'social':
        return (
          <Card className="glass-card w-full">
            <CardContent className="p-6">
              <SocialLinksPanel 
                socialLinks={socialLinks}
                onChange={onSocialLinksChange}
              />
            </CardContent>
          </Card>
        );
      
      case 'personality':
        return (
          <Card className="glass-card w-full">
            <CardContent className="p-6">
              <ChatPersonalization />
            </CardContent>
          </Card>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="flex-1 p-4 md:p-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-4xl mx-auto"
      >
        {sectionConfig && (
          <div className="mb-8">
            <PageHero
              iconName={sectionConfig.iconName}
              title={sectionConfig.title}
              description={sectionConfig.description}
            />
          </div>
        )}
        {renderSectionContent()}
      </motion.div>
    </div>
  );
}
