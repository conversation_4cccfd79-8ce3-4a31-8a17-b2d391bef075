import { useEffect, useState } from 'react';
import { LoginModal } from '@/components/LoginModal';

interface LoginGateProps {
  children: React.ReactNode;
  onLogin?: () => void;
}

export function LoginGate({ children, onLogin }: LoginGateProps) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    const user = localStorage.getItem('user');
    setIsLoggedIn(!!user);
    if (!user) setShowModal(true);
  }, []);

  const handleSuccess = () => {
    setShowModal(false);
    setIsLoggedIn(true);
    onLogin?.();
  };

  if (!isLoggedIn) {
    return (
      <LoginModal isOpen={showModal} onClose={() => {}} onSuccess={handleSuccess} />
    );
  }

  return <>{children}</>;
}
