
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { useNavigate } from 'react-router-dom';
import '@google/model-viewer';
import { useTranslation } from 'react-i18next';
import { useRTL } from '@/hooks/useRTL';

interface AvatarIntroSectionProps {
  onStart?: () => void;
}

export function AvatarIntroSection({ onStart }: AvatarIntroSectionProps) {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useRTL();

  const handleCreateAvatar = () => {
    if (onStart) {
      onStart();
    } else {
      navigate('/avatar/create');
    }
  };

  return (
    <div className={`space-y-8 ${isRTL ? 'rtl' : ''}`}>
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center space-y-4"
      >
        <AppIcon
          name="GamePad"
          alt={t('avatar.title')}
          className="h-16 w-16 text-primary mx-auto mb-2"
        />
        <h1 className="heading-lg text-gradient">
          {t('avatar.title')}
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
          {t('avatar.description')}
        </p>
      </motion.div>


      {/* 3D Avatar Preview */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="flex justify-center"
      >
          <div className="relative w-full h-64 sm:h-80 md:h-96 max-w-xs sm:max-w-sm md:max-w-md mx-auto">
            <model-viewer
              src="/assets/models/avatarsample.glb"
              camera-controls
              auto-rotate
              disable-zoom
              shadow-intensity="1"
              style={{ width: '100%', height: '100%' }}
            ></model-viewer>
            <div className="absolute inset-0 bg-gradient-to-t from-background/20 to-transparent rounded-2xl pointer-events-none" />
          </div>
      </motion.div>

      {/* CTA Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="text-center"
      >
        <Button
          onClick={handleCreateAvatar}
          size="lg"
          className="btn-primary text-lg px-8 py-4 rounded-xl transform hover:scale-105 transition-all duration-200"
        >
          {t('avatar.createButton')}
        </Button>
      </motion.div>
    </div>
  );
}
