
import { motion } from 'framer-motion';
import { Logo } from '@/components/Logo';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';
import { AppIcon } from '@/components/AppIcon';
import { useNavigate, useLocation } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useRTL } from '@/hooks/useRTL';
import walletIcon from '/3D-coin/heey-wallet-icon.webp';

interface User {
  email: string;
  username: string;
  avatarUrl?: string;
}

export function Header() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { isRTL } = useRTL();
  const [user, setUser] = useState<User | null>(null);
  const isLandingPage = location.pathname === '/';

  useEffect(() => {
    if (!isLandingPage) {
      // For app pages, always show user data (create default if none exists)
      const userData = localStorage.getItem('user');
      if (userData) {
        setUser(JSON.parse(userData));
      } else {
        // Set default user data for demo
        const defaultUser = {
          email: '<EMAIL>',
          username: 'User',
          avatarUrl: ''
        };
        localStorage.setItem('user', JSON.stringify(defaultUser));
        setUser(defaultUser);
      }
    } else {
      // For landing page, check if user exists but don't create default
      const userData = localStorage.getItem('user');
      if (userData) {
        setUser(JSON.parse(userData));
      }
    }
  }, [location, isLandingPage]);

  const handleLogin = () => {
    // For demo purposes, create a user and navigate to topics
    const defaultUser = {
      email: '<EMAIL>',
      username: 'User',
      avatarUrl: ''
    };
    localStorage.setItem('user', JSON.stringify(defaultUser));
    setUser(defaultUser);
    navigate('/topics');
  };

  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className={`fixed top-0 left-0 right-0 z-50 h-14 sm:h-16 glass-header safe-area-top ${isRTL ? 'rtl' : ''}`}
    >
      <div className="mobile-container h-full flex items-center">
        <div className="flex items-center justify-between w-full">
          <Logo size="sm" />
          
          {/* Navigation based on page type */}
          {isLandingPage ? (
            <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <LanguageSwitcher className="bg-transparent p-0 h-8 w-8" />
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full glass-button h-10 w-10"
                onClick={() => navigate('/my-account/token-rewards')}
              >
                <img src="/gift.png" alt={t('navigation.tokenRewards')} className="h-5 w-5" />
              </Button>
              {user ? (
                <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full p-0 h-8 w-8 bg-transparent"
                    onClick={() => navigate('/wallet')}
                  >
                    <img src={walletIcon} alt="Wallet" className="h-full w-full animate-pulse" />
                  </Button>
                  <Button
                    variant="ghost"
                    className={`flex items-center space-x-2 h-10 px-3 bg-transparent ${isRTL ? 'space-x-reverse' : ''}`}
                    onClick={() => navigate('/my-account')}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.avatarUrl} alt={user.username} />
                      <AvatarFallback className="bg-primary/20 text-primary font-medium text-sm">
                        {user.username?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="hidden sm:inline-block text-sm font-medium text-white">
                      {user.username || 'User'}
                    </span>
                  </Button>
                </div>
              ) : (
                <Button
                  onClick={handleLogin}
                  className="btn-primary"
                >
                  {t('common.login')}
                </Button>
              )}
            </div>
          ) : (
            <div className={`flex items-center ${isRTL ? 'space-x-reverse' : ''}`}>
              <LanguageSwitcher className="bg-transparent p-0 h-8 w-8" />
              <Button
                variant="ghost"
                size="icon"
                className={`rounded-full glass-button h-10 w-10 ${isRTL ? 'ml-2' : 'mr-2'}`}
                onClick={() => navigate('/my-account/token-rewards')}
              >
                <img src="/gift.png" alt={t('navigation.tokenRewards')} className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className={`rounded-full p-0 h-8 w-8 bg-transparent ${isRTL ? 'ml-2' : 'mr-2'}`}
                onClick={() => navigate('/wallet')}
              >
                <img src={walletIcon} alt="Wallet" className="h-full w-full animate-pulse" />
              </Button>
              {user && (
                <Button
                  variant="ghost"
                  className={`flex items-center space-x-2 h-10 px-3 bg-transparent ${isRTL ? 'space-x-reverse' : ''}`}
                  onClick={() => navigate('/my-account')}
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.avatarUrl} alt={user.username} />
                    <AvatarFallback className="bg-primary/20 text-primary font-medium text-sm">
                      {user.username?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <span className="hidden sm:inline-block text-sm font-medium text-white">
                    {user.username || 'User'}
                  </span>
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.header>
  );
}
