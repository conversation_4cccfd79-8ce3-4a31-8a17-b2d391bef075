
import { useEffect, useMemo, useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { AppIcon } from '@/components/AppIcon';
import { TopicsSidebar } from '@/components/TopicsSidebar';
import { TopicsMainView } from '@/components/TopicsMainView';
import { useTranslation } from 'react-i18next';
import { useHideGlobalHeaderOnScroll } from '@/hooks/useHideGlobalHeaderOnScroll';
import { chatService } from '@/lib/chatService';
import { useAuth } from '@/contexts/AuthContext';

interface UITopic {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: string;
  visitorName: string;
  messageCount: number;
}

export default function Topics() {
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);
  const [topics, setTopics] = useState<UITopic[]>([]);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const headerRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  useHideGlobalHeaderOnScroll(headerRef);
  const { user } = useAuth();

  useEffect(() => {
    let isMounted = true;
    async function loadTopics() {
      if (!user?.id) return;
      const result = await chatService.getUserTopics(user.id, 50);
      if (!isMounted) return;
      if (result.success && result.data) {
        // Map backend topics to UI summaries (placeholder values for missing fields)
        const mapped: UITopic[] = result.data.map((t) => ({
          id: t.id,
          title: t.topic,
          lastMessage: t.summary || '',
          timestamp: new Date(t.created_at).toLocaleString(),
          visitorName: 'Visitor',
          messageCount: 0
        }));
        setTopics(mapped);
      } else {
        setTopics([]);
      }
    }
    loadTopics();
    return () => { isMounted = false; };
  }, [user?.id]);

  const selectedTopicData = useMemo(() => topics.find(topic => topic.id === selectedTopic), [topics, selectedTopic]);

  return (
    <div className="min-h-full flex">
      <div className="hidden md:flex">
        <TopicsSidebar
          topics={topics}
          selectedTopic={selectedTopic}
          onSelectTopic={setSelectedTopic}
          className="w-80 border-r border-border/50"
        />
      </div>

      <div className="flex-1 flex flex-col min-h-full">
        <div 
          ref={headerRef} 
          className="md:hidden sticky top-0 z-50 glass-header border-b border-border/50 p-4 transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(true)}
              className="glass-button"
            >
              <AppIcon name="Hamburger" alt="Menu" className="h-10 w-10" />
            </Button>
            <h1 className="heading-md">{t('topics.chatTopics')}</h1>
            <div className="w-10" />
          </div>
        </div>

        <div className="flex-1">
          <TopicsMainView selectedTopic={selectedTopicData} />
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <motion.div
            initial={{ x: -320 }}
            animate={{ x: 0 }}
            exit={{ x: -320 }}
            className="w-80 h-full bg-background/95 backdrop-blur-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <TopicsSidebar
              topics={topics}
              selectedTopic={selectedTopic}
              onSelectTopic={(topicId) => {
                setSelectedTopic(topicId);
                setSidebarOpen(false);
              }}
              onClose={() => setSidebarOpen(false)}
            />
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}
