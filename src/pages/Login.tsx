
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { motion } from 'framer-motion';
import { TikTokBackground } from '@/components/TikTokBackground';
import { AuthPanel } from '@/components/AuthPanel';
import { ErrorBoundary } from '@/components/ErrorBoundary';

export default function Login() {
  const [authMode, setAuthMode] = useState<'login' | 'signup'>('login');
  const navigate = useNavigate();
  const { signIn, signUp } = useAuth();

  const handleAuthSubmit = async (email: string, password: string) => {
    console.log('Auth attempt:', { mode: authMode, email, password });

    let error;
    if (authMode === 'login') {
      ({ error } = await signIn(email, password));
    } else {
      ({ error } = await signUp(email, password));
    }

    if (!error) {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('avatar_url')
          .eq('id', user.id)
          .single();

        if (profile?.avatar_url) {
          navigate('/topics');
        } else {
          navigate('/avatar');
        }
      } else {
        navigate('/avatar');
      }
    }
  };

  const toggleAuthMode = () => {
    setAuthMode(authMode === 'login' ? 'signup' : 'login');
  };

  return (
    <motion.div
      className="min-h-screen mobile-full-screen w-full flex items-center justify-center relative overflow-hidden bg-background"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      <ErrorBoundary fallback={<div className="fixed inset-0 bg-background" />}> 
        <TikTokBackground />
      </ErrorBoundary>
      
      <div className="relative z-10 w-full flex items-center justify-center px-4">
        <AuthPanel 
          mode={authMode}
          onToggleMode={toggleAuthMode}
          onSubmit={handleAuthSubmit}
        />
      </div>
    </motion.div>
  );
}
