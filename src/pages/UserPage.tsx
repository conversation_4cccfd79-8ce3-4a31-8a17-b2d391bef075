import { motion } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Send, MessageCircle, User, Edit3 } from 'lucide-react';
import { TikTokBackground } from '@/components/TikTokBackground';
import { AuthPanel } from '@/components/AuthPanel';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { useTranslation } from 'react-i18next';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

interface UserProfile {
  username: string;
  displayName: string;
  bio: string;
  avatarUrl: string | null;
  isOnline: boolean;
}

export default function UserPage() {
  const { username } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showAuthPanel, setShowAuthPanel] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'signup'>('login');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const userData = localStorage.getItem('user');
    setIsAuthenticated(!!userData);

    const display = username || 'Demo User';

    setProfile({
      username: username || 'demo',
      displayName: display,
      bio: t('userPage.bioDefault'),
      avatarUrl: null,
      isOnline: true
    });

    setMessages([
      {
        id: '1',
        content: t('userPage.initialMessage', { user: display }),
        sender: 'ai',
        timestamp: new Date()
      }
    ]);
  }, [username, t]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;
    
    if (!isAuthenticated) {
      setShowAuthPanel(true);
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      content: newMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');
    setIsLoading(true);

    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: `Thanks for your message: "${newMessage}". This is a simulated AI response. In the real app, this would connect to your LLM API endpoint.`,
        sender: 'ai',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 1500);
  };

  const handleAuthSubmit = (email: string, password: string) => {
    localStorage.setItem('user', JSON.stringify({ 
      email, 
      username: email.split('@')[0] 
    }));
    setIsAuthenticated(true);
    setShowAuthPanel(false);
  };

  const handleCreateAvatar = () => {
    console.log('Opening Ready Player Me for avatar creation...');
    alert('Ready Player Me would initialize here for avatar creation/editing');
  };

  if (!profile) {
    return (
      <div className="min-h-screen mobile-full-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen mobile-full-screen relative bg-background">
      <ErrorBoundary fallback={<div className="fixed inset-0 bg-background" />}> 
        <TikTokBackground />
      </ErrorBoundary>
      
      <div className="relative z-10 h-screen flex">
        {/* Left Section - Avatar & 3D Environment */}
        <motion.div 
          className="flex-1 lg:flex-[2] relative"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="h-full flex flex-col items-center justify-center p-8">
            {/* Profile Header */}
            <motion.div 
              className="text-center mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-lime-gradient flex items-center justify-center">
                {profile.avatarUrl ? (
                  <img 
                    src={profile.avatarUrl} 
                    alt={profile.displayName}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <User className="w-12 h-12 text-white" />
                )}
              </div>
              <h1 className="heading-lg mb-2">{profile.displayName}</h1>
              <p className="body-md text-muted-foreground mb-4">{profile.bio}</p>
              <div className="flex items-center justify-center gap-2">
                <div className={`w-2 h-2 rounded-full ${profile.isOnline ? 'bg-green-500' : 'bg-gray-500'}`} />
                <span className="body-sm">{profile.isOnline ? t('userPage.online') : t('userPage.offline')}</span>
              </div>
            </motion.div>

            {/* Ready Player Me Container */}
            <motion.div
              id="rpm-sdk-container"
              className="w-full max-w-md h-96 glass-panel rounded-xl flex items-center justify-center"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="text-center p-8">
                <Edit3 className="w-16 h-16 mx-auto mb-4 text-lime-500" />
                <h3 className="font-semibold mb-2">{t('userPage.avatarSpaceTitle')}</h3>
                <p className="body-sm text-muted-foreground mb-4">
                  {t('userPage.avatarSpaceDescription')}
                </p>
                <Button
                  onClick={handleCreateAvatar}
                  className="brand-gradient hover:opacity-90"
                >
                  {t('userPage.createAvatarButton')}
                </Button>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Right Section - Chat Panel */}
        <motion.div 
          className="w-full lg:w-96 xl:w-[28rem] flex flex-col"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <Card className="glass-panel h-full flex flex-col m-4 border-white/20">
            <CardHeader className="border-b border-white/10">
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="w-5 h-5 text-lime-500" />
                {t('userPage.chatTitle', { user: profile.displayName })}
              </CardTitle>
              {!isAuthenticated && (
                <Badge variant="outline" className="w-fit text-xs">
                  {t('userPage.loginRequired')}
                </Badge>
              )}
            </CardHeader>

            {/* Messages */}
            <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`
                    max-w-[80%] p-3 rounded-lg
                    ${message.sender === 'user' 
                      ? 'bg-lime-gradient text-white' 
                      : 'glass-button'
                    }
                  `}>
                    <p className="body-sm">{message.content}</p>
                    <p className="caption opacity-70 mt-1">
                      {message.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </motion.div>
              ))}
              
              {isLoading && (
                <div className="flex justify-start">
                  <div className="glass-button p-3 rounded-lg">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-lime-500 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-lime-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-lime-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </CardContent>

            {/* Message Input */}
            <div className="p-4 border-t border-white/10">
              <div className="flex gap-2">
                <Input
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder={isAuthenticated ? t('userPage.typePlaceholder') : t('userPage.loginPlaceholder')}
                  className="glass-button flex-1"
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  disabled={!isAuthenticated}
                />
                <Button 
                  onClick={handleSendMessage}
                  className="brand-gradient hover:opacity-90"
                  disabled={!newMessage.trim() || isLoading}
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>

      {/* Auth Panel Overlay */}
      {showAuthPanel && (
        <motion.div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onClick={() => setShowAuthPanel(false)}
        >
          <div onClick={(e) => e.stopPropagation()}>
            <AuthPanel 
              mode={authMode}
              onToggleMode={() => setAuthMode(authMode === 'login' ? 'signup' : 'login')}
              onSubmit={handleAuthSubmit}
            />
          </div>
        </motion.div>
      )}
    </div>
  );
}
