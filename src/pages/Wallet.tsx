
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TikTokBackground } from '@/components/TikTokBackground';
import { WalletBalanceCard } from '@/components/wallet/WalletBalanceCard';
import { TierCard } from '@/components/wallet/TierCard';
import { PurchaseModal } from '@/components/wallet/PurchaseModal';
import { useToast } from '@/hooks/use-toast';

// Mock data for demonstration
const TIER_DATA = [
  {
    id: '1',
    name: 'Starter',
    icon: 'Person',
    minBuy: 50,
    maxBuy: 500,
    tokenPrice: 0.10,
    bonusPercent: 0,
    totalTokens: 100000,
    soldTokens: 65000,
    benefits: [
      'Basic $HEEY allocation',
      'Access to community features',
      'Standard transaction fees'
    ]
  },
  {
    id: '2',
    name: 'Pro',
    icon: 'Star',
    minBuy: 500,
    maxBuy: 2500,
    tokenPrice: 0.08,
    bonusPercent: 15,
    totalTokens: 75000,
    soldTokens: 45000,
    benefits: [
      '15% bonus tokens',
      'Priority customer support',
      'Reduced transaction fees',
      'Early access to new features'
    ],
    isPopular: true
  },
  {
    id: '3',
    name: 'VIP',
    icon: 'Crown',
    minBuy: 2500,
    maxBuy: 10000,
    tokenPrice: 0.06,
    bonusPercent: 25,
    totalTokens: 50000,
    soldTokens: 18000,
    benefits: [
      '25% bonus tokens',
      'VIP support & account manager',
      'Zero transaction fees',
      'Exclusive VIP features',
      'Priority in new token sales'
    ],
    isVip: true
  }
];

export default function Wallet() {
  const [isWalletConnected, setIsWalletConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string>('');
  const [heeyBalance, setHeeyBalance] = useState(0);
  const [usdValue, setUsdValue] = useState(0);
  const [selectedTier, setSelectedTier] = useState<string | null>(null);
  const [isPurchaseModalOpen, setIsPurchaseModalOpen] = useState(false);
  const { toast } = useToast();

  // Mock wallet connection
  const handleConnectWallet = async () => {
    // Simulate wallet connection
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsWalletConnected(true);
    setWalletAddress('******************************************');
    setHeeyBalance(12500);
    setUsdValue(1250);
    toast({
      title: "Wallet Connected",
      description: "Successfully connected to your Web3 wallet",
    });
  };

  const handleDisconnectWallet = () => {
    setIsWalletConnected(false);
    setWalletAddress('');
    setHeeyBalance(0);
    setUsdValue(0);
    toast({
      title: "Wallet Disconnected",
      description: "Your wallet has been disconnected",
    });
  };

  const handleBuyTokens = (tierId: string) => {
    setSelectedTier(tierId);
    setIsPurchaseModalOpen(true);
  };

  const handleConfirmPurchase = async (amount: number) => {
    // Simulate blockchain transaction
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const tier = TIER_DATA.find(t => t.id === selectedTier);
    if (tier) {
      const tokensReceived = amount / tier.tokenPrice;
      const bonusTokens = tokensReceived * (tier.bonusPercent / 100);
      const totalTokens = tokensReceived + bonusTokens;
      
      setHeeyBalance(prev => prev + totalTokens);
      setUsdValue(prev => prev + (totalTokens * 0.10)); // Assuming $0.10 current price
      
      toast({
        title: "Purchase Successful!",
        description: `You received ${totalTokens.toFixed(2)} $HEEY tokens`,
      });
    }
    
    setIsPurchaseModalOpen(false);
    setSelectedTier(null);
  };

  const selectedTierData = TIER_DATA.find(t => t.id === selectedTier);

  return (
    <div className="min-h-screen mobile-full-screen bg-background relative">
      <TikTokBackground />
      
      <div className="relative z-10 w-full max-w-none px-4 md:px-6 lg:px-8 py-8 space-y-12 glass-section">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center space-y-6"
        >
          <div className="flex items-center justify-center space-x-4">
            <img src="/3D-coin/heey-wallet-icon.webp" alt="HEEY Wallet" className="w-20 h-20 md:w-24 md:h-24" />
            <div>
              <h1 className="heading-xl text-white mb-2">$HEEY Wallet</h1>
              <p className="body-lg text-muted-foreground">Premium Token Launchpad</p>
            </div>
          </div>
          
          <div className="inline-flex items-center px-8 py-4 glass-card">
            <span className="body-md font-medium text-[#83FFBC]">🚀 Genesis Offering • Limited Time</span>
          </div>
        </motion.div>

        {/* Wallet Balance Card */}
        <div className="w-full max-w-4xl mx-auto">
          <WalletBalanceCard
            balance={heeyBalance}
            usdValue={usdValue}
            isConnected={isWalletConnected}
            walletAddress={walletAddress}
            onConnect={handleConnectWallet}
            onDisconnect={handleDisconnectWallet}
          />
        </div>

        {/* Tier Selection */}
        <div className="space-y-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-center"
          >
            <h2 className="heading-lg text-white mb-4">Choose Your Tier</h2>
            <p className="body-lg text-muted-foreground max-w-2xl mx-auto">
              Select the perfect tier for your investment goals and unlock exclusive benefits
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 md:gap-8 max-w-7xl mx-auto">
            {TIER_DATA.map((tier, index) => (
              <motion.div
                key={tier.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              >
                <TierCard
                  tier={tier}
                  onBuy={handleBuyTokens}
                  isConnected={isWalletConnected}
                />
              </motion.div>
            ))}
          </div>
        </div>

        {/* Enhanced Footer Info with improved glassmorphism */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center space-y-6 pt-12"
        >
          <div className="glass-section p-8 md:p-10 max-w-4xl mx-auto">
            <h3 className="heading-md text-white mb-6">Why $HEEY?</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
              <div className="space-y-4">
                <div className="w-16 h-16 bg-[#83FFBC]/10 backdrop-blur-lg rounded-full flex items-center justify-center mx-auto shadow-lg">
                  <span className="text-2xl">🎯</span>
                </div>
                <p className="body-lg font-semibold text-white">Utility Token</p>
                <p className="body-md text-muted-foreground">Power Heey platform features and unlock premium content</p>
              </div>
              <div className="space-y-4">
                <div className="w-16 h-16 bg-[#83FFBC]/10 backdrop-blur-lg rounded-full flex items-center justify-center mx-auto shadow-lg">
                  <span className="text-2xl">🔥</span>
                </div>
                <p className="body-lg font-semibold text-white">Limited Supply</p>
                <p className="body-md text-muted-foreground">Deflationary tokenomics with built-in scarcity</p>
              </div>
              <div className="space-y-4">
                <div className="w-16 h-16 bg-[#83FFBC]/10 backdrop-blur-lg rounded-full flex items-center justify-center mx-auto shadow-lg">
                  <span className="text-2xl">🚀</span>
                </div>
                <p className="body-lg font-semibold text-white">Growth Potential</p>
                <p className="body-md text-muted-foreground">Early adopter benefits and exclusive rewards</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Purchase Modal */}
      {selectedTierData && (
        <PurchaseModal
          isOpen={isPurchaseModalOpen}
          onClose={() => {
            setIsPurchaseModalOpen(false);
            setSelectedTier(null);
          }}
          tierName={selectedTierData.name}
          tokenPrice={selectedTierData.tokenPrice}
          minBuy={selectedTierData.minBuy}
          maxBuy={selectedTierData.maxBuy}
          bonusPercent={selectedTierData.bonusPercent}
          onConfirm={handleConfirmPurchase}
        />
      )}
    </div>
  );
}
