
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChatInterface } from '@/components/ChatInterface';
import { useNavigate } from 'react-router-dom';
import { MessageCircle, Palette, Brain, Users, Zap, Plus, TrendingUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export default function Dashboard() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const quickActions = [
    {
      title: t('dashboard.actions.newChat'),
      description: t('dashboard.actions.startConversation'),
      icon: MessageCircle,
      action: () => console.log('New chat'),
      gradient: 'from-primary/20 to-primary/5'
    },
    {
      title: t('dashboard.actions.createAvatar'),
      description: t('dashboard.actions.customizePersona'),
      icon: Palette,
      action: () => navigate('/avatar'),
      gradient: 'from-blue-500/20 to-blue-500/5'
    },
    {
      title: t('dashboard.actions.trainAI'),
      description: t('dashboard.actions.uploadKnowledge'),
      icon: Brain,
      action: () => navigate('/knowledge'),
      gradient: 'from-purple-500/20 to-purple-500/5'
    },
    {
      title: t('dashboard.actions.viewNetwork'),
      description: t('dashboard.actions.exploreConnections'),
      icon: Users,
      action: () => navigate('/network'),
      gradient: 'from-green-500/20 to-green-500/5'
    },
  ];

  const stats = [
    { label: t('dashboard.stats.conversations'), value: '127', icon: MessageCircle, change: '+12%', color: 'text-primary' },
    { label: t('dashboard.stats.aiResponses'), value: '1.2K', icon: Zap, change: '+23%', color: 'text-blue-400' },
    { label: t('dashboard.stats.knowledgeItems'), value: '45', icon: Brain, change: '+8%', color: 'text-purple-400' },
    { label: t('dashboard.stats.networkSize'), value: '23', icon: Users, change: '+15%', color: 'text-green-400' },
  ];

  return (
    <div className="min-h-full bg-background">
      <div className="mobile-container mobile-section">
        
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-6"
        >
          <h1 className="heading-xl mb-2">
            {t('dashboard.welcomeBack')}
          </h1>
          <p className="body-lg text-muted-foreground">
            {t('dashboard.readyToChat')}
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mobile-grid-2 mb-8"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="glass-card p-4">
                <CardContent className="p-0">
                  <div className="flex items-center justify-between">
                    <div className="min-w-0 flex-1">
                      <p className="body-sm text-muted-foreground truncate">{stat.label}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <p className="heading-md truncate">{stat.value}</p>
                        <span className="body-sm text-green-400 font-medium flex items-center">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          {stat.change}
                        </span>
                      </div>
                    </div>
                    <div className={`w-10 h-10 rounded-2xl bg-primary/10 flex items-center justify-center ${stat.color}`}>
                      <stat.icon className="w-5 h-5" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="heading-lg">{t('dashboard.quickActions')}</h2>
            <Button variant="ghost" size="sm" className="glass-button">
              <Plus className="w-4 h-4 mr-2" />
              {t('dashboard.more')}
            </Button>
          </div>
          
          <div className="mobile-grid">
            {quickActions.map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card className="glass-card cursor-pointer hover:glass-modal transition-all duration-300" onClick={action.action}>
                  <CardContent className="p-6">
                    <div className={`w-12 h-12 rounded-2xl bg-gradient-to-br ${action.gradient} flex items-center justify-center mb-4`}>
                      <action.icon className="w-6 h-6 text-primary" />
                    </div>
                    <h3 className="heading-sm mb-2 text-foreground">
                      {action.title}
                    </h3>
                    <p className="body-sm text-muted-foreground">
                      {action.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Chat Interface */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Card className="glass-card h-96">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="heading-md flex items-center gap-2">
                    <MessageCircle className="w-5 h-5 text-primary" />
                    {t('dashboard.aiAssistant')}
                  </CardTitle>
                  <CardDescription className="body-sm">
                    {t('dashboard.chatWithAI')}
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm" className="glass-button">
                  <Plus className="w-4 h-4 mr-2" />
                  {t('dashboard.new')}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0 flex-1 overflow-hidden">
              <div className="h-72">
                <ChatInterface />
              </div>
            </CardContent>
          </Card>
        </motion.div>

      </div>
    </div>
  );
}
