
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const Index = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    const userData = localStorage.getItem('user');
    if (userData) {
      navigate('/topics');
    } else {
      navigate('/login');
    }
  }, [navigate]);

  return (
    <div className="min-h-screen mobile-full-screen flex items-center justify-center bg-background">
      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-lime-500"></div>
    </div>
  );
};

export default Index;
