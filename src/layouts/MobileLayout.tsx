
import { Outlet } from 'react-router-dom';
import { MobileHeader } from '@/components/MobileHeader';
import { MobileBottomNav } from '@/components/MobileBottomNav';
import { useEffect, useState } from 'react';

export default function MobileLayout() {
  const [isScrollingDown, setIsScrollingDown] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsScrollingDown(true);
      } else {
        setIsScrollingDown(false);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  return (
    <div className="min-h-screen flex flex-col bg-background mobile-full-screen">
      <MobileHeader />
      
      <main className="flex-1 pb-20 safe-area-bottom overflow-x-hidden">
        <div className="min-h-full">
          <Outlet />
        </div>
      </main>
      
      <MobileBottomNav isHidden={isScrollingDown} />
    </div>
  );
}
