
import { ReactNode, useEffect, useState } from 'react';
import { MobileHeader } from '@/components/MobileHeader';
import { MobileBottomNav } from '@/components/MobileBottomNav';
import { TikTokBackground } from '@/components/TikTokBackground';

interface DashboardLayoutProps {
  children: ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [isScrollingDown, setIsScrollingDown] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsScrollingDown(true);
      } else {
        setIsScrollingDown(false);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  return (
    <div className="min-h-screen flex flex-col bg-background mobile-full-screen relative">
      {/* TikTok-style animated background */}
      <TikTokBackground />
      
      <MobileHeader />
      
      <main className="flex-1 pb-20 safe-area-bottom overflow-x-hidden relative">
        <div className="min-h-full">
          {children}
        </div>
      </main>
      
      <MobileBottomNav isHidden={isScrollingDown} />
    </div>
  );
}
