
declare namespace JSX {
  interface IntrinsicElements {
    'model-viewer': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
      src?: string;
      alt?: string;
      'camera-controls'?: boolean;
      'auto-rotate'?: boolean;
      ar?: boolean;
      loading?: string;
      reveal?: string;
      poster?: string;
      'power-preference'?: string;
      style?: React.CSSProperties;
    };
  }
}
