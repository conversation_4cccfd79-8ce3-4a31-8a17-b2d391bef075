// Enhanced Authentication Hook
// Advanced authentication with MFA, security monitoring, and session management

import { useState, useEffect, useCallback, useRef } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface AuthState {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  sessionId: string | null;
  lastActivity: Date | null;
  authMethod: AuthMethod | null;
  securityLevel: SecurityLevel;
  requiresMFA: boolean;
  mfaVerified: boolean;
  deviceTrusted: boolean;
  sessionExpiry: Date | null;
}

interface MFAChallenge {
  id: string;
  type: MFAType;
  expiresAt: Date;
  attempts: number;
  maxAttempts: number;
}

interface SecurityEvent {
  type: SecurityEventType;
  timestamp: Date;
  details: Record<string, unknown>;
  riskLevel: 'low' | 'medium' | 'high';
}

interface DeviceInfo {
  id: string;
  name: string;
  type: 'mobile' | 'desktop' | 'tablet';
  browser: string;
  os: string;
  fingerprint: string;
  trusted: boolean;
  lastUsed: Date;
  location?: {
    country: string;
    city: string;
    ip: string;
  };
}

interface SessionInfo {
  id: string;
  userId: string;
  deviceId: string;
  createdAt: Date;
  expiresAt: Date;
  lastActivity: Date;
  ipAddress: string;
  userAgent: string;
  status: 'active' | 'expired' | 'revoked';
}

type AuthMethod = 'email' | 'phone' | 'google' | 'github' | 'apple' | 'facebook';
type MFAType = 'totp' | 'sms' | 'email' | 'backup_code';
type SecurityLevel = 'basic' | 'enhanced' | 'maximum';
type SecurityEventType = 
  | 'login_success'
  | 'login_failed'
  | 'mfa_required'
  | 'mfa_success'
  | 'mfa_failed'
  | 'password_changed'
  | 'suspicious_activity'
  | 'device_new'
  | 'location_new'
  | 'session_expired'
  | 'account_locked';

export const useEnhancedAuth = () => {
  const { toast } = useToast();
  const activityTimer = useRef<NodeJS.Timeout>();
  const securityMonitor = useRef<NodeJS.Timeout>();

  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    isAuthenticated: false,
    sessionId: null,
    lastActivity: null,
    authMethod: null,
    securityLevel: 'basic',
    requiresMFA: false,
    mfaVerified: false,
    deviceTrusted: false,
    sessionExpiry: null
  });

  const [mfaChallenge, setMfaChallenge] = useState<MFAChallenge | null>(null);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [activeSessions, setActiveSessions] = useState<SessionInfo[]>([]);

  // Initialize authentication state
  useEffect(() => {
    const initAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Auth initialization error:', error);
          setAuthState(prev => ({ ...prev, loading: false }));
          return;
        }

        if (session?.user) {
          await handleAuthStateChange(session.user, session);
        } else {
          setAuthState(prev => ({ ...prev, loading: false }));
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
        setAuthState(prev => ({ ...prev, loading: false }));
      }
    };

    initAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          await handleAuthStateChange(session.user, session);
        } else if (event === 'SIGNED_OUT') {
          await handleSignOut();
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          await updateSession(session);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
      if (activityTimer.current) clearInterval(activityTimer.current);
      if (securityMonitor.current) clearInterval(securityMonitor.current);
    };
  }, []);

  // Handle authentication state changes
  const handleAuthStateChange = async (user: User, session: any) => {
    try {
      // Get user profile and security settings
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      // Get device information
      const device = await getDeviceInfo();
      
      // Check if MFA is required
      const requiresMFA = await checkMFARequired(user.id, device);
      
      // Get session info
      const sessionInfo = await createOrUpdateSession(user.id, device.id);

      // Log security event
      await logSecurityEvent({
        type: 'login_success',
        timestamp: new Date(),
        details: {
          method: user.app_metadata.provider || 'email',
          device: device.name,
          location: device.location
        },
        riskLevel: 'low'
      });

      setAuthState({
        user,
        loading: false,
        isAuthenticated: true,
        sessionId: sessionInfo.id,
        lastActivity: new Date(),
        authMethod: (user.app_metadata.provider as AuthMethod) || 'email',
        securityLevel: profile?.security_level || 'basic',
        requiresMFA,
        mfaVerified: !requiresMFA,
        deviceTrusted: device.trusted,
        sessionExpiry: sessionInfo.expiresAt
      });

      setDeviceInfo(device);

      // Start activity monitoring
      startActivityMonitoring();
      startSecurityMonitoring();

    } catch (error) {
      console.error('Auth state change error:', error);
      await logSecurityEvent({
        type: 'login_failed',
        timestamp: new Date(),
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        riskLevel: 'medium'
      });
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      // Revoke current session
      if (authState.sessionId) {
        await supabase
          .from('user_sessions')
          .update({ status: 'revoked' })
          .eq('id', authState.sessionId);
      }

      // Log security event
      await logSecurityEvent({
        type: 'session_expired',
        timestamp: new Date(),
        details: { reason: 'user_logout' },
        riskLevel: 'low'
      });

      // Clear timers
      if (activityTimer.current) clearInterval(activityTimer.current);
      if (securityMonitor.current) clearInterval(securityMonitor.current);

      setAuthState({
        user: null,
        loading: false,
        isAuthenticated: false,
        sessionId: null,
        lastActivity: null,
        authMethod: null,
        securityLevel: 'basic',
        requiresMFA: false,
        mfaVerified: false,
        deviceTrusted: false,
        sessionExpiry: null
      });

      setMfaChallenge(null);
      setDeviceInfo(null);
      setActiveSessions([]);

    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  // Sign in with email and password
  const signInWithEmail = useCallback(async (
    email: string, 
    password: string, 
    options?: { rememberDevice?: boolean }
  ) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        await logSecurityEvent({
          type: 'login_failed',
          timestamp: new Date(),
          details: { email, error: error.message },
          riskLevel: 'medium'
        });
        throw error;
      }

      // Handle device trust
      if (options?.rememberDevice && deviceInfo) {
        await trustDevice(deviceInfo.id);
      }

      return data;
    } catch (error) {
      setAuthState(prev => ({ ...prev, loading: false }));
      throw error;
    }
  }, [deviceInfo]);

  // Sign in with OAuth provider
  const signInWithProvider = useCallback(async (
    provider: 'google' | 'github' | 'facebook' | 'apple'
  ) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) {
        setAuthState(prev => ({ ...prev, loading: false }));
        throw error;
      }

      return data;
    } catch (error) {
      setAuthState(prev => ({ ...prev, loading: false }));
      throw error;
    }
  }, []);

  // Sign up with email and password
  const signUp = useCallback(async (
    email: string,
    password: string,
    userData?: Record<string, unknown>
  ) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      });

      if (error) throw error;

      // Create initial profile
      if (data.user) {
        await supabase
          .from('profiles')
          .insert({
            id: data.user.id,
            email: data.user.email,
            security_level: 'basic',
            mfa_enabled: false,
            ...userData
          });
      }

      return data;
    } catch (error) {
      setAuthState(prev => ({ ...prev, loading: false }));
      throw error;
    }
  }, []);

  // Sign out
  const signOut = useCallback(async () => {
    try {
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }, []);

  // Multi-Factor Authentication
  const initiateMFA = useCallback(async (type: MFAType = 'totp') => {
    try {
      if (!authState.user) throw new Error('User not authenticated');

      const challenge: MFAChallenge = {
        id: `mfa_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type,
        expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
        attempts: 0,
        maxAttempts: 3
      };

      setMfaChallenge(challenge);

      // Send MFA challenge based on type
      switch (type) {
        case 'sms':
          await sendSMSChallenge(authState.user.phone || '');
          break;
        case 'email':
          await sendEmailChallenge(authState.user.email || '');
          break;
        case 'totp':
          // TOTP challenges don't need sending, just verification
          break;
      }

      await logSecurityEvent({
        type: 'mfa_required',
        timestamp: new Date(),
        details: { type, challengeId: challenge.id },
        riskLevel: 'low'
      });

      return challenge;
    } catch (error) {
      console.error('MFA initiation error:', error);
      throw error;
    }
  }, [authState.user]);

  // Verify MFA code
  const verifyMFA = useCallback(async (code: string) => {
    try {
      if (!mfaChallenge) throw new Error('No active MFA challenge');
      if (!authState.user) throw new Error('User not authenticated');

      // Check if challenge has expired
      if (new Date() > mfaChallenge.expiresAt) {
        setMfaChallenge(null);
        throw new Error('MFA challenge has expired');
      }

      // Check attempt limits
      if (mfaChallenge.attempts >= mfaChallenge.maxAttempts) {
        setMfaChallenge(null);
        throw new Error('Maximum MFA attempts exceeded');
      }

      // Verify the code based on type
      const isValid = await verifyMFACode(mfaChallenge.type, code, authState.user.id);

      if (isValid) {
        setAuthState(prev => ({ ...prev, mfaVerified: true }));
        setMfaChallenge(null);

        await logSecurityEvent({
          type: 'mfa_success',
          timestamp: new Date(),
          details: { type: mfaChallenge.type },
          riskLevel: 'low'
        });

        toast({
          title: 'MFA Verified',
          description: 'Multi-factor authentication successful'
        });

        return true;
      } else {
        const updatedChallenge = {
          ...mfaChallenge,
          attempts: mfaChallenge.attempts + 1
        };
        setMfaChallenge(updatedChallenge);

        await logSecurityEvent({
          type: 'mfa_failed',
          timestamp: new Date(),
          details: { 
            type: mfaChallenge.type, 
            attempts: updatedChallenge.attempts 
          },
          riskLevel: 'medium'
        });

        throw new Error('Invalid MFA code');
      }
    } catch (error) {
      console.error('MFA verification error:', error);
      throw error;
    }
  }, [mfaChallenge, authState.user, toast]);

  // Session Management
  const getActiveSessions = useCallback(async () => {
    try {
      if (!authState.user) return [];

      const { data: sessions, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', authState.user.id)
        .eq('status', 'active')
        .order('last_activity', { ascending: false });

      if (error) throw error;

      const sessionInfos: SessionInfo[] = sessions?.map(session => ({
        id: session.id,
        userId: session.user_id,
        deviceId: session.device_id,
        createdAt: new Date(session.created_at),
        expiresAt: new Date(session.expires_at),
        lastActivity: new Date(session.last_activity),
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        status: session.status
      })) || [];

      setActiveSessions(sessionInfos);
      return sessionInfos;
    } catch (error) {
      console.error('Get active sessions error:', error);
      return [];
    }
  }, [authState.user]);

  // Revoke session
  const revokeSession = useCallback(async (sessionId: string) => {
    try {
      const { error } = await supabase
        .from('user_sessions')
        .update({ status: 'revoked' })
        .eq('id', sessionId);

      if (error) throw error;

      // If revoking current session, sign out
      if (sessionId === authState.sessionId) {
        await signOut();
      } else {
        // Remove from active sessions
        setActiveSessions(prev => prev.filter(s => s.id !== sessionId));
      }

      toast({
        title: 'Session Revoked',
        description: 'Session has been successfully revoked'
      });

    } catch (error) {
      console.error('Revoke session error:', error);
      throw error;
    }
  }, [authState.sessionId, signOut, toast]);

  // Change password
  const changePassword = useCallback(async (newPassword: string) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;

      await logSecurityEvent({
        type: 'password_changed',
        timestamp: new Date(),
        details: { method: 'user_initiated' },
        riskLevel: 'low'
      });

      toast({
        title: 'Password Changed',
        description: 'Your password has been successfully updated'
      });

    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }, [toast]);

  // Update security level
  const updateSecurityLevel = useCallback(async (level: SecurityLevel) => {
    try {
      if (!authState.user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('profiles')
        .update({ security_level: level })
        .eq('id', authState.user.id);

      if (error) throw error;

      setAuthState(prev => ({ ...prev, securityLevel: level }));

      toast({
        title: 'Security Level Updated',
        description: `Security level changed to ${level}`
      });

    } catch (error) {
      console.error('Update security level error:', error);
      throw error;
    }
  }, [authState.user, toast]);

  // Utility functions
  const getDeviceInfo = async (): Promise<DeviceInfo> => {
    const userAgent = navigator.userAgent;
    const deviceType = /Mobile|Android|iPhone|iPad/.test(userAgent) ? 'mobile' : 
                      /Tablet|iPad/.test(userAgent) ? 'tablet' : 'desktop';
    
    const fingerprint = await generateDeviceFingerprint();
    
    return {
      id: fingerprint,
      name: `${deviceType} - ${getBrowserName(userAgent)}`,
      type: deviceType,
      browser: getBrowserName(userAgent),
      os: getOSName(userAgent),
      fingerprint,
      trusted: false,
      lastUsed: new Date()
    };
  };

  const generateDeviceFingerprint = async (): Promise<string> => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx?.fillText('Device fingerprint', 10, 10);
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|');
    
    return btoa(fingerprint).substring(0, 32);
  };

  const getBrowserName = (userAgent: string): string => {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  };

  const getOSName = (userAgent: string): string => {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  };

  const checkMFARequired = async (userId: string, device: DeviceInfo): Promise<boolean> => {
    // Check user's MFA settings
    const { data: profile } = await supabase
      .from('profiles')
      .select('mfa_enabled, security_level')
      .eq('id', userId)
      .single();

    if (!profile?.mfa_enabled) return false;

    // Check if device is trusted
    const { data: trustedDevice } = await supabase
      .from('trusted_devices')
      .select('id')
      .eq('user_id', userId)
      .eq('device_fingerprint', device.fingerprint)
      .single();

    return !trustedDevice;
  };

  const createOrUpdateSession = async (userId: string, deviceId: string): Promise<SessionInfo> => {
    const sessionData = {
      user_id: userId,
      device_id: deviceId,
      ip_address: await getUserIP(),
      user_agent: navigator.userAgent,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      last_activity: new Date().toISOString(),
      status: 'active'
    };

    const { data: session, error } = await supabase
      .from('user_sessions')
      .insert(sessionData)
      .select()
      .single();

    if (error) throw error;

    return {
      id: session.id,
      userId: session.user_id,
      deviceId: session.device_id,
      createdAt: new Date(session.created_at),
      expiresAt: new Date(session.expires_at),
      lastActivity: new Date(session.last_activity),
      ipAddress: session.ip_address,
      userAgent: session.user_agent,
      status: session.status
    };
  };

  const getUserIP = async (): Promise<string> => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return 'unknown';
    }
  };

  const trustDevice = async (deviceId: string): Promise<void> => {
    if (!authState.user) return;

    await supabase
      .from('trusted_devices')
      .insert({
        user_id: authState.user.id,
        device_fingerprint: deviceId,
        trusted_at: new Date().toISOString()
      });
  };

  const sendSMSChallenge = async (phone: string): Promise<void> => {
    // Implementation would send SMS via Twilio or similar service
    console.log('Sending SMS challenge to:', phone);
  };

  const sendEmailChallenge = async (email: string): Promise<void> => {
    // Implementation would send email via service
    console.log('Sending email challenge to:', email);
  };

  const verifyMFACode = async (type: MFAType, code: string, userId: string): Promise<boolean> => {
    // Implementation would verify code based on type
    // For demo purposes, accept any 6-digit code
    return code.length === 6 && /^\d+$/.test(code);
  };

  const logSecurityEvent = async (event: SecurityEvent): Promise<void> => {
    try {
      await supabase
        .from('security_events')
        .insert({
          user_id: authState.user?.id,
          type: event.type,
          details: event.details,
          risk_level: event.riskLevel,
          timestamp: event.timestamp.toISOString()
        });

      setSecurityEvents(prev => [event, ...prev.slice(0, 99)]); // Keep last 100 events
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  };

  const startActivityMonitoring = (): void => {
    activityTimer.current = setInterval(() => {
      setAuthState(prev => ({ ...prev, lastActivity: new Date() }));
      
      // Update session activity
      if (authState.sessionId) {
        supabase
          .from('user_sessions')
          .update({ last_activity: new Date().toISOString() })
          .eq('id', authState.sessionId)
          .then(() => {})
          .catch(error => console.error('Activity update error:', error));
      }
    }, 60000); // Update every minute
  };

  const startSecurityMonitoring = (): void => {
    securityMonitor.current = setInterval(async () => {
      // Check for suspicious activity patterns
      // This is a simplified example
      const now = new Date();
      const recentEvents = securityEvents.filter(
        event => now.getTime() - event.timestamp.getTime() < 60000 // Last minute
      );

      const failedLogins = recentEvents.filter(event => event.type === 'login_failed').length;
      
      if (failedLogins >= 5) {
        await logSecurityEvent({
          type: 'suspicious_activity',
          timestamp: new Date(),
          details: { failedLogins, timeWindow: '1 minute' },
          riskLevel: 'high'
        });
      }
    }, 30000); // Check every 30 seconds
  };

  const updateSession = async (session: any): Promise<void> => {
    setAuthState(prev => ({
      ...prev,
      sessionExpiry: session.expires_at ? new Date(session.expires_at * 1000) : null
    }));
  };

  return {
    // Auth state
    ...authState,
    
    // MFA state
    mfaChallenge,
    
    // Security state
    securityEvents,
    deviceInfo,
    activeSessions,
    
    // Auth methods
    signInWithEmail,
    signInWithProvider,
    signUp,
    signOut,
    
    // MFA methods
    initiateMFA,
    verifyMFA,
    
    // Session management
    getActiveSessions,
    revokeSession,
    
    // Security methods
    changePassword,
    updateSecurityLevel,
    
    // Utilities
    refreshSecurityEvents: () => getActiveSessions(),
    clearSecurityEvents: () => setSecurityEvents([])
  };
};