
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { languages } from '@/i18n/config';

export function useRTL() {
  const { i18n } = useTranslation();

  useEffect(() => {
    const currentLang = languages[i18n.language as keyof typeof languages] || languages.en;
    const direction = currentLang.dir;
    
    // Set document direction
    document.documentElement.dir = direction;
    document.documentElement.lang = i18n.language;
    
    // Add RTL class to body for styling
    if (direction === 'rtl') {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }
  }, [i18n.language]);

  const isRTL = languages[i18n.language as keyof typeof languages]?.dir === 'rtl';
  
  return { isRTL };
}
