
import { useEffect, useRef } from 'react';
import { useHeaderVisibility } from '@/contexts/HeaderVisibilityContext';

const GLOBAL_HEADER_HEIGHT = 64; // px

export function useHideGlobalHeaderOnScroll(
  headerRef: React.RefObject<HTMLElement>
) {
  const { setGlobalHeaderHidden } = useHeaderVisibility();
  const lastScrollY = useRef(0);
  const scrollDirection = useRef<'up' | 'down'>('down');

  useEffect(() => {
    const handleScroll = () => {
      const currentY = window.scrollY;
      const isScrollingUp = currentY < lastScrollY.current;
      
      // Update scroll direction
      scrollDirection.current = isScrollingUp ? 'up' : 'down';

      if (headerRef.current) {
        const { top } = headerRef.current.getBoundingClientRect();
        
        // When scrolling up and page header is visible, pin it to top and hide global header
        if (isScrollingUp && currentY > 100) {
          setGlobalHeaderHidden(true);
          headerRef.current.style.position = 'fixed';
          headerRef.current.style.top = '0';
          headerRef.current.style.left = '0';
          headerRef.current.style.right = '0';
          headerRef.current.style.zIndex = '60';
          headerRef.current.style.transform = 'translateY(0)';
        } 
        // When scrolling down or at top, restore normal behavior
        else if (!isScrollingUp || currentY <= 100) {
          setGlobalHeaderHidden(false);
          headerRef.current.style.position = 'sticky';
          headerRef.current.style.top = '';
          headerRef.current.style.left = '';
          headerRef.current.style.right = '';
          headerRef.current.style.zIndex = '50';
          headerRef.current.style.transform = '';
        }
      }

      lastScrollY.current = currentY;
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      setGlobalHeaderHidden(false);
      
      // Reset header styles on cleanup
      if (headerRef.current) {
        headerRef.current.style.position = '';
        headerRef.current.style.top = '';
        headerRef.current.style.left = '';
        headerRef.current.style.right = '';
        headerRef.current.style.zIndex = '';
        headerRef.current.style.transform = '';
      }
    };
  }, [headerRef, setGlobalHeaderHidden]);
}
