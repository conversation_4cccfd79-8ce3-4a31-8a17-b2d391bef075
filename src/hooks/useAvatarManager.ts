
import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';

interface AvatarMetadata {
  createdAt: string;
  fileSize?: number;
  version?: string;
}

interface AvatarData {
  url: string;
  metadata?: AvatarMetadata;
}

export function useAvatarManager() {
  const [avatarData, setAvatarData] = useState<AvatarData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { t } = useTranslation();

  // Rate limiting: max 5 avatar creations per hour
  const checkRateLimit = useCallback((): boolean => {
    const RATE_LIMIT_KEY = 'avatar_creation_timestamps';
    const HOUR_IN_MS = 60 * 60 * 1000;
    const MAX_CREATIONS = 5;

    try {
      const timestamps = JSON.parse(localStorage.getItem(RATE_LIMIT_KEY) || '[]');
      const now = Date.now();
      
      // Filter timestamps from the last hour
      const recentTimestamps = timestamps.filter((ts: number) => now - ts < HOUR_IN_MS);
      
      if (recentTimestamps.length >= MAX_CREATIONS) {
        const oldestTimestamp = Math.min(...recentTimestamps);
        const timeUntilReset = Math.ceil((oldestTimestamp + HOUR_IN_MS - now) / (60 * 1000));
        
        toast({
          title: t('toasts.avatarRateLimit'),
          description: `You can create a new avatar in ${timeUntilReset} minutes.`,
          variant: 'destructive',
        });
        return false;
      }

      // Add current timestamp and save
      recentTimestamps.push(now);
      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify(recentTimestamps));
      return true;
    } catch (error) {
      console.error('Rate limit check failed:', error);
      return true; // Allow creation if rate limit check fails
    }
  }, [toast]);

  // Validate GLB URL
  const validateAvatarUrl = useCallback((url: string): boolean => {
    if (!url) return false;
    
    try {
      const urlObj = new URL(url);
      return urlObj.hostname === 'models.readyplayer.me' && 
             url.endsWith('.glb') &&
             url.includes('/v1/avatars/');
    } catch {
      return false;
    }
  }, []);

  // Load avatar from localStorage
  const loadStoredAvatar = useCallback(() => {
    try {
      const storedUrl = localStorage.getItem('userAvatarUrl');
      const storedMetadata = localStorage.getItem('userAvatarMetadata');
      
      if (storedUrl && validateAvatarUrl(storedUrl)) {
        const metadata = storedMetadata ? JSON.parse(storedMetadata) : undefined;
        setAvatarData({ url: storedUrl, metadata });
        return { url: storedUrl, metadata };
      }
    } catch (error) {
      console.error('Failed to load stored avatar:', error);
    }
    return null;
  }, [validateAvatarUrl]);

  // Save avatar
  const saveAvatar = useCallback(async (url: string): Promise<boolean> => {
    if (!checkRateLimit()) {
      return false;
    }

    if (!validateAvatarUrl(url)) {
      setError('Invalid avatar URL format');
      toast({
        title: t('toasts.avatarInvalid'),
        description: t('toasts.avatarInvalid'),
        variant: 'destructive',
      });
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Test if the GLB file is accessible
      const response = await fetch(url, { method: 'HEAD' });
      
      if (!response.ok) {
        throw new Error('Avatar file is not accessible');
      }

      const metadata: AvatarMetadata = {
        createdAt: new Date().toISOString(),
        fileSize: parseInt(response.headers.get('content-length') || '0'),
        version: '1.0'
      };

      // Save to localStorage (will be replaced with Supabase later)
      localStorage.setItem('userAvatarUrl', url);
      localStorage.setItem('userAvatarMetadata', JSON.stringify(metadata));

      setAvatarData({ url, metadata });

      toast({
        title: t('toasts.avatarSaved'),
        description: t('toasts.avatarSaved'),
      });

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save avatar';
      setError(errorMessage);
      
      toast({
        title: t('toasts.avatarSaveFailed'),
        description: errorMessage,
        variant: 'destructive',
      });
      
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [checkRateLimit, validateAvatarUrl, toast]);

  // Delete avatar
  const deleteAvatar = useCallback(() => {
    try {
      localStorage.removeItem('userAvatarUrl');
      localStorage.removeItem('userAvatarMetadata');
      setAvatarData(null);
      setError(null);
      
      toast({
        title: t('toasts.avatarDeleted'),
        description: t('toasts.avatarDeletedDesc'),
      });
    } catch (error) {
      console.error('Failed to delete avatar:', error);
    }
  }, [toast]);

  // Initialize on mount
  useEffect(() => {
    loadStoredAvatar();
  }, [loadStoredAvatar]);

  return {
    avatarData,
    isLoading,
    error,
    saveAvatar,
    deleteAvatar,
    loadStoredAvatar,
    validateAvatarUrl,
    hasAvatar: !!avatarData?.url,
  };
}
