export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      avatars: {
        Row: {
          avatar_url: string
          created_at: string | null
          id: string
          is_active: boolean | null
          metadata: Json | null
          name: string | null
          rpm_avatar_id: string | null
          thumbnail_url: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          avatar_url: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          metadata?: Json | null
          name?: string | null
          rpm_avatar_id?: string | null
          thumbnail_url?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          avatar_url?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          metadata?: Json | null
          name?: string | null
          rpm_avatar_id?: string | null
          thumbnail_url?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "avatars_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_messages: {
        Row: {
          chat_id: string | null
          content: string
          created_at: string | null
          id: string
          message_type: string | null
          metadata: Json | null
          sender_id: string | null
          sender_type: string | null
        }
        Insert: {
          chat_id?: string | null
          content: string
          created_at?: string | null
          id?: string
          message_type?: string | null
          metadata?: Json | null
          sender_id?: string | null
          sender_type?: string | null
        }
        Update: {
          chat_id?: string | null
          content?: string
          created_at?: string | null
          id?: string
          message_type?: string | null
          metadata?: Json | null
          sender_id?: string | null
          sender_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chat_messages_chat_id_fkey"
            columns: ["chat_id"]
            isOneToOne: false
            referencedRelation: "chats"
            referencedColumns: ["id"]
          },
        ]
      }
      chats: {
        Row: {
          created_at: string | null
          ended_at: string | null
          id: string
          metadata: Json | null
          session_id: string | null
          started_at: string | null
          status: Database["public"]["Enums"]["chat_status"] | null
          total_messages: number | null
          user_id: string | null
          visitor_id: string | null
        }
        Insert: {
          created_at?: string | null
          ended_at?: string | null
          id?: string
          metadata?: Json | null
          session_id?: string | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["chat_status"] | null
          total_messages?: number | null
          user_id?: string | null
          visitor_id?: string | null
        }
        Update: {
          created_at?: string | null
          ended_at?: string | null
          id?: string
          metadata?: Json | null
          session_id?: string | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["chat_status"] | null
          total_messages?: number | null
          user_id?: string | null
          visitor_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chats_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chats_visitor_id_fkey"
            columns: ["visitor_id"]
            isOneToOne: false
            referencedRelation: "visitors"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_analytics: {
        Row: {
          ai_performance_score: number | null
          avg_response_time_ms: number | null
          chat_id: string | null
          conversation_duration_minutes: number | null
          created_at: string | null
          engagement_score: number | null
          id: string
          language_detected: string | null
          message_count: number | null
          sentiment_score: number | null
          topics_discussed: string[] | null
          user_id: string | null
          user_satisfaction: number | null
        }
        Insert: {
          ai_performance_score?: number | null
          avg_response_time_ms?: number | null
          chat_id?: string | null
          conversation_duration_minutes?: number | null
          created_at?: string | null
          engagement_score?: number | null
          id?: string
          language_detected?: string | null
          message_count?: number | null
          sentiment_score?: number | null
          topics_discussed?: string[] | null
          user_id?: string | null
          user_satisfaction?: number | null
        }
        Update: {
          ai_performance_score?: number | null
          avg_response_time_ms?: number | null
          chat_id?: string | null
          conversation_duration_minutes?: number | null
          created_at?: string | null
          engagement_score?: number | null
          id?: string
          language_detected?: string | null
          message_count?: number | null
          sentiment_score?: number | null
          topics_discussed?: string[] | null
          user_id?: string | null
          user_satisfaction?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "chat_analytics_chat_id_fkey"
            columns: ["chat_id"]
            isOneToOne: false
            referencedRelation: "chats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_analytics_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      notifications: {
        Row: {
          action_url: string | null
          created_at: string | null
          data: Json | null
          expires_at: string | null
          id: string
          is_important: boolean | null
          is_read: boolean | null
          message: string
          title: string
          type: string
          user_id: string | null
        }
        Insert: {
          action_url?: string | null
          created_at?: string | null
          data?: Json | null
          expires_at?: string | null
          id?: string
          is_important?: boolean | null
          is_read?: boolean | null
          message: string
          title: string
          type: string
          user_id?: string | null
        }
        Update: {
          action_url?: string | null
          created_at?: string | null
          data?: Json | null
          expires_at?: string | null
          id?: string
          is_important?: boolean | null
          is_read?: boolean | null
          message?: string
          title?: string
          type?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      page_analytics: {
        Row: {
          created_at: string | null
          duration_seconds: number | null
          id: string
          interaction_data: Json | null
          interaction_type: string | null
          page_path: string | null
          profile_id: string | null
          referrer_url: string | null
          session_id: string | null
          user_agent: string | null
          visitor_city: string | null
          visitor_country: string | null
          visitor_ip: string | null
        }
        Insert: {
          created_at?: string | null
          duration_seconds?: number | null
          id?: string
          interaction_data?: Json | null
          interaction_type?: string | null
          page_path?: string | null
          profile_id?: string | null
          referrer_url?: string | null
          session_id?: string | null
          user_agent?: string | null
          visitor_city?: string | null
          visitor_country?: string | null
          visitor_ip?: string | null
        }
        Update: {
          created_at?: string | null
          duration_seconds?: number | null
          id?: string
          interaction_data?: Json | null
          interaction_type?: string | null
          page_path?: string | null
          profile_id?: string | null
          referrer_url?: string | null
          session_id?: string | null
          user_agent?: string | null
          visitor_city?: string | null
          visitor_country?: string | null
          visitor_ip?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "page_analytics_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      profiles: {
        Row: {
          avatar_name: string | null
          avatar_thumbnail: string | null
          avatar_url: string | null
          bio: string | null
          created_at: string | null
          custom_theme: Json | null
          display_name: string | null
          id: string
          is_public: boolean | null
          page_views: number | null
          referral_code: string | null
          referral_points: number | null
          role: Database["public"]["Enums"]["user_role"] | null
          social_links: Json | null
          total_referrals: number | null
          updated_at: string | null
          username: string | null
          verification_status: string | null
        }
        Insert: {
          avatar_name?: string | null
          avatar_thumbnail?: string | null
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          custom_theme?: Json | null
          display_name?: string | null
          id: string
          is_public?: boolean | null
          page_views?: number | null
          referral_code?: string | null
          referral_points?: number | null
          role?: Database["public"]["Enums"]["user_role"] | null
          social_links?: Json | null
          total_referrals?: number | null
          updated_at?: string | null
          username?: string | null
          verification_status?: string | null
        }
        Update: {
          avatar_name?: string | null
          avatar_thumbnail?: string | null
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          custom_theme?: Json | null
          display_name?: string | null
          id?: string
          is_public?: boolean | null
          page_views?: number | null
          referral_code?: string | null
          referral_points?: number | null
          role?: Database["public"]["Enums"]["user_role"] | null
          social_links?: Json | null
          total_referrals?: number | null
          updated_at?: string | null
          username?: string | null
          verification_status?: string | null
        }
        Relationships: []
      }
      referrals: {
        Row: {
          bonus_points: number | null
          completion_date: string | null
          created_at: string | null
          id: string
          metadata: Json | null
          points_awarded: number | null
          referral_code: string
          referred_id: string | null
          referrer_id: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          bonus_points?: number | null
          completion_date?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          points_awarded?: number | null
          referral_code: string
          referred_id?: string | null
          referrer_id?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          bonus_points?: number | null
          completion_date?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          points_awarded?: number | null
          referral_code?: string
          referred_id?: string | null
          referrer_id?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "referrals_referred_id_fkey"
            columns: ["referred_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referrals_referrer_id_fkey"
            columns: ["referrer_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      referral_notifications: {
        Row: {
          created_at: string | null
          id: string
          is_processed: boolean | null
          milestone_level: number | null
          notification_type: string
          points_earned: number | null
          processed_at: string | null
          referred_id: string | null
          referrer_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_processed?: boolean | null
          milestone_level?: number | null
          notification_type: string
          points_earned?: number | null
          processed_at?: string | null
          referred_id?: string | null
          referrer_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_processed?: boolean | null
          milestone_level?: number | null
          notification_type?: string
          points_earned?: number | null
          processed_at?: string | null
          referred_id?: string | null
          referrer_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "referral_notifications_referred_id_fkey"
            columns: ["referred_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_notifications_referrer_id_fkey"
            columns: ["referrer_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      rooms: {
        Row: {
          category: string | null
          created_at: string | null
          creator_id: string | null
          description: string | null
          download_count: number | null
          glb_url: string
          id: string
          is_active: boolean | null
          is_premium: boolean | null
          metadata: Json | null
          name: string
          popularity_score: number | null
          rating: number | null
          tags: string[] | null
          thumbnail_url: string | null
          updated_at: string | null
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          creator_id?: string | null
          description?: string | null
          download_count?: number | null
          glb_url: string
          id?: string
          is_active?: boolean | null
          is_premium?: boolean | null
          metadata?: Json | null
          name: string
          popularity_score?: number | null
          rating?: number | null
          tags?: string[] | null
          thumbnail_url?: string | null
          updated_at?: string | null
        }
        Update: {
          category?: string | null
          created_at?: string | null
          creator_id?: string | null
          description?: string | null
          download_count?: number | null
          glb_url?: string
          id?: string
          is_active?: boolean | null
          is_premium?: boolean | null
          metadata?: Json | null
          name?: string
          popularity_score?: number | null
          rating?: number | null
          tags?: string[] | null
          thumbnail_url?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "rooms_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      space_visits: {
        Row: {
          created_at: string | null
          duration_minutes: number | null
          entered_at: string | null
          id: string
          interactions: Json | null
          left_at: string | null
          space_id: string | null
          visitor_id: string | null
        }
        Insert: {
          created_at?: string | null
          duration_minutes?: number | null
          entered_at?: string | null
          id?: string
          interactions?: Json | null
          left_at?: string | null
          space_id?: string | null
          visitor_id?: string | null
        }
        Update: {
          created_at?: string | null
          duration_minutes?: number | null
          entered_at?: string | null
          id?: string
          interactions?: Json | null
          left_at?: string | null
          space_id?: string | null
          visitor_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "space_visits_space_id_fkey"
            columns: ["space_id"]
            isOneToOne: false
            referencedRelation: "spaces"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "space_visits_visitor_id_fkey"
            columns: ["visitor_id"]
            isOneToOne: false
            referencedRelation: "visitors"
            referencedColumns: ["id"]
          },
        ]
      }
      spaces: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          is_public: boolean | null
          max_visitors: number | null
          name: string
          settings: Json | null
          space_url: string | null
          updated_at: string | null
          user_id: string | null
          verse_engine_space_id: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          max_visitors?: number | null
          name: string
          settings?: Json | null
          space_url?: string | null
          updated_at?: string | null
          user_id?: string | null
          verse_engine_space_id?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          max_visitors?: number | null
          name?: string
          settings?: Json | null
          space_url?: string | null
          updated_at?: string | null
          user_id?: string | null
          verse_engine_space_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "spaces_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      subscriptions: {
        Row: {
          amount: number | null
          created_at: string | null
          currency: string | null
          end_date: string | null
          id: string
          metadata: Json | null
          payment_method: Database["public"]["Enums"]["payment_method"] | null
          plan_type: Database["public"]["Enums"]["plan_type"] | null
          start_date: string | null
          status: Database["public"]["Enums"]["subscription_status"] | null
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          amount?: number | null
          created_at?: string | null
          currency?: string | null
          end_date?: string | null
          id?: string
          metadata?: Json | null
          payment_method?: Database["public"]["Enums"]["payment_method"] | null
          plan_type?: Database["public"]["Enums"]["plan_type"] | null
          start_date?: string | null
          status?: Database["public"]["Enums"]["subscription_status"] | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          amount?: number | null
          created_at?: string | null
          currency?: string | null
          end_date?: string | null
          id?: string
          metadata?: Json | null
          payment_method?: Database["public"]["Enums"]["payment_method"] | null
          plan_type?: Database["public"]["Enums"]["plan_type"] | null
          start_date?: string | null
          status?: Database["public"]["Enums"]["subscription_status"] | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      topics: {
        Row: {
          chat_id: string | null
          confidence: number | null
          created_at: string | null
          id: string
          keywords: string[] | null
          metadata: Json | null
          sentiment: string | null
          summary: string | null
          topic: string
          user_id: string | null
        }
        Insert: {
          chat_id?: string | null
          confidence?: number | null
          created_at?: string | null
          id?: string
          keywords?: string[] | null
          metadata?: Json | null
          sentiment?: string | null
          summary?: string | null
          topic: string
          user_id?: string | null
        }
        Update: {
          chat_id?: string | null
          confidence?: number | null
          created_at?: string | null
          id?: string
          keywords?: string[] | null
          metadata?: Json | null
          sentiment?: string | null
          summary?: string | null
          topic?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "topics_chat_id_fkey"
            columns: ["chat_id"]
            isOneToOne: false
            referencedRelation: "chats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "topics_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_rooms: {
        Row: {
          created_at: string | null
          id: string
          is_default: boolean | null
          last_used_at: string | null
          position: Json | null
          room_id: string | null
          rotation: Json | null
          scale: number | null
          usage_count: number | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_default?: boolean | null
          last_used_at?: string | null
          position?: Json | null
          room_id?: string | null
          rotation?: Json | null
          scale?: number | null
          usage_count?: number | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_default?: boolean | null
          last_used_at?: string | null
          position?: Json | null
          room_id?: string | null
          rotation?: Json | null
          scale?: number | null
          usage_count?: number | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_rooms_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_rooms_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      user_settings: {
        Row: {
          ai_personality: Json | null
          chat_settings: Json | null
          created_at: string | null
          id: string
          notification_settings: Json | null
          privacy_settings: Json | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          ai_personality?: Json | null
          chat_settings?: Json | null
          created_at?: string | null
          id?: string
          notification_settings?: Json | null
          privacy_settings?: Json | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          ai_personality?: Json | null
          chat_settings?: Json | null
          created_at?: string | null
          id?: string
          notification_settings?: Json | null
          privacy_settings?: Json | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_settings_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      visitors: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          display_name: string | null
          email: string | null
          first_visit_at: string | null
          id: string
          last_visit_at: string | null
          metadata: Json | null
          profile_owner_id: string | null
          total_visits: number | null
          user_id: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          display_name?: string | null
          email?: string | null
          first_visit_at?: string | null
          id?: string
          last_visit_at?: string | null
          metadata?: Json | null
          profile_owner_id?: string | null
          total_visits?: number | null
          user_id?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          display_name?: string | null
          email?: string | null
          first_visit_at?: string | null
          id?: string
          last_visit_at?: string | null
          metadata?: Json | null
          profile_owner_id?: string | null
          total_visits?: number | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "visitors_profile_owner_id_fkey"
            columns: ["profile_owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      chat_status: "active" | "archived" | "deleted"
      payment_method: "stripe" | "crypto"
      plan_type: "basic" | "premium" | "enterprise"
      subscription_status: "active" | "inactive" | "cancelled" | "past_due"
      user_role: "user" | "visitor" | "admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      chat_status: ["active", "archived", "deleted"],
      payment_method: ["stripe", "crypto"],
      plan_type: ["basic", "premium", "enterprise"],
      subscription_status: ["active", "inactive", "cancelled", "past_due"],
      user_role: ["user", "visitor", "admin"],
    },
  },
} as const
