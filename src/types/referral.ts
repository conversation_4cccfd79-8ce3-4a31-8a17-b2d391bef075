// Referral Types
// TypeScript interfaces for referral system

export interface Referral {
  id: string;
  referrer_id: string;
  referred_user_id?: string;
  referral_code: string;
  status: ReferralStatus;
  email?: string;
  signup_date?: string;
  conversion_date?: string;
  reward_earned: number;
  commission_earned: number;
  tier_level: number;
  metadata?: ReferralMetadata;
  expires_at?: string;
  created_at: string;
  updated_at?: string;
}

export interface ReferralProgram {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  reward_structure: RewardStructure;
  commission_structure: CommissionStructure;
  tier_system: TierSystem;
  terms_and_conditions: string;
  min_payout_amount: number;
  payout_currency: string;
  tracking_duration_days: number;
  auto_approve_rewards: boolean;
  referral_link_template: string;
  created_at: string;
  updated_at?: string;
}

export interface RewardStructure {
  type: RewardType;
  amount: number;
  currency?: string;
  percentage?: number;
  bonus_tiers: BonusTier[];
  conditions: RewardCondition[];
}

export interface CommissionStructure {
  base_percentage: number;
  tier_bonuses: TierBonus[];
  lifetime_commission: boolean;
  commission_cap?: number;
  minimum_order_value?: number;
}

export interface TierSystem {
  tiers: ReferralTier[];
  qualification_period_days: number;
  reset_period: 'monthly' | 'quarterly' | 'yearly' | 'never';
}

export interface ReferralTier {
  level: number;
  name: string;
  requirements: TierRequirement[];
  benefits: TierBenefit[];
  badge_icon?: string;
  badge_color?: string;
}

export interface TierRequirement {
  type: 'referrals_count' | 'revenue_generated' | 'active_referrals' | 'conversion_rate';
  value: number;
  period_days?: number;
}

export interface TierBenefit {
  type: 'commission_boost' | 'bonus_reward' | 'exclusive_access' | 'priority_support';
  value: number;
  description: string;
}

export interface BonusTier {
  min_referrals: number;
  bonus_multiplier: number;
  description: string;
}

export interface TierBonus {
  tier_level: number;
  bonus_percentage: number;
}

export interface RewardCondition {
  type: ConditionType;
  value: string | number;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
}

export interface ReferralReward {
  id: string;
  referral_id: string;
  referrer_id: string;
  referred_user_id: string;
  type: RewardType;
  amount: number;
  currency: string;
  status: RewardStatus;
  trigger_event: TriggerEvent;
  processed_at?: string;
  paid_out_at?: string;
  payout_method?: PayoutMethod;
  payout_reference?: string;
  metadata?: Record<string, unknown>;
  created_at: string;
}

export interface ReferralCommission {
  id: string;
  referral_id: string;
  referrer_id: string;
  order_id?: string;
  subscription_id?: string;
  amount: number;
  percentage: number;
  currency: string;
  status: CommissionStatus;
  tier_level: number;
  period_start: string;
  period_end: string;
  paid_out_at?: string;
  payout_reference?: string;
  created_at: string;
}

export interface ReferralLink {
  id: string;
  user_id: string;
  referral_code: string;
  url: string;
  campaign?: string;
  source?: string;
  medium?: string;
  is_active: boolean;
  click_count: number;
  conversion_count: number;
  last_clicked_at?: string;
  expires_at?: string;
  created_at: string;
}

export interface ReferralTracking {
  id: string;
  referral_code: string;
  visitor_id: string;
  ip_address?: string;
  user_agent?: string;
  referrer_url?: string;
  landing_page: string;
  utm_params?: UTMParams;
  device_info?: DeviceInfo;
  location_info?: LocationInfo;
  clicked_at: string;
  converted_at?: string;
  conversion_value?: number;
}

export interface ReferralAnalytics {
  id: string;
  user_id: string;
  period_start: string;
  period_end: string;
  total_referrals: number;
  successful_referrals: number;
  pending_referrals: number;
  conversion_rate: number;
  total_rewards_earned: number;
  total_commissions_earned: number;
  total_revenue_generated: number;
  top_performing_links: TopPerformingLink[];
  traffic_sources: TrafficSource[];
  geographic_distribution: GeographicData[];
  device_breakdown: DeviceBreakdown[];
  tier_progression: TierProgression;
  created_at: string;
}

export interface TopPerformingLink {
  link_id: string;
  url: string;
  clicks: number;
  conversions: number;
  conversion_rate: number;
  revenue_generated: number;
}

export interface TrafficSource {
  source: string;
  clicks: number;
  conversions: number;
  percentage: number;
}

export interface GeographicData {
  country: string;
  clicks: number;
  conversions: number;
  revenue: number;
}

export interface DeviceBreakdown {
  device_type: 'mobile' | 'desktop' | 'tablet';
  clicks: number;
  conversions: number;
  percentage: number;
}

export interface TierProgression {
  current_tier: number;
  progress_to_next: number;
  requirements_met: string[];
  requirements_pending: string[];
}

export interface ReferralLeaderboard {
  id: string;
  period_start: string;
  period_end: string;
  category: LeaderboardCategory;
  entries: LeaderboardEntry[];
  created_at: string;
}

export interface LeaderboardEntry {
  rank: number;
  user_id: string;
  username: string;
  avatar_url?: string;
  value: number;
  tier_level: number;
  badge?: string;
}

export interface ReferralNotification {
  id: string;
  user_id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, unknown>;
  is_read: boolean;
  priority: 'low' | 'medium' | 'high';
  action_url?: string;
  action_text?: string;
  expires_at?: string;
  created_at: string;
}

export interface ReferralPayout {
  id: string;
  user_id: string;
  amount: number;
  currency: string;
  method: PayoutMethod;
  status: PayoutStatus;
  fee_amount?: number;
  net_amount: number;
  reference_id?: string;
  transaction_id?: string;
  processed_at?: string;
  failed_reason?: string;
  retry_count: number;
  metadata?: Record<string, unknown>;
  created_at: string;
}

// Supporting Interfaces

export interface ReferralMetadata {
  source?: string;
  campaign?: string;
  referrer_url?: string;
  utm_params?: UTMParams;
  device_info?: DeviceInfo;
  location_info?: LocationInfo;
  custom_fields?: Record<string, unknown>;
}

export interface UTMParams {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
}

export interface DeviceInfo {
  type: 'mobile' | 'desktop' | 'tablet';
  os?: string;
  browser?: string;
  screen_resolution?: string;
}

export interface LocationInfo {
  country?: string;
  state?: string;
  city?: string;
  timezone?: string;
  latitude?: number;
  longitude?: number;
}

// Enums and Union Types

export type ReferralStatus = 
  | 'pending'
  | 'active'
  | 'converted'
  | 'expired'
  | 'cancelled'
  | 'fraudulent';

export type RewardType = 
  | 'cash'
  | 'credit'
  | 'discount'
  | 'free_trial'
  | 'upgrade'
  | 'points'
  | 'gift_card';

export type RewardStatus = 
  | 'pending'
  | 'approved'
  | 'paid'
  | 'rejected'
  | 'expired';

export type CommissionStatus = 
  | 'pending'
  | 'earned'
  | 'paid'
  | 'cancelled'
  | 'disputed';

export type TriggerEvent = 
  | 'signup'
  | 'first_purchase'
  | 'subscription'
  | 'milestone_reached'
  | 'upgrade'
  | 'renewal';

export type PayoutMethod = 
  | 'bank_transfer'
  | 'paypal'
  | 'stripe'
  | 'crypto'
  | 'gift_card'
  | 'account_credit';

export type PayoutStatus = 
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled';

export type ConditionType = 
  | 'signup_completed'
  | 'first_purchase'
  | 'subscription_active'
  | 'minimum_spend'
  | 'trial_converted'
  | 'custom_event';

export type LeaderboardCategory = 
  | 'total_referrals'
  | 'total_revenue'
  | 'conversion_rate'
  | 'monthly_referrals'
  | 'tier_level';

export type NotificationType = 
  | 'referral_signup'
  | 'referral_conversion'
  | 'reward_earned'
  | 'commission_earned'
  | 'tier_upgraded'
  | 'payout_processed'
  | 'milestone_reached'
  | 'program_update';

// API Response Types

export interface ReferralAPIResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

export interface ReferralDashboardResponse extends ReferralAPIResponse<{
  summary: ReferralSummary;
  recent_activity: ReferralActivity[];
  performance_metrics: PerformanceMetrics;
  tier_info: TierInfo;
}> {}

export interface ReferralSummary {
  total_referrals: number;
  successful_referrals: number;
  pending_referrals: number;
  conversion_rate: number;
  total_earnings: number;
  pending_earnings: number;
  current_tier: number;
  next_tier_progress: number;
}

export interface ReferralActivity {
  id: string;
  type: 'referral' | 'reward' | 'commission' | 'tier_change';
  description: string;
  amount?: number;
  currency?: string;
  timestamp: string;
}

export interface PerformanceMetrics {
  clicks_this_month: number;
  conversions_this_month: number;
  revenue_this_month: number;
  top_performing_link: TopPerformingLink;
  trending_sources: TrafficSource[];
}

export interface TierInfo {
  current_tier: ReferralTier;
  next_tier?: ReferralTier;
  progress_percentage: number;
  requirements_status: Array<{
    requirement: TierRequirement;
    current_value: number;
    is_met: boolean;
  }>;
}

// Event Types

export interface ReferralEvent {
  type: ReferralEventType;
  referral_id?: string;
  user_id?: string;
  data?: Record<string, unknown>;
  timestamp: string;
}

export type ReferralEventType = 
  | 'referral_created'
  | 'referral_clicked'
  | 'referral_converted'
  | 'reward_earned'
  | 'commission_calculated'
  | 'tier_changed'
  | 'payout_requested'
  | 'fraud_detected';

// Validation Types

export interface ReferralValidationRules {
  referral_code: {
    min_length: number;
    max_length: number;
    allowed_chars: string;
    case_sensitive: boolean;
  };
  rewards: {
    min_amount: number;
    max_amount: number;
    auto_approval_threshold: number;
  };
  fraud_detection: {
    max_same_ip_referrals: number;
    min_time_between_referrals: number;
    suspicious_patterns: string[];
  };
  payout: {
    min_payout_amount: number;
    max_payout_amount: number;
    payout_frequency_days: number;
  };
}

export interface ReferralValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fraud_score?: number;
  recommended_actions?: string[];
}

// Error Types

export class ReferralError extends Error {
  code: string;
  referral_id?: string;
  details?: Record<string, unknown>;

  constructor(message: string, code: string, referral_id?: string, details?: Record<string, unknown>) {
    super(message);
    this.name = 'ReferralError';
    this.code = code;
    this.referral_id = referral_id;
    this.details = details;
  }
}

export class ReferralCodeError extends ReferralError {
  constructor(message: string, code?: string) {
    super(message, 'REFERRAL_CODE_ERROR', undefined, { code });
    this.name = 'ReferralCodeError';
  }
}

export class FraudDetectionError extends ReferralError {
  fraud_score: number;
  detection_rules: string[];

  constructor(message: string, fraud_score: number, detection_rules: string[], referral_id?: string) {
    super(message, 'FRAUD_DETECTED', referral_id, { fraud_score, detection_rules });
    this.name = 'FraudDetectionError';
    this.fraud_score = fraud_score;
    this.detection_rules = detection_rules;
  }
}

// Utility Types

export interface ReferralConfig {
  program: ReferralProgram;
  validation_rules: ReferralValidationRules;
  fraud_detection_enabled: boolean;
  auto_approval_enabled: boolean;
  notification_settings: {
    email_notifications: boolean;
    push_notifications: boolean;
    webhook_url?: string;
  };
  integration_settings: {
    google_analytics_enabled: boolean;
    facebook_pixel_enabled: boolean;
    custom_tracking_enabled: boolean;
  };
}

export interface ReferralCodeGenerator {
  generate: () => string;
  validate: (code: string) => boolean;
  isUnique: (code: string) => Promise<boolean>;
}

export interface FraudDetector {
  analyze: (referral: Partial<Referral>) => Promise<{ score: number; reasons: string[] }>;
  markFraudulent: (referralId: string, reason: string) => Promise<void>;
  whitelist: (userId: string) => Promise<void>;
}

// Export utility functions type
export interface ReferralUtils {
  generateReferralCode: (userId: string, length?: number) => string;
  validateReferralCode: (code: string) => ReferralValidationResult;
  calculateCommission: (amount: number, tierLevel: number, structure: CommissionStructure) => number;
  calculateReward: (triggerEvent: TriggerEvent, structure: RewardStructure) => number;
  buildReferralUrl: (code: string, campaign?: string) => string;
  trackConversion: (referralCode: string, userId: string, value?: number) => Promise<void>;
  detectFraud: (referral: Partial<Referral>) => Promise<{ isFraudulent: boolean; score: number }>;
  processPayouts: (userId: string) => Promise<void>;
}