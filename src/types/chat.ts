// Chat System Types
// TypeScript interfaces for AI-powered chat functionality

export interface ChatMessage {
  id: string;
  chat_id: string;
  sender_type: 'user' | 'visitor' | 'ai';
  sender_id: string;
  content: string;
  message_type: MessageType;
  metadata?: MessageMetadata;
  created_at: string;
}

export interface ChatSession {
  id: string;
  user_id: string; // Profile owner
  visitor_id: string;
  session_id: string;
  status: ChatStatus;
  started_at: string;
  ended_at?: string;
  total_messages: number;
  metadata?: SessionMetadata;
  created_at: string;
}

export interface ChatAnalytics {
  id: string;
  chat_id: string;
  user_id: string;
  message_count: number;
  avg_response_time_ms?: number;
  conversation_duration_minutes?: number;
  sentiment_score?: number; // -1.0 to 1.0
  engagement_score?: number; // 0.0 to 1.0
  topics_discussed: string[];
  user_satisfaction?: number; // 1-5 rating
  ai_performance_score?: number;
  language_detected?: string;
  created_at: string;
}

export interface ChatTopic {
  id: string;
  chat_id: string;
  user_id: string;
  topic: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number; // 0.0 to 1.0
  keywords: string[];
  summary?: string;
  metadata?: Record<string, unknown>;
  created_at: string;
}

export interface Visitor {
  id: string;
  user_id?: string; // If visitor has an account
  profile_owner_id: string;
  display_name?: string;
  email?: string;
  avatar_url?: string;
  first_visit_at: string;
  last_visit_at: string;
  total_visits: number;
  metadata?: VisitorMetadata;
  created_at: string;
}

export interface AIPersonality {
  tone: PersonalityTone;
  style: PersonalityStyle;
  traits: string[];
  interests: string[];
  background?: string;
  custom_instructions?: string;
  response_length: ResponseLength;
  knowledge_areas: string[];
  conversation_starters: string[];
  forbidden_topics?: string[];
}

export interface AIResponse {
  content: string;
  confidence: number;
  processing_time_ms: number;
  sources?: string[];
  suggestions?: string[];
  metadata?: {
    model_used: string;
    tokens_used: number;
    context_length: number;
    temperature: number;
  };
}

export interface ChatContext {
  recent_messages: ChatMessage[];
  user_profile: {
    name?: string;
    bio?: string;
    interests?: string[];
  };
  conversation_history: {
    topics: string[];
    sentiment_trend: number;
    key_points: string[];
  };
  session_info: {
    duration_minutes: number;
    message_count: number;
    last_interaction: string;
  };
}

// Supporting Interfaces

export interface MessageMetadata {
  edited?: boolean;
  edited_at?: string;
  reply_to?: string;
  attachments?: MessageAttachment[];
  reactions?: MessageReaction[];
  read_by?: Array<{
    user_id: string;
    read_at: string;
  }>;
  ai_context?: {
    prompt_used?: string;
    model_version?: string;
    processing_time?: number;
    confidence_score?: number;
  };
}

export interface SessionMetadata {
  ip_address?: string;
  user_agent?: string;
  referrer?: string;
  location?: {
    country?: string;
    city?: string;
    timezone?: string;
  };
  device_info?: {
    type: 'mobile' | 'desktop' | 'tablet';
    os?: string;
    browser?: string;
  };
  utm_params?: {
    source?: string;
    medium?: string;
    campaign?: string;
  };
}

export interface VisitorMetadata {
  source: 'direct' | 'referral' | 'social' | 'search' | 'email';
  referrer_url?: string;
  utm_params?: Record<string, string>;
  device_type: 'mobile' | 'desktop' | 'tablet';
  browser?: string;
  location?: {
    country?: string;
    city?: string;
  };
  session_duration_avg?: number;
  pages_viewed?: string[];
  interactions_count?: number;
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'file' | 'audio' | 'video';
  url: string;
  filename: string;
  size_bytes: number;
  mime_type: string;
}

export interface MessageReaction {
  emoji: string;
  count: number;
  users: string[];
}

// Enums and Union Types

export type MessageType = 
  | 'text' 
  | 'image' 
  | 'audio' 
  | 'video' 
  | 'file' 
  | 'system' 
  | 'ai_suggestion'
  | 'quick_reply';

export type ChatStatus = 
  | 'active' 
  | 'archived' 
  | 'deleted' 
  | 'paused'
  | 'ended';

export type PersonalityTone = 
  | 'friendly' 
  | 'professional' 
  | 'casual' 
  | 'enthusiastic' 
  | 'calm'
  | 'humorous'
  | 'supportive'
  | 'authoritative';

export type PersonalityStyle = 
  | 'conversational' 
  | 'formal' 
  | 'concise' 
  | 'detailed'
  | 'storytelling'
  | 'educational'
  | 'empathetic';

export type ResponseLength = 
  | 'short' 
  | 'medium' 
  | 'long' 
  | 'adaptive';

export type SentimentType = 
  | 'very_positive' 
  | 'positive' 
  | 'neutral' 
  | 'negative' 
  | 'very_negative';

// API Response Types

export interface ChatAPIResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

export interface ChatListResponse extends ChatAPIResponse<ChatSession[]> {
  filters?: {
    status?: ChatStatus;
    date_from?: string;
    date_to?: string;
  };
}

export interface MessageListResponse extends ChatAPIResponse<ChatMessage[]> {
  chat_info?: {
    id: string;
    status: ChatStatus;
    participant_count: number;
  };
}

// Event Types

export interface ChatEvent {
  type: ChatEventType;
  chat_id: string;
  user_id?: string;
  visitor_id?: string;
  data?: Record<string, unknown>;
  timestamp: string;
}

export type ChatEventType = 
  | 'chat_started'
  | 'chat_ended'
  | 'message_sent'
  | 'message_received'
  | 'typing_start'
  | 'typing_stop'
  | 'user_joined'
  | 'user_left'
  | 'ai_thinking'
  | 'ai_response_ready'
  | 'error_occurred';

// AI Service Types

export interface AIServiceConfig {
  provider: 'gemini' | 'openai' | 'claude' | 'custom';
  model: string;
  api_key: string;
  base_url?: string;
  max_tokens: number;
  temperature: number;
  personality: AIPersonality;
  safety_settings?: AISafetySettings;
  rate_limits?: {
    requests_per_minute: number;
    tokens_per_minute: number;
  };
}

export interface AISafetySettings {
  content_filtering: boolean;
  toxic_language_detection: boolean;
  personal_info_protection: boolean;
  banned_topics: string[];
  escalation_triggers: string[];
}

export interface AIPromptTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  variables: Array<{
    name: string;
    type: 'string' | 'number' | 'array';
    required: boolean;
    description: string;
  }>;
  category: 'conversation' | 'greeting' | 'farewell' | 'error' | 'fallback';
}

// Chat Analytics Types

export interface ConversationInsights {
  total_conversations: number;
  total_messages: number;
  avg_conversation_length: number;
  avg_response_time_minutes: number;
  most_active_hour: number;
  top_topics: Array<{
    topic: string;
    frequency: number;
    sentiment: number;
  }>;
  sentiment_breakdown: {
    positive: number;
    neutral: number;
    negative: number;
  };
  visitor_retention_rate: number;
  ai_performance_metrics: {
    avg_confidence: number;
    avg_response_time: number;
    error_rate: number;
  };
}

export interface TopicAnalytics {
  topic: string;
  mention_count: number;
  avg_sentiment: number;
  avg_confidence: number;
  most_common_keywords: string[];
  latest_mention: string;
  trend: 'increasing' | 'decreasing' | 'stable';
}

// Error Types

export class ChatError extends Error {
  code: string;
  chat_id?: string;
  details?: Record<string, unknown>;

  constructor(message: string, code: string, chat_id?: string, details?: Record<string, unknown>) {
    super(message);
    this.name = 'ChatError';
    this.code = code;
    this.chat_id = chat_id;
    this.details = details;
  }
}

export class AIServiceError extends ChatError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, 'AI_SERVICE_ERROR', undefined, details);
    this.name = 'AIServiceError';
  }
}

export class MessageValidationError extends ChatError {
  validation_errors: string[];

  constructor(message: string, validation_errors: string[], chat_id?: string) {
    super(message, 'MESSAGE_VALIDATION_ERROR', chat_id);
    this.name = 'MessageValidationError';
    this.validation_errors = validation_errors;
  }
}

// Real-time Types

export interface ChatSocketEvent {
  event: string;
  chat_id: string;
  user_id?: string;
  data: unknown;
  timestamp: string;
}

export interface TypingIndicator {
  chat_id: string;
  user_id: string;
  user_name: string;
  is_typing: boolean;
  timestamp: string;
}

export interface OnlineStatus {
  user_id: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  last_seen?: string;
}

// Validation Types

export interface ChatValidationRules {
  message: {
    min_length: number;
    max_length: number;
    allowed_types: MessageType[];
    rate_limit_per_minute: number;
  };
  session: {
    max_duration_hours: number;
    max_messages_per_session: number;
    idle_timeout_minutes: number;
  };
  content: {
    profanity_filter: boolean;
    spam_detection: boolean;
    personal_info_detection: boolean;
  };
}

export interface MessageValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions?: string[];
  filtered_content?: string;
}

// Export utility functions type
export interface ChatUtils {
  formatDuration: (minutes: number) => string;
  calculateSentimentScore: (messages: ChatMessage[]) => number;
  extractTopics: (content: string) => string[];
  validateMessage: (message: Partial<ChatMessage>) => MessageValidationResult;
  generateSessionId: () => string;
}