// Room and 3D Environment Types
// TypeScript interfaces for room system and 3D environments

export interface Room3D {
  id: string;
  name: string;
  description?: string;
  glb_url: string;
  thumbnail_url?: string;
  category: RoomCategory;
  is_premium: boolean;
  is_active: boolean;
  popularity_score: number;
  creator_id?: string;
  download_count: number;
  rating: number;
  tags: string[];
  metadata?: RoomMetadata;
  created_at: string;
  updated_at?: string;
}

export interface UserRoom {
  id: string;
  user_id: string;
  room_id: string;
  is_default: boolean;
  position: Vector3D;
  rotation: Vector3D;
  scale: number;
  last_used_at: string;
  usage_count: number;
  created_at: string;
}

export interface RoomMetadata {
  sketchfab_id?: string;
  author?: string;
  license?: string;
  face_count?: number;
  vertex_count?: number;
  texture_count?: number;
  animation_count?: number;
  file_size?: number;
  published_at?: string;
  source?: 'sketchfab' | 'upload' | 'generated' | 'built_in';
  optimization_level?: 'low' | 'medium' | 'high';
  compatibility?: {
    mobile: boolean;
    desktop: boolean;
    vr: boolean;
  };
  performance_metrics?: {
    load_time_ms?: number;
    render_complexity?: number;
    memory_usage_mb?: number;
  };
}

export interface Vector3D {
  x: number;
  y: number;
  z: number;
}

export interface RoomConfig {
  position: Vector3D;
  rotation: Vector3D;
  scale: number;
  lighting?: {
    ambient: number;
    directional: number;
    shadows: boolean;
  };
  environment?: {
    fog?: {
      enabled: boolean;
      color: string;
      near: number;
      far: number;
    };
    skybox?: string;
    background_color?: string;
  };
  performance?: {
    lod_enabled: boolean;
    shadow_quality: 'low' | 'medium' | 'high';
    texture_quality: 'low' | 'medium' | 'high';
  };
}

export interface RoomInteraction {
  id: string;
  room_id: string;
  type: InteractionType;
  trigger: InteractionTrigger;
  action: InteractionAction;
  position?: Vector3D;
  radius?: number;
  enabled: boolean;
  metadata?: Record<string, unknown>;
}

export interface RoomAnalytics {
  room_id: string;
  total_users: number;
  unique_visitors: number;
  average_session_duration: number;
  popular_interactions: Array<{
    type: string;
    count: number;
  }>;
  performance_stats: {
    average_load_time: number;
    average_fps: number;
    crash_rate: number;
  };
  user_feedback: {
    average_rating: number;
    total_ratings: number;
    comments_count: number;
  };
}

export interface RoomTemplate {
  id: string;
  name: string;
  description: string;
  category: RoomCategory;
  preview_url: string;
  config: RoomConfig;
  assets: Array<{
    type: 'model' | 'texture' | 'audio';
    url: string;
    name: string;
  }>;
  is_premium: boolean;
  created_by: string;
}

// Enums and Union Types

export type RoomCategory = 
  | 'interior' 
  | 'outdoor' 
  | 'office' 
  | 'futuristic' 
  | 'fantasy' 
  | 'abstract'
  | 'minimal'
  | 'cozy'
  | 'professional'
  | 'creative'
  | 'nature'
  | 'urban';

export type InteractionType = 
  | 'hover'
  | 'click'
  | 'proximity'
  | 'gaze'
  | 'voice'
  | 'gesture';

export type InteractionTrigger = 
  | 'enter'
  | 'exit'
  | 'stay'
  | 'click'
  | 'voice_command';

export type InteractionAction = 
  | 'play_animation'
  | 'play_sound'
  | 'change_lighting'
  | 'spawn_object'
  | 'teleport'
  | 'show_message'
  | 'trigger_chat';

export type RoomQuality = 'low' | 'medium' | 'high' | 'ultra';

export type RoomStatus = 'active' | 'inactive' | 'processing' | 'error';

// API Response Types

export interface RoomAPIResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

export interface RoomListResponse extends RoomAPIResponse<Room3D[]> {
  filters?: {
    category?: string;
    isPremium?: boolean;
    search?: string;
  };
}

export interface RoomSearchFilters {
  category?: RoomCategory;
  isPremium?: boolean;
  isActive?: boolean;
  minRating?: number;
  maxFileSize?: number;
  tags?: string[];
  compatibility?: {
    mobile?: boolean;
    desktop?: boolean;
    vr?: boolean;
  };
  performance?: {
    maxLoadTime?: number;
    minFps?: number;
  };
}

export interface RoomValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  recommendations?: string[];
  performance_score?: number;
  compatibility_score?: number;
}

// Event Types

export interface RoomEvent {
  type: 'room_entered' | 'room_exited' | 'interaction_triggered' | 'error_occurred';
  room_id: string;
  user_id: string;
  timestamp: string;
  data?: Record<string, unknown>;
}

export interface RoomLoadEvent {
  type: 'load_start' | 'load_progress' | 'load_complete' | 'load_error';
  room_id: string;
  progress?: number;
  error?: string;
  load_time_ms?: number;
}

// Error Types

export class RoomError extends Error {
  code: string;
  room_id?: string;
  details?: Record<string, unknown>;

  constructor(message: string, code: string, room_id?: string, details?: Record<string, unknown>) {
    super(message);
    this.name = 'RoomError';
    this.code = code;
    this.room_id = room_id;
    this.details = details;
  }
}

export class RoomLoadError extends RoomError {
  constructor(message: string, room_id: string, details?: Record<string, unknown>) {
    super(message, 'ROOM_LOAD_ERROR', room_id, details);
    this.name = 'RoomLoadError';
  }
}

export class RoomValidationError extends RoomError {
  validation_errors: string[];

  constructor(message: string, validation_errors: string[], room_id?: string) {
    super(message, 'ROOM_VALIDATION_ERROR', room_id);
    this.name = 'RoomValidationError';
    this.validation_errors = validation_errors;
  }
}

// Utility Types

export interface RoomPreset {
  name: string;
  description: string;
  config: RoomConfig;
  thumbnail: string;
}

export interface RoomCustomization {
  lighting: {
    ambient_color: string;
    ambient_intensity: number;
    directional_color: string;
    directional_intensity: number;
    shadow_enabled: boolean;
  };
  environment: {
    fog_enabled: boolean;
    fog_color: string;
    fog_density: number;
    background_type: 'color' | 'gradient' | 'skybox';
    background_value: string;
  };
  post_processing: {
    bloom_enabled: boolean;
    bloom_intensity: number;
    tone_mapping: 'none' | 'linear' | 'reinhard' | 'cineon' | 'aces';
  };
}

export interface RoomExportOptions {
  format: 'glb' | 'gltf' | 'fbx' | 'obj';
  quality: RoomQuality;
  include_textures: boolean;
  include_animations: boolean;
  optimize_for_web: boolean;
  compression_level: number;
}

// Room Collection Types

export interface RoomCollection {
  id: string;
  name: string;
  description: string;
  owner_id: string;
  rooms: string[]; // Room IDs
  is_public: boolean;
  thumbnail?: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}

export interface RoomCollectionItem {
  collection_id: string;
  room_id: string;
  order: number;
  added_at: string;
}

// 3D Engine Specific Types

export interface ThreeJSRoomConfig {
  scene: {
    background?: string | THREE.Texture;
    fog?: {
      type: 'linear' | 'exponential';
      color: string;
      near: number;
      far?: number;
      density?: number;
    };
  };
  camera: {
    type: 'perspective' | 'orthographic';
    position: Vector3D;
    target: Vector3D;
    fov?: number;
    near: number;
    far: number;
  };
  renderer: {
    antialias: boolean;
    shadows: boolean;
    tone_mapping: string;
    exposure: number;
  };
}

declare global {
  namespace THREE {
    class Texture {}
  }
}