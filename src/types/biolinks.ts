// Biolinks Types
// TypeScript interfaces for biolinks landing page system

export interface BioLink {
  id: string;
  user_id: string;
  title: string;
  url: string;
  description?: string;
  icon?: string;
  custom_icon_url?: string;
  position: number;
  is_active: boolean;
  click_count: number;
  is_featured: boolean;
  style_config?: LinkStyleConfig;
  schedule?: LinkSchedule;
  metadata?: LinkMetadata;
  created_at: string;
  updated_at?: string;
}

export interface BioPage {
  id: string;
  user_id: string;
  username: string;
  display_name: string;
  bio?: string;
  profile_image_url?: string;
  cover_image_url?: string;
  theme: PageTheme;
  custom_theme?: CustomTheme;
  seo_config?: SEOConfig;
  analytics_config?: AnalyticsConfig;
  social_links?: SocialLink[];
  is_public: boolean;
  is_verified: boolean;
  page_views: number;
  total_clicks: number;
  created_at: string;
  updated_at?: string;
}

export interface PageTheme {
  name: string;
  background_type: 'solid' | 'gradient' | 'image' | 'video';
  background_value: string;
  text_color: string;
  link_style: LinkStyle;
  button_style: ButtonStyle;
  font_family: FontFamily;
  animations_enabled: boolean;
  custom_css?: string;
}

export interface CustomTheme {
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  background_color: string;
  text_color: string;
  border_radius: number;
  shadow_enabled: boolean;
  gradient_direction?: number;
  custom_css?: string;
}

export interface LinkStyleConfig {
  background_color?: string;
  text_color?: string;
  border_color?: string;
  border_width?: number;
  border_radius?: number;
  padding?: number;
  margin?: number;
  font_size?: number;
  font_weight?: FontWeight;
  animation?: AnimationType;
  hover_effect?: HoverEffect;
}

export interface LinkSchedule {
  is_scheduled: boolean;
  start_date?: string;
  end_date?: string;
  timezone?: string;
  days_of_week?: number[]; // 0-6 (Sunday-Saturday)
  time_ranges?: TimeRange[];
}

export interface TimeRange {
  start_time: string; // HH:MM format
  end_time: string;   // HH:MM format
}

export interface LinkMetadata {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  custom_params?: Record<string, string>;
  target_audience?: string[];
  geographic_restrictions?: string[];
  device_restrictions?: DeviceType[];
}

export interface SocialLink {
  platform: SocialPlatform;
  username: string;
  url: string;
  is_verified: boolean;
  icon_url?: string;
  display_order: number;
  is_active: boolean;
}

export interface SEOConfig {
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string[];
  og_title?: string;
  og_description?: string;
  og_image?: string;
  twitter_card?: TwitterCardType;
  twitter_title?: string;
  twitter_description?: string;
  twitter_image?: string;
  canonical_url?: string;
  robots?: string;
}

export interface AnalyticsConfig {
  google_analytics_id?: string;
  facebook_pixel_id?: string;
  google_tag_manager_id?: string;
  custom_tracking_code?: string;
  enable_click_tracking: boolean;
  enable_view_tracking: boolean;
  enable_conversion_tracking: boolean;
}

export interface PageAnalytics {
  id: string;
  page_id: string;
  user_id: string;
  date: string;
  page_views: number;
  unique_visitors: number;
  total_clicks: number;
  bounce_rate: number;
  avg_session_duration: number;
  top_referrers: Array<{
    source: string;
    visits: number;
    percentage: number;
  }>;
  device_breakdown: Array<{
    device_type: DeviceType;
    visits: number;
    percentage: number;
  }>;
  geographic_data: Array<{
    country: string;
    visits: number;
    percentage: number;
  }>;
  link_performance: Array<{
    link_id: string;
    clicks: number;
    ctr: number;
  }>;
  created_at: string;
}

export interface LinkAnalytics {
  id: string;
  link_id: string;
  user_id: string;
  date: string;
  clicks: number;
  unique_clicks: number;
  ctr: number;
  conversion_rate?: number;
  revenue?: number;
  referrer_data: Array<{
    source: string;
    clicks: number;
  }>;
  device_data: Array<{
    device_type: DeviceType;
    clicks: number;
  }>;
  geographic_data: Array<{
    country: string;
    clicks: number;
  }>;
  time_distribution: Array<{
    hour: number;
    clicks: number;
  }>;
  created_at: string;
}

export interface PageTemplate {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  thumbnail_url: string;
  preview_url: string;
  theme: PageTheme;
  default_links: Array<{
    title: string;
    url: string;
    icon: string;
  }>;\n  is_premium: boolean;\n  is_featured: boolean;\n  usage_count: number;\n  rating: number;\n  created_by: string;\n  created_at: string;\n}\n\nexport interface PageCustomization {\n  layout: LayoutType;\n  header: {\n    show_profile_image: boolean;\n    show_cover_image: boolean;\n    show_bio: boolean;\n    show_social_links: boolean;\n    alignment: 'left' | 'center' | 'right';\n  };\n  links: {\n    show_icons: boolean;\n    show_descriptions: boolean;\n    group_by_category: boolean;\n    animation_style: AnimationType;\n    layout_style: 'list' | 'grid' | 'cards';\n  };\n  footer: {\n    show_powered_by: boolean;\n    custom_text?: string;\n    show_social_links: boolean;\n  };\n}\n\nexport interface PageWidget {\n  id: string;\n  type: WidgetType;\n  title: string;\n  content: unknown;\n  position: number;\n  is_active: boolean;\n  style_config?: Record<string, unknown>;\n  settings?: Record<string, unknown>;\n}\n\n// Enums and Union Types\n\nexport type LinkStyle = \n  | 'default'\n  | 'rounded'\n  | 'pill'\n  | 'minimal'\n  | 'outlined'\n  | 'filled'\n  | 'gradient'\n  | 'neumorphism';\n\nexport type ButtonStyle = \n  | 'solid'\n  | 'outline'\n  | 'ghost'\n  | 'gradient'\n  | 'glassmorphism';\n\nexport type FontFamily = \n  | 'inter'\n  | 'roboto'\n  | 'open-sans'\n  | 'poppins'\n  | 'montserrat'\n  | 'playfair'\n  | 'lora'\n  | 'custom';\n\nexport type FontWeight = \n  | 'light'\n  | 'normal'\n  | 'medium'\n  | 'semibold'\n  | 'bold';\n\nexport type AnimationType = \n  | 'none'\n  | 'fade'\n  | 'slide'\n  | 'bounce'\n  | 'scale'\n  | 'rotate'\n  | 'pulse';\n\nexport type HoverEffect = \n  | 'none'\n  | 'lift'\n  | 'glow'\n  | 'scale'\n  | 'rotate'\n  | 'color-change'\n  | 'border-grow';\n\nexport type DeviceType = \n  | 'mobile'\n  | 'tablet'\n  | 'desktop'\n  | 'all';\n\nexport type SocialPlatform = \n  | 'instagram'\n  | 'twitter'\n  | 'facebook'\n  | 'youtube'\n  | 'tiktok'\n  | 'linkedin'\n  | 'github'\n  | 'discord'\n  | 'twitch'\n  | 'spotify'\n  | 'medium'\n  | 'behance'\n  | 'dribbble'\n  | 'pinterest'\n  | 'snapchat'\n  | 'whatsapp'\n  | 'telegram'\n  | 'email'\n  | 'website'\n  | 'custom';\n\nexport type TwitterCardType = \n  | 'summary'\n  | 'summary_large_image'\n  | 'app'\n  | 'player';\n\nexport type TemplateCategory = \n  | 'business'\n  | 'creative'\n  | 'personal'\n  | 'influencer'\n  | 'artist'\n  | 'musician'\n  | 'tech'\n  | 'food'\n  | 'fashion'\n  | 'fitness'\n  | 'travel'\n  | 'minimal'\n  | 'dark'\n  | 'colorful';\n\nexport type LayoutType = \n  | 'standard'\n  | 'centered'\n  | 'split'\n  | 'masonry'\n  | 'carousel'\n  | 'tabs';\n\nexport type WidgetType = \n  | 'text'\n  | 'image'\n  | 'video'\n  | 'audio'\n  | 'countdown'\n  | 'contact_form'\n  | 'newsletter'\n  | 'social_feed'\n  | 'testimonials'\n  | 'gallery'\n  | 'map'\n  | 'calendar'\n  | 'custom_html';\n\n// API Response Types\n\nexport interface BioLinksAPIResponse<T = unknown> {\n  success: boolean;\n  data?: T;\n  error?: {\n    code: string;\n    message: string;\n    details?: Record<string, unknown>;\n  };\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    hasMore: boolean;\n  };\n}\n\nexport interface PageResponse extends BioLinksAPIResponse<BioPage> {\n  links?: BioLink[];\n  analytics?: PageAnalytics;\n}\n\nexport interface LinksResponse extends BioLinksAPIResponse<BioLink[]> {\n  page_info?: {\n    id: string;\n    username: string;\n    is_public: boolean;\n  };\n}\n\nexport interface TemplatesResponse extends BioLinksAPIResponse<PageTemplate[]> {\n  categories?: Array<{\n    name: TemplateCategory;\n    count: number;\n  }>;\n}\n\n// Event Types\n\nexport interface BioLinksEvent {\n  type: BioLinksEventType;\n  page_id?: string;\n  link_id?: string;\n  user_id?: string;\n  data?: Record<string, unknown>;\n  timestamp: string;\n}\n\nexport type BioLinksEventType = \n  | 'page_viewed'\n  | 'link_clicked'\n  | 'page_created'\n  | 'page_updated'\n  | 'link_added'\n  | 'link_updated'\n  | 'link_deleted'\n  | 'theme_changed'\n  | 'analytics_generated';\n\n// Validation Types\n\nexport interface BioLinksValidationRules {\n  page: {\n    username: {\n      min_length: number;\n      max_length: number;\n      allowed_chars: string;\n      reserved_words: string[];\n    };\n    display_name: {\n      min_length: number;\n      max_length: number;\n    };\n    bio: {\n      max_length: number;\n    };\n  };\n  link: {\n    title: {\n      min_length: number;\n      max_length: number;\n    };\n    url: {\n      allowed_protocols: string[];\n      max_length: number;\n      blocked_domains: string[];\n    };\n    description: {\n      max_length: number;\n    };\n  };\n  limits: {\n    max_links_per_page: number;\n    max_social_links: number;\n    max_widgets: number;\n    max_custom_css_length: number;\n  };\n}\n\nexport interface BioLinksValidationResult {\n  isValid: boolean;\n  errors: string[];\n  warnings: string[];\n  suggestions?: string[];\n}\n\n// Error Types\n\nexport class BioLinksError extends Error {\n  code: string;\n  page_id?: string;\n  link_id?: string;\n  details?: Record<string, unknown>;\n\n  constructor(message: string, code: string, page_id?: string, details?: Record<string, unknown>) {\n    super(message);\n    this.name = 'BioLinksError';\n    this.code = code;\n    this.page_id = page_id;\n    this.details = details;\n  }\n}\n\nexport class PageNotFoundError extends BioLinksError {\n  constructor(username: string) {\n    super(`Page not found: ${username}`, 'PAGE_NOT_FOUND', undefined, { username });\n    this.name = 'PageNotFoundError';\n  }\n}\n\nexport class LinkValidationError extends BioLinksError {\n  validation_errors: string[];\n\n  constructor(message: string, validation_errors: string[], link_id?: string) {\n    super(message, 'LINK_VALIDATION_ERROR', undefined, { link_id });\n    this.name = 'LinkValidationError';\n    this.validation_errors = validation_errors;\n  }\n}\n\n// Utility Types\n\nexport interface BioLinksConfig {\n  default_theme: PageTheme;\n  available_templates: PageTemplate[];\n  validation_rules: BioLinksValidationRules;\n  feature_flags: {\n    custom_themes: boolean;\n    analytics: boolean;\n    seo_tools: boolean;\n    scheduling: boolean;\n    widgets: boolean;\n  };\n  limits: {\n    free_plan: {\n      max_links: number;\n      max_themes: number;\n      analytics_retention_days: number;\n    };\n    premium_plan: {\n      max_links: number;\n      max_themes: number;\n      analytics_retention_days: number;\n    };\n  };\n}\n\nexport interface PagePreview {\n  html: string;\n  css: string;\n  js?: string;\n  meta_tags: Record<string, string>;\n  performance_score?: number;\n}\n\nexport interface LinkShortener {\n  short_url: string;\n  original_url: string;\n  click_count: number;\n  created_at: string;\n  expires_at?: string;\n}\n\nexport interface QRCode {\n  url: string;\n  svg: string;\n  png: string;\n  size: number;\n  error_correction: 'L' | 'M' | 'Q' | 'H';\n}\n\n// Export utility functions type\nexport interface BioLinksUtils {\n  validateUsername: (username: string) => BioLinksValidationResult;\n  validateUrl: (url: string) => BioLinksValidationResult;\n  generateSlug: (text: string) => string;\n  extractDomain: (url: string) => string;\n  generateQRCode: (url: string, options?: { size?: number }) => Promise<QRCode>;\n  shortenUrl: (url: string) => Promise<LinkShortener>;\n  renderPreview: (page: BioPage, links: BioLink[]) => Promise<PagePreview>;\n  calculateAnalytics: (pageId: string, dateRange: { start: string; end: string }) => Promise<PageAnalytics>;\n}