// Subscription Types
// TypeScript interfaces for subscription and payment system

export interface Subscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: SubscriptionStatus;
  current_period_start: string;
  current_period_end: string;
  trial_start?: string;
  trial_end?: string;
  canceled_at?: string;
  ended_at?: string;
  payment_method_id?: string;
  billing_cycle: BillingCycle;
  next_billing_date: string;
  amount: number;
  currency: string;
  discount_id?: string;
  metadata?: SubscriptionMetadata;
  created_at: string;
  updated_at?: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  features: PlanFeature[];
  price_monthly: number;
  price_yearly: number;
  currency: string;
  is_popular: boolean;
  is_active: boolean;
  trial_days: number;
  billing_cycles: BillingCycle[];
  limits: PlanLimits;
  stripe_price_id_monthly?: string;
  stripe_price_id_yearly?: string;
  created_at: string;
  updated_at?: string;
}

export interface PlanFeature {
  name: string;
  description: string;
  included: boolean;
  limit?: number;
  unlimited?: boolean;
  badge?: string;
}

export interface PlanLimits {
  max_links: number;
  max_rooms: number;
  max_avatars: number;
  max_chat_sessions_per_month: number;
  max_page_views_per_month: number;
  max_custom_themes: number;
  analytics_retention_days: number;
  priority_support: boolean;
  white_label: boolean;
  custom_domain: boolean;
  api_access: boolean;
}

export interface PaymentMethod {
  id: string;
  user_id: string;
  type: PaymentMethodType;
  provider: PaymentProvider;
  provider_id: string;
  is_default: boolean;
  card_details?: CardDetails;
  bank_details?: BankDetails;
  wallet_details?: WalletDetails;
  billing_address?: BillingAddress;
  expires_at?: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface CardDetails {
  brand: string;
  last_four: string;
  exp_month: number;
  exp_year: number;
  country?: string;
  fingerprint?: string;
}

export interface BankDetails {
  bank_name: string;
  account_type: string;
  routing_number: string;
  last_four: string;
  country: string;
}

export interface WalletDetails {
  wallet_type: WalletType;
  email?: string;
  phone?: string;
}

export interface BillingAddress {
  name: string;
  line1: string;
  line2?: string;
  city: string;
  state?: string;
  postal_code: string;
  country: string;
}

export interface Invoice {
  id: string;
  user_id: string;
  subscription_id: string;
  invoice_number: string;
  status: InvoiceStatus;
  amount_due: number;
  amount_paid: number;
  amount_remaining: number;
  currency: string;
  description: string;
  line_items: InvoiceLineItem[];
  tax_amount?: number;
  discount_amount?: number;
  subtotal: number;
  total: number;
  due_date: string;
  paid_at?: string;
  payment_method_id?: string;
  payment_intent_id?: string;
  hosted_invoice_url?: string;
  invoice_pdf_url?: string;
  metadata?: Record<string, unknown>;
  created_at: string;
}

export interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
  period_start?: string;
  period_end?: string;
  proration?: boolean;
}

export interface PaymentIntent {
  id: string;
  user_id: string;
  amount: number;
  currency: string;
  payment_method_id: string;
  status: PaymentIntentStatus;
  description?: string;
  receipt_email?: string;
  client_secret?: string;
  confirmation_method: 'automatic' | 'manual';
  capture_method: 'automatic' | 'manual';
  setup_future_usage?: 'on_session' | 'off_session';
  metadata?: Record<string, unknown>;
  created_at: string;
  confirmed_at?: string;
  succeeded_at?: string;
}

export interface Discount {
  id: string;
  name: string;
  code: string;
  type: DiscountType;
  value: number;
  currency?: string;
  max_redemptions?: number;
  redemptions_count: number;
  is_active: boolean;
  valid_from: string;
  valid_until?: string;
  applicable_plans: string[];
  first_time_only: boolean;
  metadata?: Record<string, unknown>;
  created_at: string;
}

export interface SubscriptionUsage {
  id: string;
  user_id: string;
  subscription_id: string;
  period_start: string;
  period_end: string;
  links_created: number;
  rooms_used: number;
  avatars_created: number;
  chat_sessions: number;
  page_views: number;
  api_requests: number;
  storage_used_mb: number;
  bandwidth_used_gb: number;
  created_at: string;
}

export interface SubscriptionAnalytics {
  id: string;
  user_id: string;
  date: string;
  mrr: number; // Monthly Recurring Revenue
  arr: number; // Annual Recurring Revenue
  churn_risk_score: number;
  engagement_score: number;
  feature_usage: Record<string, number>;
  support_tickets: number;
  login_frequency: number;
  last_activity: string;
  upgrade_likelihood: number;
  created_at: string;
}

export interface BillingEvent {
  id: string;
  user_id: string;
  subscription_id?: string;
  type: BillingEventType;
  amount?: number;
  currency?: string;
  description: string;
  metadata?: Record<string, unknown>;
  processed_at: string;
  created_at: string;
}

// Enums and Union Types

export type SubscriptionStatus = 
  | 'active'
  | 'trialing'
  | 'past_due'
  | 'canceled'
  | 'unpaid'
  | 'incomplete'
  | 'incomplete_expired'
  | 'paused';

export type BillingCycle = 
  | 'monthly'
  | 'yearly'
  | 'quarterly'
  | 'weekly';

export type PaymentMethodType = 
  | 'card'
  | 'bank_account'
  | 'wallet'
  | 'crypto';

export type PaymentProvider = 
  | 'stripe'
  | 'paypal'
  | 'apple_pay'
  | 'google_pay'
  | 'heey_wallet'
  | 'crypto';

export type WalletType = 
  | 'paypal'
  | 'apple_pay'
  | 'google_pay'
  | 'samsung_pay'
  | 'heey_wallet';

export type InvoiceStatus = 
  | 'draft'
  | 'open'
  | 'paid'
  | 'void'
  | 'uncollectible';

export type PaymentIntentStatus = 
  | 'requires_payment_method'
  | 'requires_confirmation'
  | 'requires_action'
  | 'processing'
  | 'requires_capture'
  | 'canceled'
  | 'succeeded';

export type DiscountType = 
  | 'percentage'
  | 'fixed_amount'
  | 'free_trial_extension';

export type BillingEventType = 
  | 'subscription_created'
  | 'subscription_updated'
  | 'subscription_canceled'
  | 'subscription_renewed'
  | 'payment_succeeded'
  | 'payment_failed'
  | 'invoice_created'
  | 'invoice_paid'
  | 'trial_started'
  | 'trial_ended'
  | 'discount_applied'
  | 'refund_issued';

// API Response Types

export interface SubscriptionAPIResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

export interface CheckoutSessionResponse extends SubscriptionAPIResponse<{
  session_id: string;
  url: string;
  expires_at: string;
}> {}

export interface PaymentMethodResponse extends SubscriptionAPIResponse<PaymentMethod> {}

export interface SubscriptionResponse extends SubscriptionAPIResponse<Subscription> {
  plan?: SubscriptionPlan;
  usage?: SubscriptionUsage;
}

export interface PlansResponse extends SubscriptionAPIResponse<SubscriptionPlan[]> {
  current_plan?: SubscriptionPlan;
}

export interface InvoicesResponse extends SubscriptionAPIResponse<Invoice[]> {
  upcoming_invoice?: Invoice;
}

// Webhook Types

export interface StripeWebhookEvent {
  id: string;
  type: string;
  data: {
    object: Record<string, unknown>;
    previous_attributes?: Record<string, unknown>;
  };
  created: number;
  livemode: boolean;
  pending_webhooks: number;
  request?: {
    id: string;
    idempotency_key?: string;
  };
}

export interface PayPalWebhookEvent {
  id: string;
  event_type: string;
  resource_type: string;
  summary: string;
  resource: Record<string, unknown>;
  create_time: string;
  event_version: string;
  resource_version?: string;
}

// Feature Flag Types

export interface SubscriptionFeatureFlags {
  name: string;
  enabled: boolean;
  plan_requirements?: string[];
  usage_limits?: Record<string, number>;
}

// Subscription Configuration

export interface SubscriptionConfig {
  available_plans: SubscriptionPlan[];
  payment_providers: PaymentProvider[];
  supported_currencies: string[];
  trial_enabled: boolean;
  default_trial_days: number;
  grace_period_days: number;
  dunning_configuration: {
    max_attempts: number;
    retry_intervals: number[];
  };
  feature_flags: SubscriptionFeatureFlags[];
}

// Usage Tracking

export interface UsageEvent {
  user_id: string;
  subscription_id: string;
  feature: string;
  quantity: number;
  timestamp: string;
  metadata?: Record<string, unknown>;
}

export interface UsageLimit {
  feature: string;
  limit: number;
  period: 'daily' | 'monthly' | 'yearly';
  reset_on_billing_cycle: boolean;
}

// Churn Prevention

export interface ChurnRiskIndicator {
  indicator: string;
  weight: number;
  threshold: number;
  description: string;
}

export interface ChurnPrevention {
  user_id: string;
  risk_score: number;
  risk_factors: string[];
  recommended_actions: string[];
  intervention_history: Array<{
    type: string;
    date: string;
    result: string;
  }>;
  next_review_date: string;
}

// Pricing and Revenue

export interface PricingRule {
  id: string;
  name: string;
  type: 'volume' | 'usage' | 'seat' | 'feature';
  tiers: Array<{
    from: number;
    to?: number;
    price: number;
  }>;
  is_active: boolean;
}

export interface RevenueMetrics {
  mrr: number;
  arr: number;
  churn_rate: number;
  ltv: number; // Lifetime Value
  cac: number; // Customer Acquisition Cost
  revenue_per_user: number;
  growth_rate: number;
  period: string;
}

// Error Types

export class SubscriptionError extends Error {
  code: string;
  subscription_id?: string;
  details?: Record<string, unknown>;

  constructor(message: string, code: string, subscription_id?: string, details?: Record<string, unknown>) {
    super(message);
    this.name = 'SubscriptionError';
    this.code = code;
    this.subscription_id = subscription_id;
    this.details = details;
  }
}

export class PaymentError extends SubscriptionError {
  payment_intent_id?: string;

  constructor(message: string, payment_intent_id?: string, details?: Record<string, unknown>) {
    super(message, 'PAYMENT_ERROR', undefined, details);
    this.name = 'PaymentError';
    this.payment_intent_id = payment_intent_id;
  }
}

export class PlanLimitError extends SubscriptionError {
  limit_type: string;
  current_usage: number;
  limit: number;

  constructor(limit_type: string, current_usage: number, limit: number, subscription_id?: string) {
    super(`${limit_type} limit exceeded: ${current_usage}/${limit}`, 'PLAN_LIMIT_ERROR', subscription_id);
    this.name = 'PlanLimitError';
    this.limit_type = limit_type;
    this.current_usage = current_usage;
    this.limit = limit;
  }
}

// Validation Types

export interface SubscriptionValidationRules {
  payment_method: {
    required_for_trial: boolean;
    allowed_types: PaymentMethodType[];
  };
  billing: {
    grace_period_days: number;
    max_failed_attempts: number;
    dunning_enabled: boolean;
  };
  plan_changes: {
    allow_downgrades: boolean;
    proration_enabled: boolean;
    immediate_change: boolean;
  };
}

export interface SubscriptionValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  can_proceed: boolean;
  required_actions?: string[];
}

// Utility Types

export interface SubscriptionMetadata {
  source?: string;
  campaign?: string;
  referrer?: string;
  coupon_code?: string;
  custom_fields?: Record<string, unknown>;
}

export interface BillingPortalSession {
  id: string;
  url: string;
  return_url: string;
  expires_at: string;
}

export interface TaxCalculation {
  amount: number;
  rate: number;
  jurisdiction: string;
  type: 'vat' | 'sales_tax' | 'gst';
}

// Export utility functions type
export interface SubscriptionUtils {
  calculateProration: (oldPlan: SubscriptionPlan, newPlan: SubscriptionPlan, daysRemaining: number) => number;
  validatePlanChange: (currentPlan: SubscriptionPlan, newPlan: SubscriptionPlan) => SubscriptionValidationResult;
  calculateTax: (amount: number, country: string, state?: string) => TaxCalculation;
  formatCurrency: (amount: number, currency: string) => string;
  isFeatureAvailable: (feature: string, plan: SubscriptionPlan) => boolean;
  calculateUsageOverage: (usage: SubscriptionUsage, limits: PlanLimits) => number;
  generateInvoice: (subscription: Subscription, lineItems: InvoiceLineItem[]) => Invoice;
  processWebhook: (provider: PaymentProvider, event: unknown) => Promise<void>;
}