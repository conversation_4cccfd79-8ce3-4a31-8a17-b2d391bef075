// Avatar Management Types
// TypeScript interfaces for avatar system

export interface AvatarData {
  id: string;
  user_id: string;
  rpm_avatar_id?: string;
  avatar_url: string;
  thumbnail_url?: string;
  name?: string;
  metadata?: Record<string, unknown>;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface Room3D {
  id: string;
  name: string;
  description?: string;
  glb_url: string;
  thumbnail_url?: string;
  category: string;
  is_premium: boolean;
  is_active: boolean;
  popularity_score: number;
  creator_id?: string;
  download_count: number;
  rating: number;
  tags: string[];
  metadata?: Record<string, unknown>;
  created_at: string;
  updated_at?: string;
}

export interface UserRoom {
  id: string;
  user_id: string;
  room_id: string;
  is_default: boolean;
  position: {
    x: number;
    y: number;
    z: number;
  };
  rotation: {
    x: number;
    y: number;
    z: number;
  };
  scale: number;
  last_used_at: string;
  usage_count: number;
  created_at: string;
}

export interface AvatarConfig {
  position: {
    x: number;
    y: number;
    z: number;
  };
  rotation: {
    x: number;
    y: number;
    z: number;
  };
  scale: number;
  animations?: {
    idle?: string;
    talking?: string;
    greeting?: string;
  };
  lighting?: {
    ambient: number;
    directional: number;
  };
}

export interface ReadyPlayerMeConfig {
  subdomain: string;
  quickStart: boolean;
  clearCache: boolean;
  bodyType: 'halfbody' | 'fullbody';
  language: string;
  avatarTemplate?: string;
  customizations?: {
    hair?: boolean;
    beard?: boolean;
    eyes?: boolean;
    eyebrows?: boolean;
    facewear?: boolean;
    headwear?: boolean;
    clothing?: boolean;
  };
}

export interface RPMAvatar {
  id: string;
  partner: string;
  modelUrl: string;
  thumbnailUrl?: string;
  updatedAt: string;
  metadata?: {
    bodyType?: string;
    gender?: string;
    outfitGender?: string;
    assets?: Record<string, unknown>;
  };
}

export interface AvatarCreationOptions {
  bodyType?: 'halfbody' | 'fullbody';
  gender?: 'male' | 'female';
  style?: 'realistic' | 'cartoon' | 'anime';
  template?: string;
  customizations?: Record<string, unknown>;
}

export interface AvatarInteraction {
  type: 'hover' | 'click' | 'voice' | 'gesture';
  animation?: string;
  sound?: string;
  response?: string;
  duration?: number;
}

export interface AvatarPersonality {
  tone: 'friendly' | 'professional' | 'casual' | 'enthusiastic' | 'calm';
  style: 'conversational' | 'formal' | 'humorous' | 'supportive';
  traits: string[];
  interests: string[];
  background?: string;
  customInstructions?: string;
}

export interface AvatarAnalytics {
  total_interactions: number;
  unique_visitors: number;
  avg_session_duration: number;
  popular_interactions: Array<{
    type: string;
    count: number;
  }>;
  sentiment_scores: {
    positive: number;
    neutral: number;
    negative: number;
  };
  conversation_topics: Array<{
    topic: string;
    frequency: number;
  }>;
}

export interface AvatarExportOptions {
  format: 'glb' | 'fbx' | 'obj';
  quality: 'low' | 'medium' | 'high';
  includeAnimations: boolean;
  includeTextures: boolean;
  compression: boolean;
}

export interface AvatarValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  recommendations?: string[];
}

// Event types for avatar system
export interface AvatarEvent {
  type: 'created' | 'updated' | 'deleted' | 'activated' | 'deactivated';
  avatarId: string;
  userId: string;
  timestamp: string;
  metadata?: Record<string, unknown>;
}

// Error types
export class AvatarError extends Error {
  code: string;
  details?: Record<string, unknown>;

  constructor(message: string, code: string, details?: Record<string, unknown>) {
    super(message);
    this.name = 'AvatarError';
    this.code = code;
    this.details = details;
  }
}

export class RPMError extends AvatarError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, 'RPM_ERROR', details);
    this.name = 'RPMError';
  }
}

export class AvatarValidationError extends AvatarError {
  validationErrors: string[];

  constructor(message: string, validationErrors: string[]) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'AvatarValidationError';
    this.validationErrors = validationErrors;
  }
}

// Utility types
export type AvatarStatus = 'active' | 'inactive' | 'processing' | 'error';
export type AvatarSource = 'ready_player_me' | 'upload' | 'generated' | 'default';
export type RoomCategory = 'interior' | 'outdoor' | 'office' | 'futuristic' | 'fantasy' | 'abstract';

// API Response types
export interface AvatarAPIResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

export interface AvatarListResponse extends AvatarAPIResponse<AvatarData[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

export interface RoomListResponse extends AvatarAPIResponse<Room3D[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}