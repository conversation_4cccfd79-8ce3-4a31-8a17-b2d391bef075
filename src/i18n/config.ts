
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import en from './locales/en.json';
import zh from './locales/zh.json';
import ja from './locales/ja.json';
import ko from './locales/ko.json';
import th from './locales/th.json';
import es from './locales/es.json';
import ar from './locales/ar.json';
import vi from './locales/vi.json';

export const languages = {
  en: { name: 'English', flag: '🇺🇸', dir: 'ltr' },
  zh: { name: '中文', flag: '🇨🇳', dir: 'ltr' },
  ja: { name: '日本語', flag: '🇯🇵', dir: 'ltr' },
  ko: { name: '한국어', flag: '🇰🇷', dir: 'ltr' },
  th: { name: 'ไทย', flag: '🇹🇭', dir: 'ltr' },
  es: { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸', dir: 'ltr' },
  ar: { name: 'العربية', flag: '🇸🇦', dir: 'rtl' },
  vi: { name: 'Tiếng Việt', flag: '🇻🇳', dir: 'ltr' }
};

const resources = {
  en: { translation: en },
  zh: { translation: zh },
  ja: { translation: ja },
  ko: { translation: ko },
  th: { translation: th },
  es: { translation: es },
  ar: { translation: ar },
  vi: { translation: vi }
};

i18n.use(LanguageDetector);

// Ensure default language is English on first load
if (typeof localStorage !== 'undefined' && !localStorage.getItem('i18nextLng')) {
  localStorage.setItem('i18nextLng', 'en');
}

i18n
  // language detector is registered above
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: false,

    detection: {
      // ignore the browser navigator language to keep English as default
      order: ['localStorage', 'htmlTag'],
      caches: ['localStorage'],
    },

    interpolation: {
      escapeValue: false,
    },

    react: {
      useSuspense: false,
    },
  });

// Update document direction and lang attributes whenever the language changes
i18n.on('languageChanged', (lng) => {
  const lang = lng.split('-')[0];
  document.documentElement.lang = lang;
  document.documentElement.dir =
    languages[lang as keyof typeof languages]?.dir || 'ltr';
});

export default i18n;
