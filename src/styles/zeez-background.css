
/* FILE: src/styles/zeez-background.css */

/* Container bao quanh tất cả shapes */
.zeez-floating-shapes {
  position: absolute;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
  z-index: 0;
}

/* Tất cả shapes con sẽ có chung các thuộc tính position và animation */
.zeez-floating-shapes > div {
  position: absolute;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}

/* 1) Diamond - Enhanced floating with rotation */
.zeez-floating-diamond {
  animation-name: floatDiamond;
  animation-duration: 8s;
}
@keyframes floatDiamond {
  0%   { transform: translateY(0) rotate(0deg);   opacity: 0.30; }
  25%  { transform: translateY(-15px) rotate(90deg); opacity: 0.70; }
  50%  { transform: translateY(10px) rotate(180deg); opacity: 0.90; }
  75%  { transform: translateY(-8px) rotate(270deg); opacity: 0.60; }
  100% { transform: translateY(0) rotate(360deg);   opacity: 0.30; }
}

/* 2) Circle - More dynamic scaling */
.zeez-floating-circle {
  animation-name: floatCircle;
  animation-duration: 10s;
}
@keyframes floatCircle {
  0%   { transform: translateY(0) scale(1);    opacity: 0.25; }
  33%  { transform: translateY(-25px) scale(1.4); opacity: 0.80; }
  66%  { transform: translateY(15px) scale(0.8); opacity: 0.90; }
  100% { transform: translateY(0) scale(1);    opacity: 0.25; }
}

/* 3) Orb - Larger movements */
.zeez-orb {
  animation-name: floatOrb;
  animation-duration: 12s;
}
@keyframes floatOrb {
  0%   { transform: translateY(0) scaleX(1);    opacity: 0.15; }
  25%  { transform: translateY(30px) scaleX(1.2); opacity: 0.45; }
  50%  { transform: translateY(-20px) scaleX(0.9); opacity: 0.60; }
  75%  { transform: translateY(25px) scaleX(1.1); opacity: 0.40; }
  100% { transform: translateY(0) scaleX(1);    opacity: 0.15; }
}

/* 4) Heart - TikTok-style heartbeat */
.zeez-heart {
  animation-name: heartBeat;
  animation-duration: 3s;
  animation-timing-function: ease;
}
@keyframes heartBeat {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  15%      { transform: scale(1.3); opacity: 0.9; }
  30%      { transform: scale(1.1); opacity: 0.8; }
  50%      { transform: scale(1.4); opacity: 1; }
  70%      { transform: scale(1.2); opacity: 0.7; }
}

/* 5) Sparkle - Enhanced sparkle effect */
.zeez-sparkle {
  animation-name: sparkle;
  animation-duration: 3s;
}
@keyframes sparkle {
  0%   { opacity: 0; transform: scale(0) rotate(0deg); }
  20%  { opacity: 1; transform: scale(1.2) rotate(90deg); }
  40%  { opacity: 0.7; transform: scale(0.8) rotate(180deg); }
  60%  { opacity: 1; transform: scale(1.3) rotate(270deg); }
  80%  { opacity: 0.5; transform: scale(1) rotate(360deg); }
  100% { opacity: 0; transform: scale(0) rotate(450deg); }
}

/* 6) Particle - Floating upward */
.zeez-particle {
  animation-name: floatParticle;
  animation-duration: 15s;
  animation-timing-function: linear;
}
@keyframes floatParticle {
  0%   { transform: translateY(0) translateX(0); opacity: 0.8; }
  25%  { transform: translateY(-30px) translateX(10px); opacity: 1; }
  50%  { transform: translateY(-60px) translateX(-5px); opacity: 0.6; }
  75%  { transform: translateY(-90px) translateX(15px); opacity: 0.3; }
  100% { transform: translateY(-120px) translateX(0); opacity: 0; }
}

/* 7) Music Note - TikTok music vibes */
.zeez-music-note {
  animation-name: musicNote;
  animation-duration: 4s;
}
@keyframes musicNote {
  0%   { transform: translateY(0) rotate(0deg); opacity: 0.70; }
  25%  { transform: translateY(-12px) rotate(15deg); opacity: 1; }
  50%  { transform: translateY(8px) rotate(-10deg); opacity: 0.80; }
  75%  { transform: translateY(-6px) rotate(20deg); opacity: 0.90; }
  100% { transform: translateY(0) rotate(0deg); opacity: 0.70; }
}

/* 8) Pulse Dot - New pulsing effect */
.zeez-pulse-dot {
  animation-name: pulseDot;
  animation-duration: 2s;
}
@keyframes pulseDot {
  0%   { transform: scale(1); opacity: 0.4; }
  50%  { transform: scale(1.8); opacity: 1; }
  100% { transform: scale(1); opacity: 0.4; }
}

/* 9) Bounce - Bouncing effect */
.zeez-bounce {
  animation-name: bounce;
  animation-duration: 2.5s;
}
@keyframes bounce {
  0%, 100% { transform: translateY(0) scale(1); }
  25%      { transform: translateY(-20px) scale(1.1); }
  50%      { transform: translateY(-5px) scale(0.95); }
  75%      { transform: translateY(-15px) scale(1.05); }
}

/* 10) Gradient Orb - Floating gradient spheres */
.zeez-gradient-orb {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,20,147,0.3), rgba(37,244,238,0.2), transparent);
  animation: floatGradientOrb 20s infinite ease-in-out;
  filter: blur(8px);
}
@keyframes floatGradientOrb {
  0%   { transform: translateY(0) translateX(0) scale(1); opacity: 0.3; }
  25%  { transform: translateY(-40px) translateX(20px) scale(1.2); opacity: 0.6; }
  50%  { transform: translateY(20px) translateX(-30px) scale(0.8); opacity: 0.8; }
  75%  { transform: translateY(-30px) translateX(15px) scale(1.1); opacity: 0.5; }
  100% { transform: translateY(0) translateX(0) scale(1); opacity: 0.3; }
}

/* 11) Enhanced Tiktok-style gradient */
.zeez-tiktok-gradient {
  background: linear-gradient(45deg,
    rgba(48,213,200,0.6),
    rgba(255,79,79,0.6)
  );
  background-size: 400% 400%;
  animation: gradientShift 12s ease infinite;
}
@keyframes gradientShift {
  0%   { background-position: 0%   50%; }
  25%  { background-position: 100% 0%; }
  50%  { background-position: 100% 100%; }
  75%  { background-position: 0%   100%; }
  100% { background-position: 0%   50%; }
}

/* Aura color variations */
.zeez-aura-turquoise {
  background-color: #30d5c8;
}

.zeez-aura-red {
  background-color: #ff4f4f;
}

.zeez-aura-gradient {
  background: linear-gradient(45deg, #30d5c8, #ff4f4f);
  background-size: 400% 400%;
  animation: gradientShift 12s ease infinite;
}

/* 12) Additional floating variations */
@keyframes floatSlow {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50%      { transform: translateY(-30px) rotate(180deg); }
}

@keyframes floatFast {
  0%, 100% { transform: translateY(0) scale(1); }
  50%      { transform: translateY(-50px) scale(1.2); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .zeez-floating-shapes > div {
    animation-duration: 0.8s;
  }
  
  .zeez-orb {
    width: 16px !important;
    height: 16px !important;
  }
  
  .zeez-gradient-orb {
    width: 40px;
    height: 40px;
  }
}
