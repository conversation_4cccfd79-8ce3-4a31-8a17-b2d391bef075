
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS custom properties for theming */
@layer base {
  :root {
    --background: 210 17% 7%;
    --foreground: 0 0% 98%;
    --card: 210 17% 9%;
    --card-foreground: 0 0% 98%;
    --popover: 210 17% 9%;
    --popover-foreground: 0 0% 98%;
    --primary: 190 100% 55%;
    --primary-foreground: 210 17% 7%;
    --secondary: 115 50% 75%;
    --secondary-foreground: 210 17% 7%;
    --muted: 210 15% 16%;
    --muted-foreground: 210 5% 65%;
    --accent: 210 15% 16%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 86% 59%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 12% 20%;
    --input: 210 12% 20%;
    --ring: 190 100% 55%;
    --radius: 0.75rem;
    
    /* Dark glassmorphism base */
    --glass-bg: 210 17% 7%;
    --glass-border: 210 12% 20%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
  }

  [dir="rtl"] .space-x-reverse > * + * {
    margin-right: var(--tw-space-x-reverse, 0);
    margin-left: calc(var(--tw-space-x) * calc(1 - var(--tw-space-x-reverse, 0)));
  }

  /* Arabic font support */
  body.rtl {
    font-family: 'Noto Sans Arabic', 'Arial', sans-serif;
  }

  /* RTL-specific adjustments */
  .rtl .text-right {
    text-align: right;
  }

  .rtl .text-left {
    text-align: left;
  }

  .rtl .ml-auto {
    margin-left: unset;
    margin-right: auto;
  }

  .rtl .mr-auto {
    margin-right: unset;
    margin-left: auto;
  }

  /* ... keep existing code (marquee animations and other existing styles) */
}

/* Enhanced Dark Glassmorphism Design System */
@layer components {
  /* Main glass panel with enhanced #001B23 base */
  .glass-panel {
    @apply bg-[#001B23]/70 backdrop-blur-lg shadow-xl shadow-black/25;
  }

  /* Enhanced glass header for navigation and headers */
  .glass-header {
    @apply bg-[#001B23]/80 backdrop-blur-lg shadow-lg shadow-black/20;
  }

  /* Enhanced interactive glass button */
  .glass-button {
    @apply bg-[#001B23]/50 hover:bg-[#001B23]/70 backdrop-blur-md shadow-lg shadow-black/25 transition-all duration-200;
  }

  /* Unified glass card style */
  .glass-card {
    @apply bg-[#001B23]/30 backdrop-blur-md rounded-xl border border-white/15;
  }

  /* Section container with stronger glass effect */
  .glass-section {
    @apply bg-[#001B23]/60 backdrop-blur-lg rounded-3xl;
  }

  /* Enhanced glass card for modals and important content */
  .glass-modal {
    @apply bg-[#001B23]/75 backdrop-blur-xl shadow-2xl shadow-black/35 rounded-xl;
  }

  /* New Primary Gradient */
  .primary-gradient {
    @apply bg-gradient-to-r from-[#1DEBF6] to-[#83FFBC];
  }

  /* Brand gradient - updated with new colors */
  .brand-gradient {
    @apply bg-gradient-to-r from-[#1DEBF6] to-[#83FFBC] text-black;
  }

  /* Text gradient for headings and highlights */
  .text-gradient {
    @apply bg-gradient-to-r from-[#1DEBF6] to-[#83FFBC] bg-clip-text text-transparent;
  }

  /* Container utilities */
  .mobile-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .mobile-full-screen {
    @apply min-h-screen min-h-[100dvh];
  }

  .safe-area-top {
    @apply pt-[env(safe-area-inset-top)];
  }

  .safe-area-bottom {
    @apply pb-[env(safe-area-inset-bottom)];
  }

  /* Button system - updated with new gradient */
  .btn-primary {
    @apply bg-gradient-to-r from-[#1DEBF6] to-[#83FFBC] hover:from-[#1DEBF6]/90 hover:to-[#83FFBC]/90 text-black font-medium px-6 py-2.5 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-transparent border border-[#1DEBF6]/30 text-[#1DEBF6] hover:bg-gradient-to-r hover:from-[#1DEBF6]/10 hover:to-[#83FFBC]/10 hover:border-[#1DEBF6]/50 font-medium px-6 py-2.5 rounded-lg transition-all duration-200 backdrop-blur-sm;
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-[#001B23]/40 text-foreground font-medium px-6 py-2.5 rounded-lg transition-all duration-200 backdrop-blur-sm;
  }

  /* Typography scale */
  .heading-xl {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight;
  }

  .heading-lg {
    @apply text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight;
  }

  .heading-md {
    @apply text-2xl sm:text-3xl font-bold tracking-tight;
  }

  .heading-sm {
    @apply text-xl sm:text-2xl font-semibold tracking-tight;
  }

  .body-lg {
    @apply text-lg sm:text-xl;
  }

  .body-md {
    @apply text-base sm:text-lg;
  }

  .body-sm {
    @apply text-sm;
  }

  .caption {
    @apply text-xs;
  }

  /* Focus ring for accessibility */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#1DEBF6] focus-visible:ring-offset-2 focus-visible:ring-offset-background;
  }

  /* Custom scrollbar for glass containers */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(29, 235, 246, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-[#1DEBF6]/30 rounded-full hover:bg-[#1DEBF6]/50;
  }
}

/* Safe area insets for mobile devices */
@supports (padding-top: env(safe-area-inset-top)) {
  .safe-area-inset-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-inset-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}
