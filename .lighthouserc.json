{"ci": {"collect": {"numberOfRuns": 3, "startServerCommand": "npm run preview", "url": ["http://localhost:4173"], "settings": {"chromeFlags": "--no-sandbox --disable-dev-shm-usage"}}, "assert": {"preset": "lighthouse:recommended", "assertions": {"categories:performance": ["error", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["error", {"minScore": 0.8}], "categories:seo": ["error", {"minScore": 0.8}], "categories:pwa": ["warn", {"minScore": 0.6}]}}, "upload": {"target": "temporary-public-storage"}}}