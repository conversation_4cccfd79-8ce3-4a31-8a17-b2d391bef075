
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_cron";

-- <PERSON>reate enum types
CREATE TYPE public.user_role AS ENUM ('user', 'visitor', 'admin');
CREATE TYPE public.subscription_status AS ENUM ('active', 'inactive', 'cancelled', 'past_due');
CREATE TYPE public.plan_type AS ENUM ('basic', 'premium', 'enterprise');
CREATE TYPE public.payment_method AS ENUM ('stripe', 'crypto');
CREATE TYPE public.chat_status AS ENUM ('active', 'archived', 'deleted');

-- Create profiles table (extends auth.users)
CREATE TABLE public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username TEXT UNIQUE,
  display_name TEXT,
  bio TEXT,
  avatar_url TEXT,
  avatar_name TEXT,
  avatar_thumbnail TEXT,
  social_links JSONB DEFAULT '{}',
  role user_role DEFAULT 'user',
  is_public BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create subscriptions table
CREATE TABLE public.subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT,
  status subscription_status DEFAULT 'inactive',
  plan_type plan_type DEFAULT 'basic',
  payment_method payment_method DEFAULT 'stripe',
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ,
  amount INTEGER, -- in cents
  currency TEXT DEFAULT 'usd',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create avatars table for Ready Player Me integration
CREATE TABLE public.avatars (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  rpm_avatar_id TEXT,
  avatar_url TEXT NOT NULL,
  thumbnail_url TEXT,
  name TEXT,
  metadata JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create visitors table
CREATE TABLE public.visitors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  profile_owner_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  display_name TEXT,
  email TEXT,
  avatar_url TEXT,
  first_visit_at TIMESTAMPTZ DEFAULT NOW(),
  last_visit_at TIMESTAMPTZ DEFAULT NOW(),
  total_visits INTEGER DEFAULT 1,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create chats table for conversations
CREATE TABLE public.chats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE, -- profile owner
  visitor_id UUID REFERENCES public.visitors(id) ON DELETE CASCADE,
  session_id TEXT,
  status chat_status DEFAULT 'active',
  started_at TIMESTAMPTZ DEFAULT NOW(),
  ended_at TIMESTAMPTZ,
  total_messages INTEGER DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create chat_messages table
CREATE TABLE public.chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE,
  sender_type TEXT CHECK (sender_type IN ('user', 'visitor', 'ai')),
  sender_id UUID, -- can be user_id or visitor_id
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'text', -- text, image, audio, etc.
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create topics table for AI-extracted conversation topics
CREATE TABLE public.topics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  topic TEXT NOT NULL,
  sentiment TEXT, -- positive, negative, neutral
  confidence REAL DEFAULT 0.0,
  keywords TEXT[],
  summary TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create spaces table for VerseEngine 3D spaces
CREATE TABLE public.spaces (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  verse_engine_space_id TEXT,
  space_url TEXT,
  is_public BOOLEAN DEFAULT false,
  max_visitors INTEGER DEFAULT 10,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create space_visits table
CREATE TABLE public.space_visits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  space_id UUID REFERENCES public.spaces(id) ON DELETE CASCADE,
  visitor_id UUID REFERENCES public.visitors(id) ON DELETE CASCADE,
  entered_at TIMESTAMPTZ DEFAULT NOW(),
  left_at TIMESTAMPTZ,
  duration_minutes INTEGER,
  interactions JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user_settings table for AI behavior and preferences
CREATE TABLE public.user_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  ai_personality JSONB DEFAULT '{}', -- tone, style, response patterns
  chat_settings JSONB DEFAULT '{}', -- auto-responses, greeting messages
  privacy_settings JSONB DEFAULT '{}', -- data collection preferences
  notification_settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.avatars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.visitors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.topics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.spaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.space_visits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Public profiles are viewable by everyone" 
  ON public.profiles FOR SELECT 
  USING (is_public = true);

CREATE POLICY "Users can view own profile" 
  ON public.profiles FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" 
  ON public.profiles FOR UPDATE 
  USING (auth.uid() = id);

-- RLS Policies for subscriptions
CREATE POLICY "Users can view own subscriptions" 
  ON public.subscriptions FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscriptions" 
  ON public.subscriptions FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own subscriptions" 
  ON public.subscriptions FOR UPDATE 
  USING (auth.uid() = user_id);

-- RLS Policies for avatars
CREATE POLICY "Users can manage own avatars" 
  ON public.avatars FOR ALL 
  USING (auth.uid() = user_id);

CREATE POLICY "Public avatars viewable by everyone" 
  ON public.avatars FOR SELECT 
  USING (user_id IN (SELECT id FROM public.profiles WHERE is_public = true));

-- RLS Policies for visitors
CREATE POLICY "Profile owners can view their visitors" 
  ON public.visitors FOR SELECT 
  USING (auth.uid() = profile_owner_id);

CREATE POLICY "Visitors can view own records" 
  ON public.visitors FOR SELECT 
  USING (auth.uid() = user_id);

-- RLS Policies for chats
CREATE POLICY "Users can view own chats" 
  ON public.chats FOR SELECT 
  USING (auth.uid() = user_id OR auth.uid() IN (SELECT user_id FROM public.visitors WHERE id = visitor_id));

CREATE POLICY "Users can create chats" 
  ON public.chats FOR INSERT 
  WITH CHECK (true); -- Allow chat creation for visitors

-- RLS Policies for chat_messages
CREATE POLICY "Chat participants can view messages" 
  ON public.chat_messages FOR SELECT 
  USING (chat_id IN (SELECT id FROM public.chats WHERE auth.uid() = user_id OR auth.uid() IN (SELECT user_id FROM public.visitors WHERE id = visitor_id)));

CREATE POLICY "Chat participants can send messages" 
  ON public.chat_messages FOR INSERT 
  WITH CHECK (chat_id IN (SELECT id FROM public.chats WHERE auth.uid() = user_id OR auth.uid() IN (SELECT user_id FROM public.visitors WHERE id = visitor_id)));

-- RLS Policies for topics
CREATE POLICY "Users can view own topics" 
  ON public.topics FOR SELECT 
  USING (auth.uid() = user_id);

-- RLS Policies for spaces
CREATE POLICY "Users can manage own spaces" 
  ON public.spaces FOR ALL 
  USING (auth.uid() = user_id);

CREATE POLICY "Public spaces viewable by everyone" 
  ON public.spaces FOR SELECT 
  USING (is_public = true);

-- RLS Policies for space_visits
CREATE POLICY "Space owners can view visits" 
  ON public.space_visits FOR SELECT 
  USING (space_id IN (SELECT id FROM public.spaces WHERE user_id = auth.uid()));

CREATE POLICY "Visitors can view own visits" 
  ON public.space_visits FOR SELECT 
  USING (visitor_id IN (SELECT id FROM public.visitors WHERE user_id = auth.uid()));

-- RLS Policies for user_settings
CREATE POLICY "Users can manage own settings" 
  ON public.user_settings FOR ALL 
  USING (auth.uid() = user_id);

-- Create function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, username, display_name, role)
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    'user'
  );
  
  -- Create default user settings
  INSERT INTO public.user_settings (user_id, ai_personality, chat_settings)
  VALUES (
    NEW.id,
    '{"tone": "friendly", "style": "conversational", "personality": "helpful"}',
    '{"auto_greeting": true, "response_delay": 1000}'
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Create function to update profile updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for updated_at columns
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON public.subscriptions
  FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

CREATE TRIGGER update_avatars_updated_at BEFORE UPDATE ON public.avatars
  FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

CREATE TRIGGER update_spaces_updated_at BEFORE UPDATE ON public.spaces
  FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON public.user_settings
  FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

-- Create indexes for performance
CREATE INDEX idx_profiles_username ON public.profiles(username);
CREATE INDEX idx_profiles_role ON public.profiles(role);
CREATE INDEX idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON public.subscriptions(status);
CREATE INDEX idx_avatars_user_id ON public.avatars(user_id);
CREATE INDEX idx_visitors_profile_owner_id ON public.visitors(profile_owner_id);
CREATE INDEX idx_chats_user_id ON public.chats(user_id);
CREATE INDEX idx_chats_visitor_id ON public.chats(visitor_id);
CREATE INDEX idx_chat_messages_chat_id ON public.chat_messages(chat_id);
CREATE INDEX idx_topics_user_id ON public.topics(user_id);
CREATE INDEX idx_topics_chat_id ON public.topics(chat_id);
CREATE INDEX idx_spaces_user_id ON public.spaces(user_id);
CREATE INDEX idx_space_visits_space_id ON public.space_visits(space_id);

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('avatars', 'avatars', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/webp']), -- 50MB limit
  ('user-content', 'user-content', true, 104857600, ARRAY['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'audio/mpeg']); -- 100MB limit

-- Storage policies
CREATE POLICY "Users can upload their own avatar content" 
  ON storage.objects FOR INSERT 
  WITH CHECK (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Avatar content is publicly accessible" 
  ON storage.objects FOR SELECT 
  USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own content" 
  ON storage.objects FOR INSERT 
  WITH CHECK (bucket_id = 'user-content' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "User content is publicly accessible" 
  ON storage.objects FOR SELECT 
  USING (bucket_id = 'user-content');

CREATE POLICY "Users can update their own content" 
  ON storage.objects FOR UPDATE 
  USING (bucket_id IN ('avatars', 'user-content') AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own content" 
  ON storage.objects FOR DELETE 
  USING (bucket_id IN ('avatars', 'user-content') AND auth.uid()::text = (storage.foldername(name))[1]);
