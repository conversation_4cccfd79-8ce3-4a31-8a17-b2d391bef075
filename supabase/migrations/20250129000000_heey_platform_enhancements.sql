-- Heey Platform Enhancements Migration
-- Adds missing tables and columns for full platform functionality

-- Add missing columns to existing profiles table for biolinks features
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS custom_theme JSONB DEFAULT '{}';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS page_views INTEGER DEFAULT 0;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS referral_code VARCHAR(20) UNIQUE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS referral_points INTEGER DEFAULT 0;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS total_referrals INTEGER DEFAULT 0;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS verification_status VARCHAR(20) DEFAULT 'pending';

-- Create referrals table for referral system
CREATE TABLE IF NOT EXISTS public.referrals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  referrer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  referred_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  referral_code VARCHAR(20) NOT NULL,
  points_awarded INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
  completion_date TIMESTAMPTZ,
  bonus_points INTEGER DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(referrer_id, referred_id), -- Prevent duplicate referrals
  CHECK (referrer_id != referred_id) -- Prevent self-referrals
);

-- Create rooms table for 3D environments
CREATE TABLE IF NOT EXISTS public.rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  glb_url TEXT NOT NULL,
  thumbnail_url TEXT,
  category VARCHAR(50) DEFAULT 'general',
  is_premium BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  popularity_score INTEGER DEFAULT 0,
  creator_id UUID REFERENCES profiles(id),
  download_count INTEGER DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0.0,
  tags TEXT[] DEFAULT '{}',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user_rooms table for user's selected rooms
CREATE TABLE IF NOT EXISTS public.user_rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  is_default BOOLEAN DEFAULT false,
  position JSONB DEFAULT '{"x": 0, "y": 0, "z": 0}',
  rotation JSONB DEFAULT '{"x": 0, "y": 0, "z": 0}',
  scale DECIMAL(3,2) DEFAULT 1.0,
  last_used_at TIMESTAMPTZ DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(user_id, room_id) -- User can only have one instance of each room
);

-- Create page_analytics table for visitor tracking
CREATE TABLE IF NOT EXISTS public.page_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  visitor_ip VARCHAR(45), -- Support IPv6
  visitor_country VARCHAR(3), -- ISO country code
  visitor_city VARCHAR(100),
  page_path VARCHAR(255),
  referrer_url TEXT,
  user_agent TEXT,
  session_id VARCHAR(255),
  interaction_type VARCHAR(50), -- 'page_view', 'avatar_interaction', 'link_click', etc.
  interaction_data JSONB DEFAULT '{}',
  duration_seconds INTEGER,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create chat_analytics table for conversation analytics
CREATE TABLE IF NOT EXISTS public.chat_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_id UUID REFERENCES chats(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  message_count INTEGER DEFAULT 0,
  avg_response_time_ms INTEGER,
  conversation_duration_minutes INTEGER,
  sentiment_score DECIMAL(3,2), -- -1.0 to 1.0
  engagement_score DECIMAL(3,2), -- 0.0 to 1.0
  topics_discussed TEXT[],
  user_satisfaction INTEGER CHECK (user_satisfaction BETWEEN 1 AND 5),
  ai_performance_score DECIMAL(3,2),
  language_detected VARCHAR(10),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL, -- 'referral_bonus', 'new_message', 'payment_success', etc.
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}',
  is_read BOOLEAN DEFAULT false,
  is_important BOOLEAN DEFAULT false,
  expires_at TIMESTAMPTZ,
  action_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create referral_notifications table specifically for referral events
CREATE TABLE IF NOT EXISTS public.referral_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  referrer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  referred_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  notification_type VARCHAR(50) NOT NULL, -- 'signup', 'first_purchase', 'milestone'
  points_earned INTEGER DEFAULT 0,
  milestone_level INTEGER,
  is_processed BOOLEAN DEFAULT false,
  processed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security on new tables
ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.page_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referral_notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for referrals
CREATE POLICY "Users can view their own referrals" 
  ON public.referrals FOR SELECT 
  USING (auth.uid() = referrer_id OR auth.uid() = referred_id);

CREATE POLICY "Users can create referrals" 
  ON public.referrals FOR INSERT 
  WITH CHECK (auth.uid() = referrer_id);

CREATE POLICY "Users can update their own referrals" 
  ON public.referrals FOR UPDATE 
  USING (auth.uid() = referrer_id);

-- RLS Policies for rooms
CREATE POLICY "Public rooms viewable by everyone" 
  ON public.rooms FOR SELECT 
  USING (is_active = true);

CREATE POLICY "Room creators can manage their rooms" 
  ON public.rooms FOR ALL 
  USING (auth.uid() = creator_id);

-- RLS Policies for user_rooms
CREATE POLICY "Users can manage their own room selections" 
  ON public.user_rooms FOR ALL 
  USING (auth.uid() = user_id);

-- RLS Policies for page_analytics
CREATE POLICY "Profile owners can view their analytics" 
  ON public.page_analytics FOR SELECT 
  USING (auth.uid() = profile_id);

CREATE POLICY "Anyone can insert analytics data" 
  ON public.page_analytics FOR INSERT 
  WITH CHECK (true); -- Allow anonymous analytics collection

-- RLS Policies for chat_analytics
CREATE POLICY "Users can view their own chat analytics" 
  ON public.chat_analytics FOR SELECT 
  USING (auth.uid() = user_id);

-- RLS Policies for notifications
CREATE POLICY "Users can view their own notifications" 
  ON public.notifications FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" 
  ON public.notifications FOR UPDATE 
  USING (auth.uid() = user_id);

-- RLS Policies for referral_notifications
CREATE POLICY "Users can view their referral notifications" 
  ON public.referral_notifications FOR SELECT 
  USING (auth.uid() = referrer_id);

-- Database Functions

-- Function to generate unique referral codes
CREATE OR REPLACE FUNCTION public.generate_referral_code(user_id UUID)
RETURNS TEXT AS $$
DECLARE
  code TEXT;
  attempts INTEGER := 0;
  max_attempts INTEGER := 10;
BEGIN
  LOOP
    -- Generate a random 8-character alphanumeric code
    code := upper(substring(encode(gen_random_bytes(6), 'base64') from 1 for 8));
    
    -- Check if code already exists
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE referral_code = code) THEN
      -- Update user's referral code
      UPDATE profiles SET referral_code = code WHERE id = user_id;
      RETURN code;
    END IF;
    
    attempts := attempts + 1;
    IF attempts >= max_attempts THEN
      RAISE EXCEPTION 'Could not generate unique referral code after % attempts', max_attempts;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process referral signup
CREATE OR REPLACE FUNCTION public.process_referral_signup(referral_code_input TEXT, new_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  referrer_user_id UUID;
  points_to_award INTEGER := 100; -- Base referral points
BEGIN
  -- Find the referrer
  SELECT id INTO referrer_user_id 
  FROM profiles 
  WHERE referral_code = referral_code_input;
  
  IF referrer_user_id IS NULL THEN
    RETURN FALSE; -- Invalid referral code
  END IF;
  
  -- Prevent self-referral
  IF referrer_user_id = new_user_id THEN
    RETURN FALSE;
  END IF;
  
  -- Create referral record
  INSERT INTO referrals (referrer_id, referred_id, referral_code, points_awarded, status)
  VALUES (referrer_user_id, new_user_id, referral_code_input, points_to_award, 'completed');
  
  -- Update referrer's points and stats
  UPDATE profiles 
  SET 
    referral_points = referral_points + points_to_award,
    total_referrals = total_referrals + 1
  WHERE id = referrer_user_id;
  
  -- Create notification for referrer
  INSERT INTO notifications (user_id, type, title, message, data)
  VALUES (
    referrer_user_id,
    'referral_bonus',
    'New Referral Bonus!',
    'You earned ' || points_to_award || ' points for referring a new user!',
    jsonb_build_object('points', points_to_award, 'referred_user', new_user_id)
  );
  
  -- Create referral notification
  INSERT INTO referral_notifications (referrer_id, referred_id, notification_type, points_earned)
  VALUES (referrer_user_id, new_user_id, 'signup', points_to_award);
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to track page analytics
CREATE OR REPLACE FUNCTION public.track_page_view(
  profile_id_input UUID,
  visitor_ip_input VARCHAR(45),
  page_path_input VARCHAR(255),
  referrer_url_input TEXT DEFAULT NULL,
  user_agent_input TEXT DEFAULT NULL,
  session_id_input VARCHAR(255) DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  -- Insert analytics record
  INSERT INTO page_analytics (
    profile_id, 
    visitor_ip, 
    page_path, 
    referrer_url, 
    user_agent, 
    session_id,
    interaction_type
  )
  VALUES (
    profile_id_input,
    visitor_ip_input,
    page_path_input,
    referrer_url_input,
    user_agent_input,
    session_id_input,
    'page_view'
  );
  
  -- Update profile page view count
  UPDATE profiles 
  SET page_views = page_views + 1 
  WHERE id = profile_id_input;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's room configuration
CREATE OR REPLACE FUNCTION public.get_user_room_config(user_id_input UUID)
RETURNS TABLE (
  room_id UUID,
  room_name VARCHAR(100),
  glb_url TEXT,
  thumbnail_url TEXT,
  is_default BOOLEAN,
  room_position JSONB,
  room_rotation JSONB,
  room_scale DECIMAL(3,2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    r.id,
    r.name,
    r.glb_url,
    r.thumbnail_url,
    ur.is_default,
    ur.position AS room_position,
    ur.rotation AS room_rotation,
    ur.scale AS room_scale
  FROM rooms r
  JOIN user_rooms ur ON r.id = ur.room_id
  WHERE ur.user_id = user_id_input AND r.is_active = true
  ORDER BY ur.is_default DESC, ur.last_used_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update room usage
CREATE OR REPLACE FUNCTION public.update_room_usage(user_id_input UUID, room_id_input UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE user_rooms 
  SET 
    last_used_at = NOW(),
    usage_count = usage_count + 1
  WHERE user_id = user_id_input AND room_id = room_id_input;
  
  UPDATE rooms 
  SET popularity_score = popularity_score + 1
  WHERE id = room_id_input;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup old analytics data (for GDPR compliance)
CREATE OR REPLACE FUNCTION public.cleanup_old_analytics()
RETURNS VOID AS $$
BEGIN
  -- Delete analytics data older than 2 years
  DELETE FROM page_analytics WHERE created_at < NOW() - INTERVAL '2 years';
  DELETE FROM chat_analytics WHERE created_at < NOW() - INTERVAL '2 years';
  
  -- Delete processed notifications older than 6 months
  DELETE FROM notifications WHERE is_read = true AND created_at < NOW() - INTERVAL '6 months';
  DELETE FROM referral_notifications WHERE is_processed = true AND created_at < NOW() - INTERVAL '6 months';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create Views for Analytics

-- User engagement summary view
CREATE OR REPLACE VIEW public.user_engagement_summary AS
SELECT 
  p.id,
  p.username,
  p.display_name,
  p.page_views,
  p.total_referrals,
  p.referral_points,
  COUNT(DISTINCT c.id) as total_chats,
  COUNT(DISTINCT cm.id) as total_messages,
  COUNT(DISTINCT ur.room_id) as rooms_used,
  AVG(ca.engagement_score) as avg_engagement_score,
  p.created_at as user_since
FROM profiles p
LEFT JOIN chats c ON p.id = c.user_id
LEFT JOIN chat_messages cm ON c.id = cm.chat_id
LEFT JOIN user_rooms ur ON p.id = ur.user_id
LEFT JOIN chat_analytics ca ON c.id = ca.chat_id
GROUP BY p.id, p.username, p.display_name, p.page_views, p.total_referrals, p.referral_points, p.created_at;

-- Popular rooms view
CREATE OR REPLACE VIEW public.popular_rooms AS
SELECT 
  r.*,
  COUNT(DISTINCT ur.user_id) as users_count,
  AVG(ur.usage_count) as avg_usage_per_user,
  p.display_name as creator_name
FROM rooms r
LEFT JOIN user_rooms ur ON r.id = ur.room_id
LEFT JOIN profiles p ON r.creator_id = p.id
WHERE r.is_active = true
GROUP BY r.id, p.display_name
ORDER BY r.popularity_score DESC, users_count DESC;

-- Add triggers for updated_at columns on new tables
CREATE TRIGGER update_referrals_updated_at BEFORE UPDATE ON public.referrals
  FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

CREATE TRIGGER update_rooms_updated_at BEFORE UPDATE ON public.rooms
  FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_referrals_referrer_id ON public.referrals(referrer_id);
CREATE INDEX IF NOT EXISTS idx_referrals_referred_id ON public.referrals(referred_id);
CREATE INDEX IF NOT EXISTS idx_referrals_code ON public.referrals(referral_code);
CREATE INDEX IF NOT EXISTS idx_referrals_status ON public.referrals(status);

CREATE INDEX IF NOT EXISTS idx_rooms_category ON public.rooms(category);
CREATE INDEX IF NOT EXISTS idx_rooms_is_premium ON public.rooms(is_premium);
CREATE INDEX IF NOT EXISTS idx_rooms_is_active ON public.rooms(is_active);
CREATE INDEX IF NOT EXISTS idx_rooms_popularity ON public.rooms(popularity_score DESC);

CREATE INDEX IF NOT EXISTS idx_user_rooms_user_id ON public.user_rooms(user_id);
CREATE INDEX IF NOT EXISTS idx_user_rooms_room_id ON public.user_rooms(room_id);
CREATE INDEX IF NOT EXISTS idx_user_rooms_default ON public.user_rooms(is_default);

CREATE INDEX IF NOT EXISTS idx_page_analytics_profile_id ON public.page_analytics(profile_id);
CREATE INDEX IF NOT EXISTS idx_page_analytics_created_at ON public.page_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_page_analytics_interaction_type ON public.page_analytics(interaction_type);

CREATE INDEX IF NOT EXISTS idx_chat_analytics_user_id ON public.chat_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_analytics_chat_id ON public.chat_analytics(chat_id);

CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON public.notifications(type);

CREATE INDEX IF NOT EXISTS idx_referral_notifications_referrer_id ON public.referral_notifications(referrer_id);
CREATE INDEX IF NOT EXISTS idx_referral_notifications_is_processed ON public.referral_notifications(is_processed);

-- Insert default rooms data
INSERT INTO public.rooms (name, description, glb_url, thumbnail_url, category, is_premium, is_active) VALUES
  ('Cozy Studio', 'A warm and inviting studio apartment perfect for casual conversations', '/assets/models/cozy_studio.glb', '/assets/thumbnails/cozy_studio.webp', 'interior', false, true),
  ('Modern Office', 'Professional office space ideal for business meetings', '/assets/models/modern_office.glb', '/assets/thumbnails/modern_office.webp', 'office', true, true),
  ('Garden Terrace', 'Beautiful outdoor terrace with lush greenery', '/assets/models/garden_terrace.glb', '/assets/thumbnails/garden_terrace.webp', 'outdoor', false, true),
  ('Cyberpunk City', 'Futuristic cityscape with neon lights', '/assets/models/cyberpunk_city.glb', '/assets/thumbnails/cyberpunk_city.webp', 'futuristic', true, true),
  ('Beach Paradise', 'Tropical beach setting with crystal clear waters', '/assets/models/beach_paradise.glb', '/assets/thumbnails/beach_paradise.webp', 'outdoor', false, true),
  ('Art Gallery', 'Elegant gallery space for creative discussions', '/assets/models/art_gallery.glb', '/assets/thumbnails/art_gallery.webp', 'interior', true, true)
ON CONFLICT DO NOTHING;

-- Update existing profiles to have referral codes
UPDATE profiles 
SET referral_code = upper(substring(encode(gen_random_bytes(6), 'base64') from 1 for 8))
WHERE referral_code IS NULL;

-- Ensure referral codes are unique (fix any duplicates)
WITH duplicate_codes AS (
  SELECT referral_code, array_agg(id) as profile_ids
  FROM profiles 
  WHERE referral_code IS NOT NULL
  GROUP BY referral_code 
  HAVING count(*) > 1
)
UPDATE profiles 
SET referral_code = upper(substring(encode(gen_random_bytes(6), 'base64') from 1 for 8))
WHERE id IN (
  SELECT unnest(profile_ids[2:]) 
  FROM duplicate_codes
);