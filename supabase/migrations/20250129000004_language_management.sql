-- Language Management Enhancement
-- Add support for advanced internationalization features

-- Add preferred language to profiles
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS preferred_language VARCHAR(10) DEFAULT 'en';

-- Language configurations table
CREATE TABLE IF NOT EXISTS language_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(10) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  native_name VARCHAR(100) NOT NULL,
  flag VARCHAR(10),
  direction VARCHAR(3) DEFAULT 'ltr' CHECK (direction IN ('ltr', 'rtl')),
  region VARCHAR(10),
  fallback VARCHAR(10),
  completeness INTEGER DEFAULT 0 CHECK (completeness >= 0 AND completeness <= 100),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Translations table for dynamic content
CREATE TABLE IF NOT EXISTS translations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  language_code VARCHAR(10) NOT NULL,
  key VARCHAR(500) NOT NULL,
  value TEXT NOT NULL,
  context TEXT,
  interpolations TEXT[],
  pluralization JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(language_code, key)
);

-- Translation management logs
CREATE TABLE IF NOT EXISTS translation_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  language_code VARCHAR(10) NOT NULL,
  translation_key VARCHAR(500) NOT NULL,
  old_value TEXT,
  new_value TEXT,
  action VARCHAR(20) NOT NULL CHECK (action IN ('create', 'update', 'delete')),
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Language detection logs
CREATE TABLE IF NOT EXISTS language_detection_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  detected_language VARCHAR(10),
  confidence DECIMAL(3,2),
  source VARCHAR(20) CHECK (source IN ('browser', 'user_preference', 'geolocation', 'fallback')),
  browser_languages TEXT[],
  user_agent TEXT,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Insert default language configurations
INSERT INTO language_configs (code, name, native_name, flag, direction, completeness, is_active) VALUES
  ('en', 'English', 'English', '🇺🇸', 'ltr', 100, true),
  ('es', 'Spanish', 'Español', '🇪🇸', 'ltr', 85, true),
  ('ar', 'Arabic', 'العربية', '🇸🇦', 'rtl', 70, true),
  ('zh', 'Chinese', '中文', '🇨🇳', 'ltr', 90, true),
  ('fr', 'French', 'Français', '🇫🇷', 'ltr', 80, true),
  ('de', 'German', 'Deutsch', '🇩🇪', 'ltr', 75, true),
  ('it', 'Italian', 'Italiano', '🇮🇹', 'ltr', 70, true),
  ('pt', 'Portuguese', 'Português', '🇵🇹', 'ltr', 65, true),
  ('ru', 'Russian', 'Русский', '🇷🇺', 'ltr', 60, true),
  ('ja', 'Japanese', '日本語', '🇯🇵', 'ltr', 85, true),
  ('ko', 'Korean', '한국어', '🇰🇷', 'ltr', 80, true),
  ('hi', 'Hindi', 'हिन्दी', '🇮🇳', 'ltr', 50, true)
ON CONFLICT (code) DO UPDATE SET
  name = EXCLUDED.name,
  native_name = EXCLUDED.native_name,
  flag = EXCLUDED.flag,
  direction = EXCLUDED.direction,
  completeness = EXCLUDED.completeness,
  is_active = EXCLUDED.is_active,
  updated_at = now();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_translations_language_code ON translations(language_code);
CREATE INDEX IF NOT EXISTS idx_translations_key ON translations(key);
CREATE INDEX IF NOT EXISTS idx_translations_language_key ON translations(language_code, key);
CREATE INDEX IF NOT EXISTS idx_language_configs_active ON language_configs(is_active);
CREATE INDEX IF NOT EXISTS idx_translation_logs_language_code ON translation_logs(language_code);
CREATE INDEX IF NOT EXISTS idx_translation_logs_created_at ON translation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_language_detection_logs_user_id ON language_detection_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_preferred_language ON profiles(preferred_language);

-- RLS policies for language configurations (read-only for most users)
ALTER TABLE language_configs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Language configs are readable by everyone" ON language_configs
  FOR SELECT USING (true);

CREATE POLICY "Language configs are updatable by admin users" ON language_configs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- RLS policies for translations
ALTER TABLE translations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Translations are readable by everyone" ON translations
  FOR SELECT USING (true);

CREATE POLICY "Translations are manageable by admin users" ON translations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- RLS policies for translation logs
ALTER TABLE translation_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Translation logs are readable by admin users" ON translation_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Translation logs are insertable by admin users" ON translation_logs
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- RLS policies for language detection logs
ALTER TABLE language_detection_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Language detection logs are readable by admin users" ON language_detection_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Users can insert their own language detection logs" ON language_detection_logs
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Functions for translation management
CREATE OR REPLACE FUNCTION update_translation_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_language_configs_timestamp
  BEFORE UPDATE ON language_configs
  FOR EACH ROW
  EXECUTE FUNCTION update_translation_timestamp();

CREATE TRIGGER update_translations_timestamp
  BEFORE UPDATE ON translations
  FOR EACH ROW
  EXECUTE FUNCTION update_translation_timestamp();

-- Function to log translation changes
CREATE OR REPLACE FUNCTION log_translation_change()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO translation_logs (language_code, translation_key, new_value, action, user_id)
    VALUES (NEW.language_code, NEW.key, NEW.value, 'create', auth.uid());
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO translation_logs (language_code, translation_key, old_value, new_value, action, user_id)
    VALUES (NEW.language_code, NEW.key, OLD.value, NEW.value, 'update', auth.uid());
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO translation_logs (language_code, translation_key, old_value, action, user_id)
    VALUES (OLD.language_code, OLD.key, OLD.value, 'delete', auth.uid());
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger for translation change logging
CREATE TRIGGER log_translation_changes
  AFTER INSERT OR UPDATE OR DELETE ON translations
  FOR EACH ROW
  EXECUTE FUNCTION log_translation_change();

-- Function to get translation statistics
CREATE OR REPLACE FUNCTION get_translation_stats(target_language_code TEXT)
RETURNS TABLE (
  total_keys BIGINT,
  translated_keys BIGINT,
  missing_keys BIGINT,
  completeness_percentage NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  WITH reference_keys AS (
    SELECT DISTINCT key FROM translations WHERE language_code = 'en'
  ),
  target_keys AS (
    SELECT DISTINCT key FROM translations WHERE language_code = target_language_code
  )
  SELECT 
    (SELECT COUNT(*) FROM reference_keys) as total_keys,
    (SELECT COUNT(*) FROM target_keys) as translated_keys,
    (SELECT COUNT(*) FROM reference_keys WHERE key NOT IN (SELECT key FROM target_keys)) as missing_keys,
    CASE 
      WHEN (SELECT COUNT(*) FROM reference_keys) > 0 
      THEN ROUND((SELECT COUNT(*) FROM target_keys)::NUMERIC / (SELECT COUNT(*) FROM reference_keys)::NUMERIC * 100, 2)
      ELSE 0
    END as completeness_percentage;
END;
$$ LANGUAGE plpgsql;

-- Function to get missing translation keys
CREATE OR REPLACE FUNCTION get_missing_translation_keys(target_language_code TEXT)
RETURNS TABLE (missing_key TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT t.key as missing_key
  FROM translations t
  WHERE t.language_code = 'en'
    AND t.key NOT IN (
      SELECT key FROM translations WHERE language_code = target_language_code
    )
  ORDER BY missing_key;
END;
$$ LANGUAGE plpgsql;