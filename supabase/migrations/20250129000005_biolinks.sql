-- Biolinks schema: pages, links, templates, and link analytics
-- Creates tables referenced by src/lib/biolinksService.ts

-- Enable extensions required
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- bio_pages: one public page per user with username
CREATE TABLE IF NOT EXISTS public.bio_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  username TEXT UNIQUE NOT NULL,
  display_name TEXT NOT NULL,
  bio TEXT,
  theme JSONB DEFAULT '{}',
  custom_theme JSONB,
  seo_config JSON<PERSON>,
  analytics_config JSON<PERSON>,
  social_links JSONB,
  is_public BOOLEAN DEFAULT TRUE,
  is_verified BOOLEAN DEFAULT FALSE,
  page_views INTEGER DEFAULT 0,
  total_clicks INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- bio_links: individual links on a user's page
CREATE TABLE IF NOT EXISTS public.bio_links (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  url TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  custom_icon_url TEXT,
  position INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT TRUE,
  click_count INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  style_config JSONB,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- page_templates: reusable templates for biolinks pages
CREATE TABLE IF NOT EXISTS public.page_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  category TEXT,
  theme JSONB NOT NULL,
  default_links JSONB, -- array of { title, url, icon }
  usage_count INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- link_analytics: daily aggregates per link
CREATE TABLE IF NOT EXISTS public.link_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  link_id UUID REFERENCES public.bio_links(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  clicks INTEGER DEFAULT 0,
  unique_clicks INTEGER DEFAULT 0,
  ctr NUMERIC(5,2) DEFAULT 0,
  referrer_data JSONB DEFAULT '[]',
  device_data JSONB DEFAULT '[]',
  geographic_data JSONB DEFAULT '[]',
  time_distribution JSONB DEFAULT '[]',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE (link_id, date)
);

-- Helpful indexes
CREATE INDEX IF NOT EXISTS idx_bio_pages_user ON public.bio_pages(user_id);
CREATE INDEX IF NOT EXISTS idx_bio_links_user ON public.bio_links(user_id);
CREATE INDEX IF NOT EXISTS idx_bio_links_active ON public.bio_links(user_id, is_active, position);
CREATE INDEX IF NOT EXISTS idx_link_analytics_link_date ON public.link_analytics(link_id, date);

-- Row Level Security
ALTER TABLE public.bio_pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bio_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.page_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.link_analytics ENABLE ROW LEVEL SECURITY;

-- Policies: allow owners to manage their pages/links; public read for pages and links of public users
DO $$
BEGIN
  -- bio_pages
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'bio_pages' AND policyname = 'Public read public pages'
  ) THEN
    CREATE POLICY "Public read public pages" ON public.bio_pages
      FOR SELECT USING (is_public = TRUE);
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'bio_pages' AND policyname = 'Owners full access'
  ) THEN
    CREATE POLICY "Owners full access" ON public.bio_pages
      USING (auth.uid() = user_id)
      WITH CHECK (auth.uid() = user_id);
  END IF;

  -- bio_links
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'bio_links' AND policyname = 'Public read active links'
  ) THEN
    CREATE POLICY "Public read active links" ON public.bio_links
      FOR SELECT USING (is_active = TRUE);
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'bio_links' AND policyname = 'Owners full access links'
  ) THEN
    CREATE POLICY "Owners full access links" ON public.bio_links
      USING (auth.uid() = user_id)
      WITH CHECK (auth.uid() = user_id);
  END IF;

  -- page_templates (read-only public)
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'page_templates' AND policyname = 'Public read templates'
  ) THEN
    CREATE POLICY "Public read templates" ON public.page_templates FOR SELECT USING (TRUE);
  END IF;

  -- link_analytics (owner read/write)
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'link_analytics' AND policyname = 'Owner read write analytics'
  ) THEN
    CREATE POLICY "Owner read write analytics" ON public.link_analytics
      USING (auth.uid() = user_id)
      WITH CHECK (auth.uid() = user_id);
  END IF;
END $$;


