-- Chat Topics and Analytics Enhancement
-- Adds AI-powered topic extraction and advanced analytics

-- Function to extract and store topics from chat messages
CREATE OR REPLACE FUNCTION public.extract_chat_topics(
  chat_session_id UUID,
  topics_data JSONB
)
RETURNS VOID AS $$
DECLARE
  topic_item JSONB;
  chat_user_id UUID;
BEGIN
  -- Get the user_id for this chat
  SELECT user_id INTO chat_user_id FROM chats WHERE id = chat_session_id;
  
  IF chat_user_id IS NULL THEN
    RAISE EXCEPTION 'Chat session not found: %', chat_session_id;
  END IF;
  
  -- Clear existing topics for this chat
  DELETE FROM topics WHERE chat_id = chat_session_id;
  
  -- Insert new topics
  FOR topic_item IN SELECT * FROM jsonb_array_elements(topics_data)
  LOOP
    INSERT INTO topics (
      chat_id,
      user_id,
      topic,
      sentiment,
      confidence,
      keywords,
      summary,
      metadata
    )
    VALUES (
      chat_session_id,
      chat_user_id,
      topic_item->>'topic',
      topic_item->>'sentiment',
      COALESCE((topic_item->>'confidence')::REAL, 0.0),
      CASE 
        WHEN topic_item->'keywords' IS NOT NULL THEN
          ARRAY(SELECT jsonb_array_elements_text(topic_item->'keywords'))
        ELSE
          '{}'::TEXT[]
      END,
      topic_item->>'summary',
      COALESCE(topic_item->'metadata', '{}'::jsonb)
    );
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get topic analytics for a user
CREATE OR REPLACE FUNCTION public.get_user_topic_analytics(
  user_id_input UUID,
  date_from TIMESTAMPTZ DEFAULT NULL,
  date_to TIMESTAMPTZ DEFAULT NULL
)
RETURNS TABLE (
  topic TEXT,
  mention_count INTEGER,
  avg_sentiment DECIMAL(3,2),
  avg_confidence DECIMAL(3,2),
  most_common_keywords TEXT[],
  latest_mention TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.topic,
    COUNT(*)::INTEGER as mention_count,
    AVG(
      CASE t.sentiment
        WHEN 'positive' THEN 1.0
        WHEN 'neutral' THEN 0.0
        WHEN 'negative' THEN -1.0
        ELSE 0.0
      END
    )::DECIMAL(3,2) as avg_sentiment,
    AVG(t.confidence)::DECIMAL(3,2) as avg_confidence,
    ARRAY(
      SELECT unnest(t.keywords)
      FROM topics t2
      WHERE t2.topic = t.topic AND t2.user_id = user_id_input
      GROUP BY unnest(t.keywords)
      ORDER BY COUNT(*) DESC
      LIMIT 5
    ) as most_common_keywords,
    MAX(t.created_at) as latest_mention
  FROM topics t
  WHERE t.user_id = user_id_input
    AND (date_from IS NULL OR t.created_at >= date_from)
    AND (date_to IS NULL OR t.created_at <= date_to)
  GROUP BY t.topic
  ORDER BY mention_count DESC, latest_mention DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get conversation insights
CREATE OR REPLACE FUNCTION public.get_conversation_insights(
  user_id_input UUID,
  days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
  total_conversations INTEGER,
  total_messages INTEGER,
  avg_conversation_length DECIMAL(5,2),
  avg_response_time_minutes DECIMAL(5,2),
  most_active_hour INTEGER,
  top_topics TEXT[],
  sentiment_breakdown JSONB,
  visitor_retention_rate DECIMAL(5,2)
) AS $$
DECLARE
  start_date TIMESTAMPTZ;
  insights_record RECORD;
BEGIN
  start_date := NOW() - (days_back || ' days')::INTERVAL;
  
  -- Get conversation metrics
  SELECT 
    COUNT(DISTINCT c.id)::INTEGER as conv_count,
    COUNT(cm.id)::INTEGER as msg_count,
    AVG(c.total_messages)::DECIMAL(5,2) as avg_length,
    AVG(EXTRACT(EPOCH FROM (c.ended_at - c.started_at)) / 60)::DECIMAL(5,2) as avg_duration,
    MODE() WITHIN GROUP (ORDER BY EXTRACT(HOUR FROM c.started_at))::INTEGER as active_hour
  INTO insights_record
  FROM chats c
  LEFT JOIN chat_messages cm ON c.id = cm.chat_id
  WHERE c.user_id = user_id_input 
    AND c.created_at >= start_date;
  
  RETURN QUERY
  SELECT 
    insights_record.conv_count,
    insights_record.msg_count,
    insights_record.avg_length,
    insights_record.avg_duration,
    insights_record.active_hour,
    -- Top 5 topics
    ARRAY(
      SELECT t.topic
      FROM topics t
      JOIN chats c ON t.chat_id = c.id
      WHERE c.user_id = user_id_input AND t.created_at >= start_date
      GROUP BY t.topic
      ORDER BY COUNT(*) DESC
      LIMIT 5
    ),
    -- Sentiment breakdown
    (
      SELECT jsonb_build_object(
        'positive', COUNT(*) FILTER (WHERE sentiment = 'positive'),
        'neutral', COUNT(*) FILTER (WHERE sentiment = 'neutral'),
        'negative', COUNT(*) FILTER (WHERE sentiment = 'negative')
      )
      FROM topics t
      JOIN chats c ON t.chat_id = c.id
      WHERE c.user_id = user_id_input AND t.created_at >= start_date
    ),
    -- Visitor retention rate (visitors who came back)
    (
      SELECT 
        CASE 
          WHEN COUNT(DISTINCT v.id) > 0 THEN
            (COUNT(DISTINCT v.id) FILTER (WHERE v.total_visits > 1) * 100.0 / COUNT(DISTINCT v.id))::DECIMAL(5,2)
          ELSE 0.0
        END
      FROM visitors v
      WHERE v.profile_owner_id = user_id_input 
        AND v.first_visit_at >= start_date
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update conversation analytics
CREATE OR REPLACE FUNCTION public.update_conversation_analytics(
  chat_session_id UUID,
  analytics_data JSONB
)
RETURNS VOID AS $$
DECLARE
  chat_user_id UUID;
  message_count INTEGER;
  duration_minutes INTEGER;
BEGIN
  -- Get chat details
  SELECT 
    c.user_id,
    COUNT(cm.id),
    EXTRACT(EPOCH FROM (COALESCE(c.ended_at, NOW()) - c.started_at)) / 60
  INTO chat_user_id, message_count, duration_minutes
  FROM chats c
  LEFT JOIN chat_messages cm ON c.id = cm.chat_id
  WHERE c.id = chat_session_id
  GROUP BY c.id, c.user_id, c.started_at, c.ended_at;
  
  IF chat_user_id IS NULL THEN
    RAISE EXCEPTION 'Chat session not found: %', chat_session_id;
  END IF;
  
  -- Insert or update analytics
  INSERT INTO chat_analytics (
    chat_id,
    user_id,
    message_count,
    conversation_duration_minutes,
    sentiment_score,
    engagement_score,
    topics_discussed,
    language_detected,
    ai_performance_score
  )
  VALUES (
    chat_session_id,
    chat_user_id,
    message_count,
    duration_minutes,
    COALESCE((analytics_data->>'sentiment_score')::DECIMAL(3,2), 0.0),
    COALESCE((analytics_data->>'engagement_score')::DECIMAL(3,2), 0.0),
    CASE 
      WHEN analytics_data->'topics_discussed' IS NOT NULL THEN
        ARRAY(SELECT jsonb_array_elements_text(analytics_data->'topics_discussed'))
      ELSE
        '{}'::TEXT[]
    END,
    analytics_data->>'language_detected',
    COALESCE((analytics_data->>'ai_performance_score')::DECIMAL(3,2), 0.0)
  )
  ON CONFLICT (chat_id)
  DO UPDATE SET
    sentiment_score = EXCLUDED.sentiment_score,
    engagement_score = EXCLUDED.engagement_score,
    topics_discussed = EXCLUDED.topics_discussed,
    language_detected = EXCLUDED.language_detected,
    ai_performance_score = EXCLUDED.ai_performance_score;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get trending topics across all users
CREATE OR REPLACE FUNCTION public.get_trending_topics(
  days_back INTEGER DEFAULT 7,
  limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
  topic TEXT,
  mention_count INTEGER,
  growth_rate DECIMAL(5,2),
  avg_sentiment DECIMAL(3,2)
) AS $$
DECLARE
  start_date TIMESTAMPTZ;
  comparison_date TIMESTAMPTZ;
BEGIN
  start_date := NOW() - (days_back || ' days')::INTERVAL;
  comparison_date := start_date - (days_back || ' days')::INTERVAL;
  
  RETURN QUERY
  WITH current_period AS (
    SELECT 
      t.topic,
      COUNT(*)::INTEGER as current_mentions,
      AVG(
        CASE t.sentiment
          WHEN 'positive' THEN 1.0
          WHEN 'neutral' THEN 0.0
          WHEN 'negative' THEN -1.0
          ELSE 0.0
        END
      )::DECIMAL(3,2) as sentiment_score
    FROM topics t
    WHERE t.created_at >= start_date
    GROUP BY t.topic
  ),
  previous_period AS (
    SELECT 
      t.topic,
      COUNT(*)::INTEGER as previous_mentions
    FROM topics t
    WHERE t.created_at >= comparison_date AND t.created_at < start_date
    GROUP BY t.topic
  )
  SELECT 
    cp.topic,
    cp.current_mentions,
    CASE 
      WHEN pp.previous_mentions > 0 THEN
        ((cp.current_mentions - pp.previous_mentions) * 100.0 / pp.previous_mentions)::DECIMAL(5,2)
      ELSE 
        100.0
    END as growth_rate,
    cp.sentiment_score
  FROM current_period cp
  LEFT JOIN previous_period pp ON cp.topic = pp.topic
  WHERE cp.current_mentions >= 2 -- Minimum threshold
  ORDER BY cp.current_mentions DESC, growth_rate DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to generate conversation summary
CREATE OR REPLACE FUNCTION public.generate_conversation_summary(
  chat_session_id UUID
)
RETURNS TABLE (
  chat_id UUID,
  participant_count INTEGER,
  message_count INTEGER,
  duration_minutes INTEGER,
  main_topics TEXT[],
  overall_sentiment TEXT,
  key_moments JSONB
) AS $$
DECLARE
  summary_record RECORD;
BEGIN
  -- Get basic chat metrics
  SELECT 
    c.id,
    2 as participants, -- user + visitor
    COUNT(cm.id)::INTEGER as messages,
    EXTRACT(EPOCH FROM (COALESCE(c.ended_at, NOW()) - c.started_at))::INTEGER / 60 as duration
  INTO summary_record
  FROM chats c
  LEFT JOIN chat_messages cm ON c.id = cm.chat_id
  WHERE c.id = chat_session_id
  GROUP BY c.id, c.started_at, c.ended_at;
  
  IF summary_record.id IS NULL THEN
    RAISE EXCEPTION 'Chat session not found: %', chat_session_id;
  END IF;
  
  RETURN QUERY
  SELECT 
    summary_record.id,
    summary_record.participants,
    summary_record.messages,
    summary_record.duration,
    -- Main topics from this conversation
    ARRAY(
      SELECT t.topic
      FROM topics t
      WHERE t.chat_id = chat_session_id
      ORDER BY t.confidence DESC
      LIMIT 3
    ),
    -- Overall sentiment
    (
      SELECT 
        CASE 
          WHEN AVG(
            CASE t.sentiment
              WHEN 'positive' THEN 1.0
              WHEN 'neutral' THEN 0.0
              WHEN 'negative' THEN -1.0
              ELSE 0.0
            END
          ) > 0.2 THEN 'positive'
          WHEN AVG(
            CASE t.sentiment
              WHEN 'positive' THEN 1.0
              WHEN 'neutral' THEN 0.0
              WHEN 'negative' THEN -1.0
              ELSE 0.0
            END
          ) < -0.2 THEN 'negative'
          ELSE 'neutral'
        END
      FROM topics t
      WHERE t.chat_id = chat_session_id
    ),
    -- Key moments (first and last messages)
    (
      SELECT jsonb_build_object(
        'first_message', (
          SELECT jsonb_build_object(
            'content', cm.content,
            'timestamp', cm.created_at,
            'sender', cm.sender_type
          )
          FROM chat_messages cm
          WHERE cm.chat_id = chat_session_id
          ORDER BY cm.created_at ASC
          LIMIT 1
        ),
        'last_message', (
          SELECT jsonb_build_object(
            'content', cm.content,
            'timestamp', cm.created_at,
            'sender', cm.sender_type
          )
          FROM chat_messages cm
          WHERE cm.chat_id = chat_session_id
          ORDER BY cm.created_at DESC
          LIMIT 1
        )
      )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create additional indexes for analytics performance
CREATE INDEX IF NOT EXISTS idx_topics_topic ON public.topics(topic);
CREATE INDEX IF NOT EXISTS idx_topics_sentiment ON public.topics(sentiment);
CREATE INDEX IF NOT EXISTS idx_topics_confidence ON public.topics(confidence);
CREATE INDEX IF NOT EXISTS idx_topics_created_at ON public.topics(created_at);
CREATE INDEX IF NOT EXISTS idx_topics_user_topic ON public.topics(user_id, topic);

CREATE INDEX IF NOT EXISTS idx_chat_analytics_sentiment ON public.chat_analytics(sentiment_score);
CREATE INDEX IF NOT EXISTS idx_chat_analytics_engagement ON public.chat_analytics(engagement_score);
CREATE INDEX IF NOT EXISTS idx_chat_analytics_language ON public.chat_analytics(language_detected);

-- Create GIN index for keyword searches
CREATE INDEX IF NOT EXISTS idx_topics_keywords ON public.topics USING GIN(keywords);
CREATE INDEX IF NOT EXISTS idx_chat_analytics_topics ON public.chat_analytics USING GIN(topics_discussed);