-- Referral System Notifications and Advanced Features
-- Adds comprehensive referral tracking and notification system

-- Function to process referral milestone rewards
CREATE OR REPLACE FUNCTION public.process_referral_milestones(referrer_user_id UUID)
RETURNS VOID AS $$
DECLARE
  current_referrals INTEGER;
  milestone_rewards JSONB;
  milestone_level INTEGER;
  bonus_points INTEGER;
BEGIN
  -- Get current referral count
  SELECT total_referrals INTO current_referrals
  FROM profiles
  WHERE id = referrer_user_id;
  
  -- Define milestone rewards
  milestone_rewards := '{
    "5": {"points": 250, "title": "Rising Influencer"},
    "10": {"points": 500, "title": "Community Builder"},
    "25": {"points": 1000, "title": "Super Referrer"},
    "50": {"points": 2500, "title": "Referral Master"},
    "100": {"points": 5000, "title": "Legendary Influencer"}
  }'::jsonb;
  
  -- Check for milestone achievements
  FOR milestone_level IN SELECT key::INTEGER FROM jsonb_object_keys(milestone_rewards) AS key
  LOOP
    IF current_referrals >= milestone_level THEN
      -- Check if milestone already processed
      IF NOT EXISTS (
        SELECT 1 FROM referral_notifications 
        WHERE referrer_id = referrer_user_id 
          AND notification_type = 'milestone'
          AND milestone_level = milestone_level
          AND is_processed = true
      ) THEN
        -- Award milestone bonus
        bonus_points := (milestone_rewards->(milestone_level::text)->>'points')::INTEGER;
        
        UPDATE profiles 
        SET referral_points = referral_points + bonus_points
        WHERE id = referrer_user_id;
        
        -- Create milestone notification
        INSERT INTO notifications (
          user_id,
          type,
          title,
          message,
          data,
          is_important
        )
        VALUES (
          referrer_user_id,
          'referral_milestone',
          'Referral Milestone Achieved! 🎉',
          'Congratulations! You''ve reached ' || milestone_level || ' referrals and earned ' || bonus_points || ' bonus points!',
          jsonb_build_object(
            'milestone_level', milestone_level,
            'bonus_points', bonus_points,
            'title', milestone_rewards->(milestone_level::text)->>'title'
          ),
          true
        );
        
        -- Record milestone notification
        INSERT INTO referral_notifications (
          referrer_id,
          notification_type,
          points_earned,
          milestone_level,
          is_processed,
          processed_at
        )
        VALUES (
          referrer_user_id,
          'milestone',
          bonus_points,
          milestone_level,
          true,
          NOW()
        );
      END IF;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get referral leaderboard
CREATE OR REPLACE FUNCTION public.get_referral_leaderboard(
  limit_count INTEGER DEFAULT 10,
  time_period VARCHAR(20) DEFAULT 'all_time'
)
RETURNS TABLE (
  rank INTEGER,
  user_id UUID,
  username TEXT,
  display_name TEXT,
  avatar_thumbnail TEXT,
  total_referrals INTEGER,
  referral_points INTEGER,
  recent_referrals INTEGER
) AS $$
DECLARE
  date_filter TIMESTAMPTZ;
BEGIN
  -- Set date filter based on time period
  CASE time_period
    WHEN 'week' THEN date_filter := NOW() - INTERVAL '7 days';
    WHEN 'month' THEN date_filter := NOW() - INTERVAL '30 days';
    WHEN 'quarter' THEN date_filter := NOW() - INTERVAL '90 days';
    WHEN 'year' THEN date_filter := NOW() - INTERVAL '365 days';
    ELSE date_filter := '1970-01-01'::TIMESTAMPTZ; -- All time
  END CASE;
  
  RETURN QUERY
  WITH referral_stats AS (
    SELECT 
      p.id,
      p.username,
      p.display_name,
      p.avatar_thumbnail,
      p.total_referrals,
      p.referral_points,
      COUNT(r.id) FILTER (WHERE r.created_at >= date_filter)::INTEGER as recent_count
    FROM profiles p
    LEFT JOIN referrals r ON p.id = r.referrer_id AND r.status = 'completed'
    WHERE p.total_referrals > 0
    GROUP BY p.id, p.username, p.display_name, p.avatar_thumbnail, p.total_referrals, p.referral_points
  )
  SELECT 
    ROW_NUMBER() OVER (ORDER BY rs.referral_points DESC, rs.total_referrals DESC)::INTEGER,
    rs.id,
    rs.username,
    rs.display_name,
    rs.avatar_thumbnail,
    rs.total_referrals,
    rs.referral_points,
    rs.recent_count
  FROM referral_stats rs
  ORDER BY rs.referral_points DESC, rs.total_referrals DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get referral analytics for a user
CREATE OR REPLACE FUNCTION public.get_user_referral_analytics(user_id_input UUID)
RETURNS TABLE (
  total_referrals INTEGER,
  successful_referrals INTEGER,
  pending_referrals INTEGER,
  total_points_earned INTEGER,
  referral_conversion_rate DECIMAL(5,2),
  avg_points_per_referral DECIMAL(5,2),
  referrals_this_month INTEGER,
  points_this_month INTEGER,
  current_rank INTEGER,
  next_milestone INTEGER,
  points_to_next_milestone INTEGER
) AS $$
DECLARE
  user_stats RECORD;
  user_rank INTEGER;
  next_milestone_level INTEGER;
BEGIN
  -- Get user referral stats
  SELECT 
    p.total_referrals,
    p.referral_points,
    COUNT(r.id) FILTER (WHERE r.status = 'completed')::INTEGER as successful,
    COUNT(r.id) FILTER (WHERE r.status = 'pending')::INTEGER as pending,
    COUNT(r.id) FILTER (WHERE r.created_at >= DATE_TRUNC('month', NOW()))::INTEGER as month_referrals,
    COALESCE(SUM(r.points_awarded) FILTER (WHERE r.created_at >= DATE_TRUNC('month', NOW())), 0)::INTEGER as month_points
  INTO user_stats
  FROM profiles p
  LEFT JOIN referrals r ON p.id = r.referrer_id
  WHERE p.id = user_id_input
  GROUP BY p.id, p.total_referrals, p.referral_points;
  
  -- Get user rank
  SELECT COUNT(*) + 1 INTO user_rank
  FROM profiles
  WHERE referral_points > user_stats.referral_points;
  
  -- Determine next milestone
  SELECT milestone INTO next_milestone_level
  FROM (VALUES (5), (10), (25), (50), (100), (250), (500)) AS milestones(milestone)
  WHERE milestone > user_stats.total_referrals
  ORDER BY milestone
  LIMIT 1;
  
  RETURN QUERY
  SELECT 
    user_stats.total_referrals,
    user_stats.successful,
    user_stats.pending,
    user_stats.referral_points,
    CASE 
      WHEN user_stats.total_referrals > 0 THEN
        (user_stats.successful * 100.0 / user_stats.total_referrals)::DECIMAL(5,2)
      ELSE 0.0
    END,
    CASE 
      WHEN user_stats.successful > 0 THEN
        (user_stats.referral_points::DECIMAL / user_stats.successful)::DECIMAL(5,2)
      ELSE 0.0
    END,
    user_stats.month_referrals,
    user_stats.month_points,
    user_rank,
    COALESCE(next_milestone_level, 1000),
    CASE 
      WHEN next_milestone_level IS NOT NULL THEN
        GREATEST(0, next_milestone_level - user_stats.total_referrals)
      ELSE 0
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create referral share links
CREATE OR REPLACE FUNCTION public.create_referral_share_link(
  user_id_input UUID,
  platform VARCHAR(50),
  campaign VARCHAR(100) DEFAULT NULL
)
RETURNS TABLE (
  share_url TEXT,
  tracking_code TEXT
) AS $$
DECLARE
  user_referral_code TEXT;
  tracking_code_generated TEXT;
  base_url TEXT;
BEGIN
  -- Get user's referral code
  SELECT referral_code INTO user_referral_code
  FROM profiles
  WHERE id = user_id_input;
  
  IF user_referral_code IS NULL THEN
    RAISE EXCEPTION 'User does not have a referral code';
  END IF;
  
  -- Generate tracking code
  tracking_code_generated := encode(gen_random_bytes(8), 'hex');
  
  -- Build share URL based on platform
  base_url := 'https://heey.com/join?ref=' || user_referral_code;
  
  CASE platform
    WHEN 'twitter' THEN
      base_url := base_url || '&utm_source=twitter&utm_medium=social&utm_campaign=' || COALESCE(campaign, 'referral');
    WHEN 'facebook' THEN
      base_url := base_url || '&utm_source=facebook&utm_medium=social&utm_campaign=' || COALESCE(campaign, 'referral');
    WHEN 'instagram' THEN
      base_url := base_url || '&utm_source=instagram&utm_medium=social&utm_campaign=' || COALESCE(campaign, 'referral');
    WHEN 'tiktok' THEN
      base_url := base_url || '&utm_source=tiktok&utm_medium=social&utm_campaign=' || COALESCE(campaign, 'referral');
    WHEN 'email' THEN
      base_url := base_url || '&utm_source=email&utm_medium=email&utm_campaign=' || COALESCE(campaign, 'referral');
    WHEN 'direct' THEN
      base_url := base_url || '&utm_source=direct&utm_medium=direct&utm_campaign=' || COALESCE(campaign, 'referral');
    ELSE
      base_url := base_url || '&utm_source=' || platform || '&utm_medium=social&utm_campaign=' || COALESCE(campaign, 'referral');
  END CASE;
  
  -- Add tracking code
  base_url := base_url || '&tc=' || tracking_code_generated;
  
  -- Log the share link creation for analytics
  INSERT INTO page_analytics (
    profile_id,
    interaction_type,
    interaction_data
  )
  VALUES (
    user_id_input,
    'referral_link_created',
    jsonb_build_object(
      'platform', platform,
      'campaign', campaign,
      'tracking_code', tracking_code_generated
    )
  );
  
  RETURN QUERY
  SELECT base_url, tracking_code_generated;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to track referral link clicks
CREATE OR REPLACE FUNCTION public.track_referral_click(
  referral_code_input TEXT,
  tracking_code_input TEXT DEFAULT NULL,
  visitor_ip VARCHAR(45) DEFAULT NULL,
  user_agent TEXT DEFAULT NULL,
  utm_source TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  referrer_user_id UUID;
BEGIN
  -- Find the referrer
  SELECT id INTO referrer_user_id 
  FROM profiles 
  WHERE referral_code = referral_code_input;
  
  IF referrer_user_id IS NULL THEN
    RETURN FALSE; -- Invalid referral code
  END IF;
  
  -- Log the click
  INSERT INTO page_analytics (
    profile_id,
    visitor_ip,
    interaction_type,
    interaction_data,
    user_agent
  )
  VALUES (
    referrer_user_id,
    visitor_ip,
    'referral_click',
    jsonb_build_object(
      'referral_code', referral_code_input,
      'tracking_code', tracking_code_input,
      'utm_source', utm_source
    ),
    user_agent
  );
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get referral performance analytics
CREATE OR REPLACE FUNCTION public.get_referral_performance(
  user_id_input UUID,
  days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
  total_clicks INTEGER,
  total_signups INTEGER,
  conversion_rate DECIMAL(5,2),
  clicks_by_source JSONB,
  signups_by_source JSONB,
  daily_performance JSONB
) AS $$
DECLARE
  start_date TIMESTAMPTZ;
BEGIN
  start_date := NOW() - (days_back || ' days')::INTERVAL;
  
  RETURN QUERY
  WITH click_data AS (
    SELECT 
      COUNT(*)::INTEGER as total_clicks,
      jsonb_object_agg(
        COALESCE(pa.interaction_data->>'utm_source', 'direct'),
        COUNT(*)
      ) as clicks_by_source
    FROM page_analytics pa
    WHERE pa.profile_id = user_id_input
      AND pa.interaction_type = 'referral_click'
      AND pa.created_at >= start_date
  ),
  signup_data AS (
    SELECT 
      COUNT(*)::INTEGER as total_signups,
      jsonb_object_agg(
        COALESCE(r.metadata->>'utm_source', 'direct'),
        COUNT(*)
      ) as signups_by_source
    FROM referrals r
    WHERE r.referrer_id = user_id_input
      AND r.status = 'completed'
      AND r.created_at >= start_date
  ),
  daily_data AS (
    SELECT jsonb_object_agg(
      TO_CHAR(date_day, 'YYYY-MM-DD'),
      jsonb_build_object(
        'clicks', clicks_count,
        'signups', signups_count
      )
    ) as daily_performance
    FROM (
      SELECT 
        DATE_TRUNC('day', days.day) as date_day,
        COALESCE(clicks.click_count, 0) as clicks_count,
        COALESCE(signups.signup_count, 0) as signups_count
      FROM generate_series(start_date, NOW(), '1 day'::interval) as days(day)
      LEFT JOIN (
        SELECT 
          DATE_TRUNC('day', pa.created_at) as click_date,
          COUNT(*)::INTEGER as click_count
        FROM page_analytics pa
        WHERE pa.profile_id = user_id_input
          AND pa.interaction_type = 'referral_click'
          AND pa.created_at >= start_date
        GROUP BY DATE_TRUNC('day', pa.created_at)
      ) clicks ON days.day = clicks.click_date
      LEFT JOIN (
        SELECT 
          DATE_TRUNC('day', r.created_at) as signup_date,
          COUNT(*)::INTEGER as signup_count
        FROM referrals r
        WHERE r.referrer_id = user_id_input
          AND r.status = 'completed'
          AND r.created_at >= start_date
        GROUP BY DATE_TRUNC('day', r.created_at)
      ) signups ON days.day = signups.signup_date
    ) daily_stats
  )
  SELECT 
    cd.total_clicks,
    sd.total_signups,
    CASE 
      WHEN cd.total_clicks > 0 THEN
        (sd.total_signups * 100.0 / cd.total_clicks)::DECIMAL(5,2)
      ELSE 0.0
    END,
    cd.clicks_by_source,
    sd.signups_by_source,
    dd.daily_performance
  FROM click_data cd
  CROSS JOIN signup_data sd
  CROSS JOIN daily_data dd;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to process milestones when referrals are updated
CREATE OR REPLACE FUNCTION public.trigger_milestone_processing()
RETURNS TRIGGER AS $$
BEGIN
  -- Only process if referral was completed
  IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
    PERFORM public.process_referral_milestones(NEW.referrer_id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER referral_milestone_trigger
  AFTER INSERT OR UPDATE ON public.referrals
  FOR EACH ROW
  EXECUTE FUNCTION public.trigger_milestone_processing();

-- Create indexes for referral performance
CREATE INDEX IF NOT EXISTS idx_referrals_created_status ON public.referrals(created_at, status);
CREATE INDEX IF NOT EXISTS idx_referral_notifications_processed ON public.referral_notifications(is_processed, created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_user_type ON public.notifications(user_id, type);
CREATE INDEX IF NOT EXISTS idx_notifications_important ON public.notifications(is_important, created_at);

-- Create composite index for analytics queries
CREATE INDEX IF NOT EXISTS idx_page_analytics_profile_interaction_date ON public.page_analytics(profile_id, interaction_type, created_at);