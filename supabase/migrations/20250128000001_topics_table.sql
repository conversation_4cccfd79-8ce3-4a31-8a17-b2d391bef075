-- Topics table used by chat topic extraction/analytics
CREATE TABLE IF NOT EXISTS public.topics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  topic TEXT NOT NULL,
  sentiment TEXT CHECK (sentiment IN ('positive','neutral','negative')),
  confidence REAL DEFAULT 0,
  keywords TEXT[] DEFAULT '{}',
  summary TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

ALTER TABLE public.topics ENABLE ROW LEVEL SECURITY;

-- Policies: owner read/write
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'topics' AND policyname = 'Owner read topics'
  ) THEN
    CREATE POLICY "Owner read topics" ON public.topics FOR SELECT USING (auth.uid() = user_id);
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'topics' AND policyname = 'Owner write topics'
  ) THEN
    CREATE POLICY "Owner write topics" ON public.topics FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;
END $$;


