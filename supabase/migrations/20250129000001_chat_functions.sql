-- Chat System Enhancement Functions
-- Adds advanced chat functionality including AI integration and analytics

-- Function to create a new chat session
CREATE OR REPLACE FUNCTION public.create_chat_session(
  profile_owner_id UUID,
  visitor_display_name TEXT DEFAULT NULL,
  visitor_email TEXT DEFAULT NULL,
  visitor_avatar_url TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  visitor_record_id UUID;
  chat_session_id UUID;
  session_token TEXT;
BEGIN
  -- Generate session token
  session_token := encode(gen_random_bytes(32), 'hex');
  
  -- Create or update visitor record
  INSERT INTO visitors (
    profile_owner_id,
    display_name,
    email,
    avatar_url,
    first_visit_at,
    last_visit_at,
    total_visits
  )
  VALUES (
    profile_owner_id,
    visitor_display_name,
    visitor_email,
    visitor_avatar_url,
    NOW(),
    NOW(),
    1
  )
  ON CONFLICT (profile_owner_id, email) 
  DO UPDATE SET
    last_visit_at = NOW(),
    total_visits = visitors.total_visits + 1,
    display_name = COALESCE(EXCLUDED.display_name, visitors.display_name),
    avatar_url = COALESCE(EXCLUDED.avatar_url, visitors.avatar_url)
  RETURNING id INTO visitor_record_id;
  
  -- Create chat session
  INSERT INTO chats (
    user_id,
    visitor_id,
    session_id,
    status,
    started_at
  )
  VALUES (
    profile_owner_id,
    visitor_record_id,
    session_token,
    'active',
    NOW()
  )
  RETURNING id INTO chat_session_id;
  
  RETURN chat_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to send a chat message
CREATE OR REPLACE FUNCTION public.send_chat_message(
  chat_session_id UUID,
  sender_type_input TEXT,
  sender_id_input UUID,
  content_input TEXT,
  message_type_input TEXT DEFAULT 'text'
)
RETURNS UUID AS $$
DECLARE
  message_id UUID;
BEGIN
  -- Validate sender type
  IF sender_type_input NOT IN ('user', 'visitor', 'ai') THEN
    RAISE EXCEPTION 'Invalid sender type: %', sender_type_input;
  END IF;
  
  -- Insert message
  INSERT INTO chat_messages (
    chat_id,
    sender_type,
    sender_id,
    content,
    message_type,
    created_at
  )
  VALUES (
    chat_session_id,
    sender_type_input,
    sender_id_input,
    content_input,
    message_type_input,
    NOW()
  )
  RETURNING id INTO message_id;
  
  -- Update chat session message count
  UPDATE chats 
  SET total_messages = total_messages + 1
  WHERE id = chat_session_id;
  
  RETURN message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to end a chat session
CREATE OR REPLACE FUNCTION public.end_chat_session(
  chat_session_id UUID,
  user_satisfaction_score INTEGER DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
  chat_record RECORD;
  duration_minutes INTEGER;
  message_count INTEGER;
BEGIN
  -- Get chat details
  SELECT c.*, COUNT(cm.id) as msg_count
  INTO chat_record
  FROM chats c
  LEFT JOIN chat_messages cm ON c.id = cm.chat_id
  WHERE c.id = chat_session_id
  GROUP BY c.id, c.user_id, c.visitor_id, c.session_id, c.status, c.started_at, c.ended_at, c.total_messages, c.metadata, c.created_at;
  
  IF chat_record.id IS NULL THEN
    RAISE EXCEPTION 'Chat session not found: %', chat_session_id;
  END IF;
  
  -- Calculate duration
  duration_minutes := EXTRACT(EPOCH FROM (NOW() - chat_record.started_at)) / 60;
  
  -- Update chat session
  UPDATE chats 
  SET 
    status = 'archived',
    ended_at = NOW()
  WHERE id = chat_session_id;
  
  -- Create chat analytics record
  INSERT INTO chat_analytics (
    chat_id,
    user_id,
    message_count,
    conversation_duration_minutes,
    user_satisfaction,
    created_at
  )
  VALUES (
    chat_session_id,
    chat_record.user_id,
    chat_record.msg_count,
    duration_minutes,
    user_satisfaction_score,
    NOW()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get chat history for a user
CREATE OR REPLACE FUNCTION public.get_user_chat_history(
  user_id_input UUID,
  limit_count INTEGER DEFAULT 10,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  chat_id UUID,
  visitor_name TEXT,
  visitor_email TEXT,
  message_count INTEGER,
  last_message_at TIMESTAMPTZ,
  chat_status TEXT,
  duration_minutes INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    v.display_name,
    v.email,
    c.total_messages,
    (
      SELECT MAX(cm.created_at)
      FROM chat_messages cm
      WHERE cm.chat_id = c.id
    ),
    c.status,
    CASE 
      WHEN c.ended_at IS NOT NULL THEN
        EXTRACT(EPOCH FROM (c.ended_at - c.started_at))::INTEGER / 60
      ELSE
        EXTRACT(EPOCH FROM (NOW() - c.started_at))::INTEGER / 60
    END
  FROM chats c
  JOIN visitors v ON c.visitor_id = v.id
  WHERE c.user_id = user_id_input
  ORDER BY c.created_at DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get messages for a specific chat
CREATE OR REPLACE FUNCTION public.get_chat_messages(
  chat_session_id UUID,
  limit_count INTEGER DEFAULT 50,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  message_id UUID,
  sender_type TEXT,
  sender_id UUID,
  content TEXT,
  message_type TEXT,
  created_at TIMESTAMPTZ,
  sender_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cm.id,
    cm.sender_type,
    cm.sender_id,
    cm.content,
    cm.message_type,
    cm.created_at,
    CASE 
      WHEN cm.sender_type = 'user' THEN 
        (SELECT display_name FROM profiles WHERE id = cm.sender_id)
      WHEN cm.sender_type = 'visitor' THEN 
        (SELECT display_name FROM visitors WHERE id = cm.sender_id)
      WHEN cm.sender_type = 'ai' THEN 
        'AI Assistant'
      ELSE 
        'Unknown'
    END
  FROM chat_messages cm
  WHERE cm.chat_id = chat_session_id
  ORDER BY cm.created_at ASC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get active chat sessions for a user
CREATE OR REPLACE FUNCTION public.get_active_chats(user_id_input UUID)
RETURNS TABLE (
  chat_id UUID,
  visitor_name TEXT,
  visitor_avatar TEXT,
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  unread_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    v.display_name,
    v.avatar_url,
    (
      SELECT cm.content
      FROM chat_messages cm
      WHERE cm.chat_id = c.id
      ORDER BY cm.created_at DESC
      LIMIT 1
    ),
    (
      SELECT cm.created_at
      FROM chat_messages cm
      WHERE cm.chat_id = c.id
      ORDER BY cm.created_at DESC
      LIMIT 1
    ),
    0 -- Placeholder for unread count logic
  FROM chats c
  JOIN visitors v ON c.visitor_id = v.id
  WHERE c.user_id = user_id_input AND c.status = 'active'
  ORDER BY (
    SELECT MAX(cm.created_at)
    FROM chat_messages cm
    WHERE cm.chat_id = c.id
  ) DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update AI personality settings
CREATE OR REPLACE FUNCTION public.update_ai_personality(
  user_id_input UUID,
  personality_data JSONB
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO user_settings (user_id, ai_personality)
  VALUES (user_id_input, personality_data)
  ON CONFLICT (user_id)
  DO UPDATE SET
    ai_personality = personality_data,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get AI personality settings
CREATE OR REPLACE FUNCTION public.get_ai_personality(user_id_input UUID)
RETURNS JSONB AS $$
DECLARE
  personality JSONB;
BEGIN
  SELECT ai_personality INTO personality
  FROM user_settings
  WHERE user_id = user_id_input;
  
  -- Return default if not found
  IF personality IS NULL THEN
    personality := '{
      "tone": "friendly",
      "style": "conversational",
      "personality": "helpful",
      "response_length": "medium",
      "knowledge_areas": ["general"],
      "custom_instructions": ""
    }'::jsonb;
  END IF;
  
  RETURN personality;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log chat interaction for analytics
CREATE OR REPLACE FUNCTION public.log_chat_interaction(
  profile_id_input UUID,
  interaction_type_input VARCHAR(50),
  interaction_data_input JSONB DEFAULT '{}'
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO page_analytics (
    profile_id,
    interaction_type,
    interaction_data,
    created_at
  )
  VALUES (
    profile_id_input,
    interaction_type_input,
    interaction_data_input,
    NOW()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for chat performance
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON public.chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sender_type ON public.chat_messages(sender_type);
CREATE INDEX IF NOT EXISTS idx_chats_status ON public.chats(status);
CREATE INDEX IF NOT EXISTS idx_chats_started_at ON public.chats(started_at);
CREATE INDEX IF NOT EXISTS idx_visitors_last_visit ON public.visitors(last_visit_at);
CREATE INDEX IF NOT EXISTS idx_visitors_email ON public.visitors(email);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_chats_user_status ON public.chats(user_id, status);
CREATE INDEX IF NOT EXISTS idx_chat_messages_chat_created ON public.chat_messages(chat_id, created_at);
CREATE INDEX IF NOT EXISTS idx_visitors_owner_email ON public.visitors(profile_owner_id, email);